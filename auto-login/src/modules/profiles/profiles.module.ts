import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ProfilesController } from './profiles.controller';
import { ProfilesService } from './profiles.service';
import { ProfileShare } from './entities/profile-share.entity';
import { ProfileAccessLog } from './entities/profile-access-log.entity';
import { ProfileGroup } from './entities/profile-group.entity';
import { Profile } from './entities/profile.entity';
import { UserProfileGroupAccess } from './entities/user-profile-group-access.entity';
import { Account } from '../accounts/entities/account.entity';
import { User } from '../users/entities/user.entity';
import { Package } from '../packages/entities/package.entity';
import { PackageUser } from '../packages/entities/package-user.entity';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ProfileShare,
      ProfileAccessLog,
      ProfileGroup,
      Profile,
      UserProfileGroupAccess,
      Account,
      User,
      Package,
      PackageUser,
    ]),
    JwtModule.registerAsync({
      useFactory: (configService: ConfigService) => ({
        secret: configService.get('jwt.secret'),
        signOptions: { expiresIn: configService.get('jwt.expiresIn') },
      }),
      inject: [ConfigService],
    }),
    ConfigModule,
    AuthModule,
  ],
  controllers: [ProfilesController],
  providers: [ProfilesService],
  exports: [ProfilesService],
})
export class ProfilesModule {}
