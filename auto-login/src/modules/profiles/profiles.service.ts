import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { ProfileShare } from './entities/profile-share.entity';
import { ProfileAccessLog } from './entities/profile-access-log.entity';
import { ProfileGroup } from './entities/profile-group.entity';
import { Profile } from './entities/profile.entity';
import { UserProfileGroupAccess } from './entities/user-profile-group-access.entity';
import { Account } from '../accounts/entities/account.entity';
import { User } from '../users/entities/user.entity';
import { Package } from '../packages/entities/package.entity';
import { PackageUser } from '../packages/entities/package-user.entity';
import { CreateProfileShareDto } from './dto/create-profile-share.dto';
import { UpdateProfileShareDto } from './dto/update-profile-share.dto';
import { CreateProfileGroupDto } from './dto/create-profile-group.dto';
import { UpdateProfileGroupDto } from './dto/update-profile-group.dto';
import { CreateProfileDto } from './dto/create-profile.dto';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { CreateUserProfileGroupAccessDto } from './dto/create-user-profile-group-access.dto';
import { UpdateUserProfileGroupAccessDto } from './dto/update-user-profile-group-access.dto';

@Injectable()
export class ProfilesService {
  constructor(
    @InjectRepository(ProfileShare)
    private profileShareRepository: Repository<ProfileShare>,
    @InjectRepository(ProfileAccessLog)
    private profileAccessLogRepository: Repository<ProfileAccessLog>,
    @InjectRepository(ProfileGroup)
    private profileGroupRepository: Repository<ProfileGroup>,
    @InjectRepository(Profile)
    private profileRepository: Repository<Profile>,
    @InjectRepository(UserProfileGroupAccess)
    private userProfileGroupAccessRepository: Repository<UserProfileGroupAccess>,
    @InjectRepository(Account)
    private accountRepository: Repository<Account>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Package)
    private packageRepository: Repository<Package>,
    @InjectRepository(PackageUser)
    private packageUserRepository: Repository<PackageUser>,
  ) {}

  async shareProfile(
    createProfileShareDto: CreateProfileShareDto,
    adminUserId: number,
  ): Promise<ProfileShare> {
    // Verify admin has permission to share profiles
    const admin = await this.userRepository.findOne({
      where: { id: adminUserId },
    });

    if (!admin || admin.role !== 'admin') {
      throw new ForbiddenException('Only admins can share profiles');
    }

    // Verify account exists
    const account = await this.accountRepository.findOne({
      where: { id: createProfileShareDto.account_id },
      relations: ['packages'],
    });

    if (!account) {
      throw new NotFoundException('Account not found');
    }

    // Verify target user exists and has appropriate package
    const targetUser = await this.userRepository.findOne({
      where: { id: createProfileShareDto.shared_with_user_id },
      relations: ['packageUsers', 'packageUsers.package'],
    });

    if (!targetUser) {
      throw new NotFoundException('Target user not found');
    }

    // Check if user has packages that include the account
    const userPackageIds = targetUser.packageUsers.map((pu) => pu.package_id);
    const accountPackageIds = account.packages.map((p) => p.id);

    const hasMatchingPackage = userPackageIds.some((upId) =>
      accountPackageIds.includes(upId),
    );

    if (!hasMatchingPackage) {
      throw new BadRequestException(
        'User does not have a package that includes this account',
      );
    }

    // Check if profile is already shared with this user
    const existingShare = await this.profileShareRepository.findOne({
      where: {
        account_id: createProfileShareDto.account_id,
        shared_with_user_id: createProfileShareDto.shared_with_user_id,
        status: 'active',
      },
    });

    if (existingShare) {
      throw new BadRequestException('Profile is already shared with this user');
    }

    // Create profile share
    const profileShare = this.profileShareRepository.create({
      ...createProfileShareDto,
      shared_by_admin_id: adminUserId,
      permissions: createProfileShareDto.permissions || {
        can_modify_profile: false,
        can_view_history: true,
        can_export_data: false,
        access_level: 'read',
      },
    });

    const savedShare = await this.profileShareRepository.save(profileShare);

    // Log the sharing action
    await this.logProfileAccess(
      adminUserId,
      createProfileShareDto.account_id,
      'share',
      {
        shared_with_user_id: createProfileShareDto.shared_with_user_id,
        permissions: profileShare.permissions,
      },
    );

    return savedShare;
  }

  async updateProfileShare(
    shareId: number,
    updateProfileShareDto: UpdateProfileShareDto,
    adminUserId: number,
  ): Promise<ProfileShare> {
    // Verify admin permission
    const admin = await this.userRepository.findOne({
      where: { id: adminUserId },
    });

    if (!admin || admin.role !== 'admin') {
      throw new ForbiddenException('Only admins can update profile shares');
    }

    const profileShare = await this.profileShareRepository.findOne({
      where: { id: shareId },
    });

    if (!profileShare) {
      throw new NotFoundException('Profile share not found');
    }

    // Update the share
    Object.assign(profileShare, updateProfileShareDto);
    const updatedShare = await this.profileShareRepository.save(profileShare);

    // Log the update action
    await this.logProfileAccess(
      adminUserId,
      profileShare.account_id,
      'modify',
      {
        share_id: shareId,
        changes: updateProfileShareDto,
      },
    );

    return updatedShare;
  }

  async revokeProfileShare(
    shareId: number,
    adminUserId: number,
  ): Promise<void> {
    // Verify admin permission
    const admin = await this.userRepository.findOne({
      where: { id: adminUserId },
    });

    if (!admin || admin.role !== 'admin') {
      throw new ForbiddenException('Only admins can revoke profile shares');
    }

    const profileShare = await this.profileShareRepository.findOne({
      where: { id: shareId },
    });

    if (!profileShare) {
      throw new NotFoundException('Profile share not found');
    }

    // Update status to revoked
    profileShare.status = 'revoked';
    await this.profileShareRepository.save(profileShare);

    // Log the revocation
    await this.logProfileAccess(
      adminUserId,
      profileShare.account_id,
      'revoke',
      {
        share_id: shareId,
        revoked_from_user_id: profileShare.shared_with_user_id,
      },
    );
  }

  async getUserSharedProfiles(userId: number): Promise<ProfileShare[]> {
    return this.profileShareRepository.find({
      where: {
        shared_with_user_id: userId,
        status: 'active',
      },
      relations: ['account', 'sharedByAdmin'],
    });
  }

  async getAccountShares(accountId: number): Promise<ProfileShare[]> {
    return this.profileShareRepository.find({
      where: { account_id: accountId },
      relations: ['sharedWithUser', 'sharedByAdmin'],
      order: { created_at: 'DESC' },
    });
  }

  async getUserAccessibleAccounts(userId: number): Promise<Account[]> {
    // Get user's packages
    const userPackages = await this.packageUserRepository.find({
      where: { user_id: userId },
      relations: ['package', 'package.accounts'],
    });

    // Get accounts from packages
    const packageAccounts = userPackages.flatMap(
      (up) => up.package.accounts || [],
    );

    // Get directly shared accounts
    const sharedProfiles = await this.getUserSharedProfiles(userId);
    const sharedAccounts = await this.accountRepository.findByIds(
      sharedProfiles.map((sp) => sp.account_id),
    );

    // Combine and deduplicate
    const allAccounts = [...packageAccounts, ...sharedAccounts];
    const uniqueAccounts = allAccounts.filter(
      (account, index, self) =>
        index === self.findIndex((a) => a.id === account.id),
    );

    return uniqueAccounts;
  }

  async checkUserProfileAccess(
    userId: number,
    accountId: number,
  ): Promise<{
    hasAccess: boolean;
    accessType: 'package' | 'shared' | 'none';
    permissions?: any;
  }> {
    // Check package-based access
    const userPackages = await this.packageUserRepository.find({
      where: { user_id: userId },
      relations: ['package', 'package.accounts'],
    });

    const hasPackageAccess = userPackages.some((up) =>
      up.package.accounts?.some((account) => account.id === accountId),
    );

    if (hasPackageAccess) {
      return {
        hasAccess: true,
        accessType: 'package',
        permissions: {
          can_modify_profile: true,
          can_view_history: true,
          can_export_data: true,
          access_level: 'full',
        },
      };
    }

    // Check shared access
    const sharedProfile = await this.profileShareRepository.findOne({
      where: {
        shared_with_user_id: userId,
        account_id: accountId,
        status: 'active',
      },
    });

    if (sharedProfile) {
      // Check if share has expired
      if (sharedProfile.expires_at && new Date() > sharedProfile.expires_at) {
        return { hasAccess: false, accessType: 'none' };
      }

      return {
        hasAccess: true,
        accessType: 'shared',
        permissions: sharedProfile.permissions,
      };
    }

    return { hasAccess: false, accessType: 'none' };
  }

  async logProfileAccess(
    userId: number,
    accountId: number,
    action: string,
    metadata?: any,
  ): Promise<void> {
    const accessLog = this.profileAccessLogRepository.create({
      user_id: userId,
      account_id: accountId,
      action,
      metadata,
    });

    await this.profileAccessLogRepository.save(accessLog);
  }

  async getProfileAccessLogs(
    accountId: number,
    limit: number = 100,
  ): Promise<ProfileAccessLog[]> {
    return this.profileAccessLogRepository.find({
      where: { account_id: accountId },
      relations: ['user'],
      order: { created_at: 'DESC' },
      take: limit,
    });
  }

  async getProfileShareStatistics(): Promise<{
    totalShares: number;
    activeShares: number;
    revokedShares: number;
    expiredShares: number;
    sharesByUser: { userId: number; email: string; shareCount: number }[];
  }> {
    const allShares = await this.profileShareRepository.find({
      relations: ['sharedWithUser'],
    });

    const now = new Date();
    const activeShares = allShares.filter(
      (share) =>
        share.status === 'active' &&
        (!share.expires_at || share.expires_at > now),
    );
    const revokedShares = allShares.filter(
      (share) => share.status === 'revoked',
    );
    const expiredShares = allShares.filter(
      (share) => share.expires_at && share.expires_at <= now,
    );

    // Group by user
    const sharesByUser = allShares.reduce(
      (acc, share) => {
        const userId = share.shared_with_user_id;
        const existing = acc.find((item) => item.userId === userId);

        if (existing) {
          existing.shareCount++;
        } else {
          acc.push({
            userId,
            email: share.sharedWithUser?.email || 'Unknown',
            shareCount: 1,
          });
        }

        return acc;
      },
      [] as { userId: number; email: string; shareCount: number }[],
    );

    return {
      totalShares: allShares.length,
      activeShares: activeShares.length,
      revokedShares: revokedShares.length,
      expiredShares: expiredShares.length,
      sharesByUser: sharesByUser.sort((a, b) => b.shareCount - a.shareCount),
    };
  }

  // ProfileGroup Management Methods
  async createProfileGroup(
    createProfileGroupDto: CreateProfileGroupDto,
    adminUserId: number,
  ): Promise<ProfileGroup> {
    // Verify admin permission
    const admin = await this.userRepository.findOne({
      where: { id: adminUserId },
    });

    if (!admin || admin.role !== 'admin') {
      throw new ForbiddenException('Only admins can create profile groups');
    }

    // Check if profile group name already exists
    const existingGroup = await this.profileGroupRepository.findOne({
      where: { name: createProfileGroupDto.name },
    });

    if (existingGroup) {
      throw new BadRequestException('Profile group name already exists');
    }

    const profileGroup = this.profileGroupRepository.create({
      ...createProfileGroupDto,
      created_by_admin_id: adminUserId,
    });

    return this.profileGroupRepository.save(profileGroup);
  }

  async updateProfileGroup(
    groupId: number,
    updateProfileGroupDto: UpdateProfileGroupDto,
    adminUserId: number,
  ): Promise<ProfileGroup> {
    // Verify admin permission
    const admin = await this.userRepository.findOne({
      where: { id: adminUserId },
    });

    if (!admin || admin.role !== 'admin') {
      throw new ForbiddenException('Only admins can update profile groups');
    }

    const profileGroup = await this.profileGroupRepository.findOne({
      where: { id: groupId },
    });

    if (!profileGroup) {
      throw new NotFoundException('Profile group not found');
    }

    // Check name uniqueness if name is being updated
    if (
      updateProfileGroupDto.name &&
      updateProfileGroupDto.name !== profileGroup.name
    ) {
      const existingGroup = await this.profileGroupRepository.findOne({
        where: { name: updateProfileGroupDto.name },
      });

      if (existingGroup) {
        throw new BadRequestException('Profile group name already exists');
      }
    }

    Object.assign(profileGroup, updateProfileGroupDto);
    return this.profileGroupRepository.save(profileGroup);
  }

  async deleteProfileGroup(
    groupId: number,
    adminUserId: number,
  ): Promise<void> {
    // Verify admin permission
    const admin = await this.userRepository.findOne({
      where: { id: adminUserId },
    });

    if (!admin || admin.role !== 'admin') {
      throw new ForbiddenException('Only admins can delete profile groups');
    }

    const profileGroup = await this.profileGroupRepository.findOne({
      where: { id: groupId },
      relations: ['profiles', 'userAccesses'],
    });

    if (!profileGroup) {
      throw new NotFoundException('Profile group not found');
    }

    // Check if group has profiles or user accesses
    if (profileGroup.profiles.length > 0) {
      throw new BadRequestException(
        'Cannot delete profile group that contains profiles',
      );
    }

    if (profileGroup.userAccesses.length > 0) {
      throw new BadRequestException(
        'Cannot delete profile group that has user accesses',
      );
    }

    await this.profileGroupRepository.remove(profileGroup);
  }

  async getAllProfileGroups(): Promise<ProfileGroup[]> {
    return this.profileGroupRepository.find({
      relations: ['createdByAdmin', 'profiles', 'userAccesses'],
      order: { created_at: 'DESC' },
    });
  }

  async getProfileGroupById(groupId: number): Promise<ProfileGroup> {
    const profileGroup = await this.profileGroupRepository.findOne({
      where: { id: groupId },
      relations: [
        'createdByAdmin',
        'profiles',
        'userAccesses',
        'userAccesses.user',
      ],
    });

    if (!profileGroup) {
      throw new NotFoundException('Profile group not found');
    }

    return profileGroup;
  }

  // Profile Management Methods
  async createProfile(
    createProfileDto: CreateProfileDto,
    adminUserId: number,
  ): Promise<Profile> {
    // Verify admin permission
    const admin = await this.userRepository.findOne({
      where: { id: adminUserId },
    });

    if (!admin || admin.role !== 'admin') {
      throw new ForbiddenException('Only admins can create profiles');
    }

    // Verify account exists
    const account = await this.accountRepository.findOne({
      where: { id: createProfileDto.account_id },
    });

    if (!account) {
      throw new NotFoundException('Account not found');
    }

    // Verify profile group exists
    const profileGroup = await this.profileGroupRepository.findOne({
      where: { id: createProfileDto.profile_group_id },
    });

    if (!profileGroup) {
      throw new NotFoundException('Profile group not found');
    }

    // Check if profile name already exists in the group
    const existingProfile = await this.profileRepository.findOne({
      where: {
        name: createProfileDto.name,
        profile_group_id: createProfileDto.profile_group_id,
      },
    });

    if (existingProfile) {
      throw new BadRequestException(
        'Profile name already exists in this group',
      );
    }

    const profile = this.profileRepository.create({
      ...createProfileDto,
      created_by_admin_id: adminUserId,
    });

    return this.profileRepository.save(profile);
  }

  async updateProfile(
    profileId: number,
    updateProfileDto: UpdateProfileDto,
    adminUserId: number,
  ): Promise<Profile> {
    // Verify admin permission
    const admin = await this.userRepository.findOne({
      where: { id: adminUserId },
    });

    if (!admin || admin.role !== 'admin') {
      throw new ForbiddenException('Only admins can update profiles');
    }

    const profile = await this.profileRepository.findOne({
      where: { id: profileId },
    });

    if (!profile) {
      throw new NotFoundException('Profile not found');
    }

    // Check name uniqueness if name is being updated
    if (updateProfileDto.name && updateProfileDto.name !== profile.name) {
      const existingProfile = await this.profileRepository.findOne({
        where: {
          name: updateProfileDto.name,
          profile_group_id: profile.profile_group_id,
        },
      });

      if (existingProfile) {
        throw new BadRequestException(
          'Profile name already exists in this group',
        );
      }
    }

    Object.assign(profile, updateProfileDto);
    return this.profileRepository.save(profile);
  }

  async deleteProfile(profileId: number, adminUserId: number): Promise<void> {
    // Verify admin permission
    const admin = await this.userRepository.findOne({
      where: { id: adminUserId },
    });

    if (!admin || admin.role !== 'admin') {
      throw new ForbiddenException('Only admins can delete profiles');
    }

    const profile = await this.profileRepository.findOne({
      where: { id: profileId },
    });

    if (!profile) {
      throw new NotFoundException('Profile not found');
    }

    await this.profileRepository.remove(profile);
  }

  async getAllProfiles(): Promise<Profile[]> {
    return this.profileRepository.find({
      relations: ['account', 'profileGroup', 'createdByAdmin'],
      order: { created_at: 'DESC' },
    });
  }

  async getProfilesByGroup(groupId: number): Promise<Profile[]> {
    return this.profileRepository.find({
      where: { profile_group_id: groupId },
      relations: ['account', 'profileGroup', 'createdByAdmin'],
      order: { created_at: 'DESC' },
    });
  }

  async getProfileById(profileId: number): Promise<Profile> {
    const profile = await this.profileRepository.findOne({
      where: { id: profileId },
      relations: ['account', 'profileGroup', 'createdByAdmin'],
    });

    if (!profile) {
      throw new NotFoundException('Profile not found');
    }

    return profile;
  }

  // UserProfileGroupAccess Management Methods
  async grantUserProfileGroupAccess(
    createAccessDto: CreateUserProfileGroupAccessDto,
    adminUserId: number,
  ): Promise<UserProfileGroupAccess> {
    // Verify admin permission
    const admin = await this.userRepository.findOne({
      where: { id: adminUserId },
    });

    if (!admin || admin.role !== 'admin') {
      throw new ForbiddenException(
        'Only admins can grant profile group access',
      );
    }

    // Verify user exists and has active status
    const user = await this.userRepository.findOne({
      where: { id: createAccessDto.user_id },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (user.status !== 'active') {
      throw new BadRequestException('Can only grant access to active users');
    }

    // Verify profile group exists
    const profileGroup = await this.profileGroupRepository.findOne({
      where: { id: createAccessDto.profile_group_id },
    });

    if (!profileGroup) {
      throw new NotFoundException('Profile group not found');
    }

    // Check if access already exists
    const existingAccess = await this.userProfileGroupAccessRepository.findOne({
      where: {
        user_id: createAccessDto.user_id,
        profile_group_id: createAccessDto.profile_group_id,
        status: 'active',
      },
    });

    if (existingAccess) {
      throw new BadRequestException(
        'User already has access to this profile group',
      );
    }

    const access = this.userProfileGroupAccessRepository.create({
      ...createAccessDto,
      granted_by_admin_id: adminUserId,
      permissions: createAccessDto.permissions || {
        can_launch_browser: true,
        can_view_profiles: true,
        can_export_data: false,
        access_level: 'read',
      },
    });

    return this.userProfileGroupAccessRepository.save(access);
  }

  async updateUserProfileGroupAccess(
    accessId: number,
    updateAccessDto: UpdateUserProfileGroupAccessDto,
    adminUserId: number,
  ): Promise<UserProfileGroupAccess> {
    // Verify admin permission
    const admin = await this.userRepository.findOne({
      where: { id: adminUserId },
    });

    if (!admin || admin.role !== 'admin') {
      throw new ForbiddenException(
        'Only admins can update profile group access',
      );
    }

    const access = await this.userProfileGroupAccessRepository.findOne({
      where: { id: accessId },
    });

    if (!access) {
      throw new NotFoundException('Profile group access not found');
    }

    Object.assign(access, updateAccessDto);
    return this.userProfileGroupAccessRepository.save(access);
  }

  async revokeUserProfileGroupAccess(
    accessId: number,
    adminUserId: number,
  ): Promise<void> {
    // Verify admin permission
    const admin = await this.userRepository.findOne({
      where: { id: adminUserId },
    });

    if (!admin || admin.role !== 'admin') {
      throw new ForbiddenException(
        'Only admins can revoke profile group access',
      );
    }

    const access = await this.userProfileGroupAccessRepository.findOne({
      where: { id: accessId },
    });

    if (!access) {
      throw new NotFoundException('Profile group access not found');
    }

    access.status = 'revoked';
    await this.userProfileGroupAccessRepository.save(access);
  }

  async getAllUserProfileGroupAccesses(): Promise<UserProfileGroupAccess[]> {
    return this.userProfileGroupAccessRepository.find({
      relations: ['user', 'profileGroup', 'grantedByAdmin'],
      order: { granted_at: 'DESC' },
    });
  }

  async getUserProfileGroupAccesses(
    userId: number,
  ): Promise<UserProfileGroupAccess[]> {
    return this.userProfileGroupAccessRepository.find({
      where: { user_id: userId, status: 'active' },
      relations: ['profileGroup', 'grantedByAdmin'],
      order: { granted_at: 'DESC' },
    });
  }

  async getProfileGroupAccesses(
    groupId: number,
  ): Promise<UserProfileGroupAccess[]> {
    return this.userProfileGroupAccessRepository.find({
      where: { profile_group_id: groupId },
      relations: ['user', 'grantedByAdmin'],
      order: { granted_at: 'DESC' },
    });
  }

  async getUserAccessibleProfileGroups(
    userId: number,
  ): Promise<ProfileGroup[]> {
    const accesses = await this.getUserProfileGroupAccesses(userId);
    const groupIds = accesses.map((access) => access.profile_group_id);

    if (groupIds.length === 0) {
      return [];
    }

    return this.profileGroupRepository.find({
      where: { id: In(groupIds) },
      relations: ['profiles'],
    });
  }

  // Browser Launch and Profile Data Management Methods
  async launchBrowserForAccount(
    accountId: number,
    userId: number,
  ): Promise<any> {
    // Get account information
    const account = await this.accountRepository.findOne({
      where: { id: accountId },
    });

    if (!account) {
      throw new NotFoundException('Account not found');
    }

    try {
      // Get user information to create a token for Python backend
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });

      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Create a temporary token for Python backend call
      const jwt = require('jsonwebtoken');
      const tempToken = jwt.sign(
        {
          sub: userId,
          email: user.email,
          role: user.role,
          status: user.status,
        },
        process.env.JWT_SECRET || 'your-secret-key',
        { expiresIn: '1h' },
      );

      const axios = require('axios');

      // First, check if profile exists in FastAPI backend by account_id
      let fastApiProfileId = null;
      try {
        // Try to find profile by account_id
        const profilesResponse = await axios.get(
          `http://localhost:8000/api/profiles/`,
          {
            headers: {
              Authorization: `Bearer ${tempToken}`,
            },
            timeout: 10000,
          },
        );

        // Find profile with matching account_id
        const existingProfile = profilesResponse.data.find(
          (p) => p.account_id === accountId,
        );
        if (existingProfile) {
          fastApiProfileId = existingProfile.id;
          console.log(
            `✅ Found existing profile in FastAPI backend: ID ${fastApiProfileId} for account ${accountId}`,
          );
        } else {
          console.log(
            `⚠️ No profile found for account ${accountId} in FastAPI backend, creating...`,
          );

          // Generate unique profile name to avoid conflicts
          const timestamp = Date.now();
          const uniqueName = account.username
            ? `${account.username}_${accountId}_${timestamp}`
            : `Profile_${accountId}_${timestamp}`;

          // Create profile in FastAPI backend with unique name
          const createProfileResponse = await axios.post(
            `http://localhost:8000/api/profiles/`,
            {
              name: uniqueName,
              account_id: accountId,
              proxy_config: {
                type: 'no_proxy',
              },
            },
            {
              headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${tempToken}`,
              },
              timeout: 10000,
            },
          );
          fastApiProfileId = createProfileResponse.data.id;
          console.log(
            `✅ Profile created in FastAPI backend: ID ${fastApiProfileId} for account ${accountId} with name ${uniqueName}`,
          );
        }
      } catch (profileError) {
        console.error(
          '❌ Error managing profile in FastAPI backend:',
          profileError.response?.data || profileError.message,
        );

        // If it's a name conflict error, try with a different name
        if (
          profileError.response?.status === 400 &&
          profileError.response?.data?.detail?.includes('already exists')
        ) {
          console.log(
            '🔄 Profile name conflict detected, retrying with timestamp...',
          );
          try {
            const retryTimestamp = Date.now();
            const retryName = `Profile_${accountId}_retry_${retryTimestamp}`;

            const retryResponse = await axios.post(
              `http://localhost:8000/api/profiles/`,
              {
                name: retryName,
                account_id: accountId,
                proxy_config: {
                  type: 'no_proxy',
                },
              },
              {
                headers: {
                  'Content-Type': 'application/json',
                  Authorization: `Bearer ${tempToken}`,
                },
                timeout: 10000,
              },
            );
            fastApiProfileId = retryResponse.data.id;
            console.log(
              `✅ Profile created on retry: ID ${fastApiProfileId} for account ${accountId} with name ${retryName}`,
            );
          } catch (retryError) {
            console.error(
              '❌ Retry also failed:',
              retryError.response?.data || retryError.message,
            );
            throw new Error(
              `Failed to manage profile in FastAPI backend: ${retryError.response?.data?.detail || retryError.message}`,
            );
          }
        } else {
          throw new Error(
            `Failed to manage profile in FastAPI backend: ${profileError.response?.data?.detail || profileError.message}`,
          );
        }
      }

      // Now launch browser using the FastAPI profile ID
      console.log(
        `🚀 [Service] Launching browser for FastAPI profile ID: ${fastApiProfileId}`,
      );
      const response = await axios.post(
        `http://localhost:8000/api/profiles/${fastApiProfileId}/launch-browser`,
        {},
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${tempToken}`,
          },
          timeout: 30000,
        },
      );

      console.log(
        `✅ [Service] FastAPI launch response status: ${response.status}`,
      );
      console.log(`✅ [Service] FastAPI launch response data:`, response.data);

      // Update account sync status
      await this.accountRepository.update(accountId, {
        sync_status: 'browser_launched',
        last_sync: new Date(),
      });

      console.log(`✅ [Service] Account ${accountId} sync status updated`);
      return response.data;
    } catch (error) {
      console.error('❌ [Service] Browser launch error:', error);
      console.error('❌ [Service] Error details:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        stack: error.stack,
      });

      // Provide more specific error messages
      if (error.response?.status === 400) {
        throw new Error(
          `Profile configuration error: ${error.response.data?.detail || error.message}`,
        );
      } else if (error.response?.status === 404) {
        throw new Error(
          `Profile not found in backend: ${error.response.data?.detail || error.message}`,
        );
      } else if (error.code === 'ECONNREFUSED') {
        throw new Error(
          'Cannot connect to FastAPI backend. Please ensure it is running.',
        );
      } else {
        throw new Error(
          `Failed to launch browser: ${error.response?.data?.detail || error.message}`,
        );
      }
    }
  }

  async saveProfileData(
    accountId: number,
    profileData: {
      localStorage?: any;
      indexedDB?: any;
      history?: any;
      cookies?: any;
    },
    userId: number,
  ): Promise<any> {
    // Get account information
    const account = await this.accountRepository.findOne({
      where: { id: accountId },
    });

    if (!account) {
      throw new NotFoundException('Account not found');
    }

    try {
      // Save profile data to database
      // For now, we'll store it as JSON in the cookie_data field
      // In production, you might want a separate table for profile data
      const profileDataJson = JSON.stringify({
        localStorage: profileData.localStorage || {},
        indexedDB: profileData.indexedDB || {},
        history: profileData.history || [],
        cookies: profileData.cookies || [],
        savedAt: new Date(),
        savedBy: userId,
      });

      await this.accountRepository.update(accountId, {
        cookie_data: profileDataJson,
        sync_status: 'synced',
        last_sync: new Date(),
      });

      return {
        accountId,
        dataTypes: Object.keys(profileData),
        savedAt: new Date(),
        savedBy: userId,
      };
    } catch (error) {
      console.error('Save profile data error:', error);

      // Update sync status to error
      await this.accountRepository.update(accountId, {
        sync_status: 'error',
        last_sync: new Date(),
      });

      throw new Error(`Failed to save profile data: ${error.message}`);
    }
  }

  async getProfileData(accountId: number): Promise<any> {
    // Get account information
    const account = await this.accountRepository.findOne({
      where: { id: accountId },
    });

    if (!account) {
      throw new NotFoundException('Account not found');
    }

    try {
      // First try to load from compressed files
      const compressedData = await this.loadCompressedProfileData(accountId);
      if (compressedData) {
        return {
          accountId,
          syncStatus: account.sync_status,
          lastSync: account.last_sync,
          profileData: compressedData.profileData,
          metadata: compressedData.metadata,
          source: 'compressed_file',
        };
      }

      // Fallback to database data
      let profileData = {
        localStorage: {},
        indexedDB: {},
        history: [],
        cookies: [],
      };

      if (account.cookie_data) {
        try {
          const parsedData = JSON.parse(account.cookie_data);
          profileData = {
            localStorage: parsedData.localStorage || {},
            indexedDB: parsedData.indexedDB || {},
            history: parsedData.history || [],
            cookies: parsedData.cookies || [],
            ...(parsedData.savedAt && { savedAt: parsedData.savedAt }),
            ...(parsedData.savedBy && { savedBy: parsedData.savedBy }),
          };
        } catch (parseError) {
          console.warn('Failed to parse profile data, returning empty data');
        }
      }

      return {
        accountId,
        syncStatus: account.sync_status,
        lastSync: account.last_sync,
        profileData,
        source: 'database',
      };
    } catch (error) {
      console.error('Get profile data error:', error);
      throw new Error(`Failed to get profile data: ${error.message}`);
    }
  }

  private async loadCompressedProfileData(accountId: number): Promise<any> {
    const fs = require('fs').promises;
    const path = require('path');
    const zlib = require('zlib');
    const { promisify } = require('util');
    const gunzip = promisify(zlib.gunzip);

    try {
      // Build profile identifier based on account ID
      const profileId = `Profile_${accountId}_${Date.now()}_${Math.random().toString(36).substr(2, 8)}`;

      // Look for latest compressed file
      const compressedDir = path.join(process.cwd(), 'compressed');
      const files = await fs.readdir(compressedDir);

      console.log(`🔍 Looking for compressed data for account ${accountId}`);
      console.log(
        `📁 Available files:`,
        files.filter(
          (f) => f.includes(`${accountId}`) && f.endsWith('.json.gz'),
        ),
      );

      // Find the latest file for this profile/account
      // PRIORITY 1: Try real captured data with 'profile_' prefix (larger files)
      let profileFiles = files.filter(
        (file) =>
          file.includes(`profile_Profile_${accountId}_`) &&
          file.endsWith('_latest.json.gz'),
      );
      console.log(`🎯 Priority 1 - Real data files found:`, profileFiles);

      // PRIORITY 2: Try mock data pattern (smaller files)
      if (profileFiles.length === 0) {
        profileFiles = files.filter(
          (file) =>
            file.includes(`Profile_${accountId}_`) &&
            file.endsWith('_latest.json.gz'),
        );
        console.log(`🎯 Priority 2 - Mock data files found:`, profileFiles);
      }

      // PRIORITY 3: Try any file containing the account ID
      if (profileFiles.length === 0) {
        profileFiles = files.filter(
          (file) =>
            (file.includes(`_${accountId}_`) ||
              file.includes(`Profile_${accountId}`)) &&
            file.endsWith('_latest.json.gz'),
        );
      }

      if (profileFiles.length === 0) {
        console.log(
          `No compressed profile data found for account ${accountId}`,
        );
        console.log(
          `Available files:`,
          files.filter((f) => f.endsWith('.json.gz')),
        );
        return null;
      }

      // If multiple files found, prefer the larger one (real data vs mock data)
      let latestFile = profileFiles[0];
      if (profileFiles.length > 1) {
        const fs = require('fs');
        const path = require('path');

        // Get file sizes and prefer the larger one
        const fileSizes = profileFiles.map((file) => {
          try {
            const filePath = path.join(compressedDir, file);
            const stats = fs.statSync(filePath);
            return { file, size: stats.size };
          } catch (error) {
            return { file, size: 0 };
          }
        });

        // Sort by size descending (larger files first - real data)
        fileSizes.sort((a, b) => b.size - a.size);
        latestFile = fileSizes[0].file;

        console.log(
          `📁 Multiple files found for account ${accountId}, selected largest: ${latestFile} (${fileSizes[0].size} bytes)`,
        );
      } else {
        console.log(
          `📁 Found compressed file for account ${accountId}: ${latestFile}`,
        );
      }
      const metaFile = latestFile.replace('.json.gz', '.meta.json');

      const gzipPath = path.join(compressedDir, latestFile);
      const metaPath = path.join(compressedDir, metaFile);

      // Check if files exist
      try {
        await fs.access(gzipPath);
        await fs.access(metaPath);
      } catch (accessError) {
        console.log(`Compressed files not accessible for account ${accountId}`);
        return null;
      }

      // Read metadata
      const metaContent = await fs.readFile(metaPath, 'utf8');
      const metadata = JSON.parse(metaContent);

      // Read and decompress profile data
      const compressedData = await fs.readFile(gzipPath);
      const decompressedData = await gunzip(compressedData);
      const profileData = JSON.parse(decompressedData.toString());

      console.log(
        `✅ Loaded compressed profile data for account ${accountId}:`,
        {
          originalSize: metadata.original_size,
          compressedSize: metadata.compressed_size,
          compressionRatio: metadata.compression_ratio,
          dataTypes: metadata.data_types,
        },
      );

      return {
        profileData,
        metadata,
      };
    } catch (error) {
      console.error(
        `Failed to load compressed profile data for account ${accountId}:`,
        error,
      );
      return null;
    }
  }

  async launchBrowserWithProfileData(
    accountId: number,
    userId: number,
  ): Promise<any> {
    const axios = require('axios');

    try {
      console.log(
        `🚀 [Service] Launching browser with profile data for account ${accountId}`,
      );

      // Get account information
      const account = await this.accountRepository.findOne({
        where: { id: accountId },
      });

      if (!account) {
        throw new NotFoundException('Account not found');
      }

      // Load profile data (compressed or database)
      const profileDataResult = await this.getProfileData(accountId);

      console.log(
        `📊 [Service] Profile data loaded from ${profileDataResult.source}:`,
        {
          hasLocalStorage: !!profileDataResult.profileData?.localStorage,
          hasIndexedDB: !!profileDataResult.profileData?.indexedDB,
          hasCookies: !!profileDataResult.profileData?.cookies,
          hasHistory: !!profileDataResult.profileData?.history,
        },
      );

      console.log(
        `🔄 [Service] Sending view profile request to Python backend...`,
      );

      // Call Python backend using dedicated view profile endpoint
      const response = await axios.post(
        `http://localhost:8000/api/profiles/${accountId}/view-profile-data`,
        {
          headless: false,
          browser_data: profileDataResult.profileData,
        },
        {
          timeout: 60000,
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );

      console.log(`✅ [Service] Python backend response:`, response.data);

      return {
        success: true,
        browser_launched: true,
        profile_data_loaded: true,
        data_source: profileDataResult.source,
        account_id: accountId,
        ...response.data,
      };
    } catch (error) {
      console.error(
        `❌ [Service] Launch browser with profile data error:`,
        error,
      );

      if (error.code === 'ECONNREFUSED') {
        throw new Error(
          'Python backend is not available. Please ensure the backend service is running on port 8000.',
        );
      }

      if (error.response) {
        const errorMessage =
          error.response.data?.detail ||
          error.response.data?.message ||
          error.response.statusText;
        throw new Error(`Backend error: ${errorMessage}`);
      }

      throw new Error(
        `Failed to launch browser with profile data: ${error.message}`,
      );
    }
  }
}
