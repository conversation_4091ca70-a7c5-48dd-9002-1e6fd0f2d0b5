import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  ParseIntPipe,
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { ProfilesService } from './profiles.service';
import { CreateProfileShareDto } from './dto/create-profile-share.dto';
import { UpdateProfileShareDto } from './dto/update-profile-share.dto';
import { CreateProfileGroupDto } from './dto/create-profile-group.dto';
import { UpdateProfileGroupDto } from './dto/update-profile-group.dto';
import { CreateProfileDto } from './dto/create-profile.dto';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { CreateUserProfileGroupAccessDto } from './dto/create-user-profile-group-access.dto';
import { UpdateUserProfileGroupAccessDto } from './dto/update-user-profile-group-access.dto';

@Controller('profiles')
@UseGuards(JwtAuthGuard)
export class ProfilesController {
  constructor(private readonly profilesService: ProfilesService) {}

  @Post('share')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async shareProfile(
    @Body() createProfileShareDto: CreateProfileShareDto,
    @Request() req,
  ) {
    const adminUserId = req.user.sub;
    const profileShare = await this.profilesService.shareProfile(
      createProfileShareDto,
      adminUserId,
    );

    return {
      success: true,
      message: 'Profile shared successfully',
      data: profileShare,
    };
  }

  @Put('share/:id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async updateProfileShare(
    @Param('id', ParseIntPipe) shareId: number,
    @Body() updateProfileShareDto: UpdateProfileShareDto,
    @Request() req,
  ) {
    const adminUserId = req.user.sub;
    const updatedShare = await this.profilesService.updateProfileShare(
      shareId,
      updateProfileShareDto,
      adminUserId,
    );

    return {
      success: true,
      message: 'Profile share updated successfully',
      data: updatedShare,
    };
  }

  @Delete('share/:id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async revokeProfileShare(
    @Param('id', ParseIntPipe) shareId: number,
    @Request() req,
  ) {
    const adminUserId = req.user.sub;
    await this.profilesService.revokeProfileShare(shareId, adminUserId);

    return {
      success: true,
      message: 'Profile share revoked successfully',
    };
  }

  @Get('my-shared-profiles')
  async getMySharedProfiles(@Request() req) {
    const userId = req.user.sub;
    const sharedProfiles =
      await this.profilesService.getUserSharedProfiles(userId);

    return {
      success: true,
      data: sharedProfiles,
    };
  }

  @Get('accessible-accounts')
  async getAccessibleAccounts(@Request() req) {
    const userId = req.user.sub;
    const accounts =
      await this.profilesService.getUserAccessibleAccounts(userId);

    return {
      success: true,
      data: accounts,
    };
  }

  @Get('account/:accountId/shares')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async getAccountShares(@Param('accountId', ParseIntPipe) accountId: number) {
    const shares = await this.profilesService.getAccountShares(accountId);

    return {
      success: true,
      data: shares,
    };
  }

  @Get('check-access/:accountId')
  async checkProfileAccess(
    @Param('accountId', ParseIntPipe) accountId: number,
    @Request() req,
  ) {
    const userId = req.user.sub;
    const accessInfo = await this.profilesService.checkUserProfileAccess(
      userId,
      accountId,
    );

    return {
      success: true,
      data: accessInfo,
    };
  }

  @Get('account/:accountId/access-logs')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async getProfileAccessLogs(
    @Param('accountId', ParseIntPipe) accountId: number,
    @Query('limit') limit?: string,
  ) {
    const limitNum = limit ? parseInt(limit, 10) : 100;
    const logs = await this.profilesService.getProfileAccessLogs(
      accountId,
      limitNum,
    );

    return {
      success: true,
      data: logs,
    };
  }

  @Post('log-access/:accountId')
  async logProfileAccess(
    @Param('accountId', ParseIntPipe) accountId: number,
    @Body() body: { action: string; metadata?: any },
    @Request() req,
  ) {
    const userId = req.user.sub;

    // First check if user has access to this account
    const accessInfo = await this.profilesService.checkUserProfileAccess(
      userId,
      accountId,
    );

    if (!accessInfo.hasAccess) {
      return {
        success: false,
        message: 'Access denied to this profile',
      };
    }

    await this.profilesService.logProfileAccess(
      userId,
      accountId,
      body.action,
      body.metadata,
    );

    return {
      success: true,
      message: 'Access logged successfully',
    };
  }

  @Get('statistics')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async getProfileShareStatistics() {
    const statistics = await this.profilesService.getProfileShareStatistics();

    return {
      success: true,
      data: statistics,
    };
  }

  @Get('user/:userId/shared-profiles')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async getUserSharedProfiles(@Param('userId', ParseIntPipe) userId: number) {
    const sharedProfiles =
      await this.profilesService.getUserSharedProfiles(userId);

    return {
      success: true,
      data: sharedProfiles,
    };
  }

  @Get('bulk-share-candidates')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async getBulkShareCandidates(@Query('packageId') packageId?: string) {
    // This endpoint helps admins find users who should have access to certain profiles
    // based on their package purchases but don't have explicit shares yet

    // Implementation would involve:
    // 1. Get all users with specific package
    // 2. Get all accounts associated with that package
    // 3. Find users who don't have explicit shares for those accounts
    // 4. Return candidates for bulk sharing

    return {
      success: true,
      message: 'Bulk share candidates endpoint - implementation needed',
      data: [],
    };
  }

  @Post('bulk-share')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async bulkShareProfiles(
    @Body()
    body: {
      accountIds: number[];
      userIds: number[];
      permissions?: any;
      expiresAt?: string;
    },
    @Request() req,
  ) {
    const adminUserId = req.user.sub;
    const results = [];

    for (const accountId of body.accountIds) {
      for (const userId of body.userIds) {
        try {
          const shareDto: CreateProfileShareDto = {
            account_id: accountId,
            shared_with_user_id: userId,
            permissions: body.permissions,
            expires_at: body.expiresAt,
          };

          const share = await this.profilesService.shareProfile(
            shareDto,
            adminUserId,
          );

          results.push({
            accountId,
            userId,
            success: true,
            shareId: share.id,
          });
        } catch (error) {
          results.push({
            accountId,
            userId,
            success: false,
            error: error.message,
          });
        }
      }
    }

    const successCount = results.filter((r) => r.success).length;
    const failureCount = results.filter((r) => !r.success).length;

    return {
      success: true,
      message: `Bulk sharing completed: ${successCount} successful, ${failureCount} failed`,
      data: {
        results,
        summary: {
          total: results.length,
          successful: successCount,
          failed: failureCount,
        },
      },
    };
  }

  // ProfileGroup Management Endpoints
  @Post('groups')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async createProfileGroup(
    @Body() createProfileGroupDto: CreateProfileGroupDto,
    @Request() req,
  ) {
    const adminUserId = req.user.sub;
    const profileGroup = await this.profilesService.createProfileGroup(
      createProfileGroupDto,
      adminUserId,
    );

    return {
      success: true,
      message: 'Profile group created successfully',
      data: profileGroup,
    };
  }

  @Get('groups')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async getAllProfileGroups() {
    const profileGroups = await this.profilesService.getAllProfileGroups();

    return {
      success: true,
      data: profileGroups,
    };
  }

  @Get('groups/:id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async getProfileGroupById(@Param('id', ParseIntPipe) groupId: number) {
    const profileGroup =
      await this.profilesService.getProfileGroupById(groupId);

    return {
      success: true,
      data: profileGroup,
    };
  }

  @Put('groups/:id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async updateProfileGroup(
    @Param('id', ParseIntPipe) groupId: number,
    @Body() updateProfileGroupDto: UpdateProfileGroupDto,
    @Request() req,
  ) {
    const adminUserId = req.user.sub;
    const updatedGroup = await this.profilesService.updateProfileGroup(
      groupId,
      updateProfileGroupDto,
      adminUserId,
    );

    return {
      success: true,
      message: 'Profile group updated successfully',
      data: updatedGroup,
    };
  }

  @Delete('groups/:id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async deleteProfileGroup(
    @Param('id', ParseIntPipe) groupId: number,
    @Request() req,
  ) {
    const adminUserId = req.user.sub;
    await this.profilesService.deleteProfileGroup(groupId, adminUserId);

    return {
      success: true,
      message: 'Profile group deleted successfully',
    };
  }

  // Profile Management Endpoints
  @Post('items')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async createProfile(
    @Body() createProfileDto: CreateProfileDto,
    @Request() req,
  ) {
    const adminUserId = req.user.sub;
    const profile = await this.profilesService.createProfile(
      createProfileDto,
      adminUserId,
    );

    return {
      success: true,
      message: 'Profile created successfully',
      data: profile,
    };
  }

  @Get('items')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async getAllProfiles() {
    const profiles = await this.profilesService.getAllProfiles();

    return {
      success: true,
      data: profiles,
    };
  }

  @Get('groups/:groupId/items')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async getProfilesByGroup(@Param('groupId', ParseIntPipe) groupId: number) {
    const profiles = await this.profilesService.getProfilesByGroup(groupId);

    return {
      success: true,
      data: profiles,
    };
  }

  @Get('items/:id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async getProfileById(@Param('id', ParseIntPipe) profileId: number) {
    const profile = await this.profilesService.getProfileById(profileId);

    return {
      success: true,
      data: profile,
    };
  }

  @Put('items/:id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async updateProfile(
    @Param('id', ParseIntPipe) profileId: number,
    @Body() updateProfileDto: UpdateProfileDto,
    @Request() req,
  ) {
    const adminUserId = req.user.sub;
    const updatedProfile = await this.profilesService.updateProfile(
      profileId,
      updateProfileDto,
      adminUserId,
    );

    return {
      success: true,
      message: 'Profile updated successfully',
      data: updatedProfile,
    };
  }

  @Delete('items/:id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async deleteProfile(
    @Param('id', ParseIntPipe) profileId: number,
    @Request() req,
  ) {
    const adminUserId = req.user.sub;
    await this.profilesService.deleteProfile(profileId, adminUserId);

    return {
      success: true,
      message: 'Profile deleted successfully',
    };
  }

  // UserProfileGroupAccess Management Endpoints
  @Get('group-access')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async getAllUserProfileGroupAccesses() {
    const accesses =
      await this.profilesService.getAllUserProfileGroupAccesses();

    return {
      success: true,
      data: accesses,
    };
  }

  @Post('group-access')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async grantUserProfileGroupAccess(
    @Body() createAccessDto: CreateUserProfileGroupAccessDto,
    @Request() req,
  ) {
    const adminUserId = req.user.sub;
    const access = await this.profilesService.grantUserProfileGroupAccess(
      createAccessDto,
      adminUserId,
    );

    return {
      success: true,
      message: 'Profile group access granted successfully',
      data: access,
    };
  }

  @Put('group-access/:id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async updateUserProfileGroupAccess(
    @Param('id', ParseIntPipe) accessId: number,
    @Body() updateAccessDto: UpdateUserProfileGroupAccessDto,
    @Request() req,
  ) {
    const adminUserId = req.user.sub;
    const updatedAccess =
      await this.profilesService.updateUserProfileGroupAccess(
        accessId,
        updateAccessDto,
        adminUserId,
      );

    return {
      success: true,
      message: 'Profile group access updated successfully',
      data: updatedAccess,
    };
  }

  @Delete('group-access/:id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async revokeUserProfileGroupAccess(
    @Param('id', ParseIntPipe) accessId: number,
    @Request() req,
  ) {
    const adminUserId = req.user.sub;
    await this.profilesService.revokeUserProfileGroupAccess(
      accessId,
      adminUserId,
    );

    return {
      success: true,
      message: 'Profile group access revoked successfully',
    };
  }

  @Get('my-profile-groups')
  async getMyProfileGroups(@Request() req) {
    const userId = req.user.sub;
    const profileGroups =
      await this.profilesService.getUserAccessibleProfileGroups(userId);

    return {
      success: true,
      data: profileGroups,
    };
  }

  @Get('groups/:groupId/accesses')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async getProfileGroupAccesses(
    @Param('groupId', ParseIntPipe) groupId: number,
  ) {
    const accesses =
      await this.profilesService.getProfileGroupAccesses(groupId);

    return {
      success: true,
      data: accesses,
    };
  }

  @Get('user/:userId/group-accesses')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async getUserProfileGroupAccesses(
    @Param('userId', ParseIntPipe) userId: number,
  ) {
    const accesses =
      await this.profilesService.getUserProfileGroupAccesses(userId);

    return {
      success: true,
      data: accesses,
    };
  }

  // Browser Launch and Profile Management Endpoints
  @Post(':accountId/launch-browser')
  async launchBrowser(
    @Param('accountId', ParseIntPipe) accountId: number,
    @Request() req,
  ) {
    const userId = req.user.sub;
    const userRole = req.user.role;

    console.log(
      `🚀 [Controller] Launch browser request for account ${accountId} by user ${userId} (role: ${userRole})`,
    );

    try {
      // Check if user has access to this account (admin bypass)
      if (userRole !== 'admin') {
        const accessInfo = await this.profilesService.checkUserProfileAccess(
          userId,
          accountId,
        );

        if (!accessInfo.hasAccess) {
          console.log(
            `❌ [Controller] Access denied for user ${userId} to account ${accountId}`,
          );
          return {
            success: false,
            message: 'Access denied to this profile',
          };
        }
      }

      console.log(
        `✅ [Controller] Access granted, launching browser for account ${accountId}`,
      );

      // Launch browser via Python backend
      const launchResult = await this.profilesService.launchBrowserForAccount(
        accountId,
        userId,
      );

      console.log(`✅ [Controller] Browser launch result:`, launchResult);

      // Log the access
      await this.profilesService.logProfileAccess(
        userId,
        accountId,
        'browser_launch',
        { timestamp: new Date() },
      );

      const response = {
        success: true,
        message: 'Browser launched successfully',
        data: launchResult,
      };

      console.log(`✅ [Controller] Returning response:`, response);
      return response;
    } catch (error) {
      console.error(`❌ [Controller] Launch browser error:`, error);
      const errorResponse = {
        success: false,
        message: error.message || 'Failed to launch browser',
        error: error.stack || error.toString(),
      };
      console.log(`❌ [Controller] Returning error response:`, errorResponse);
      return errorResponse;
    }
  }

  @Post(':accountId/save-profile')
  async saveProfile(
    @Param('accountId', ParseIntPipe) accountId: number,
    @Body()
    profileData: {
      localStorage?: any;
      indexedDB?: any;
      history?: any;
      cookies?: any;
    },
    @Request() req,
  ) {
    const userId = req.user.sub;
    const userRole = req.user.role;

    try {
      // Check if user has access to this account (admin bypass)
      if (userRole !== 'admin') {
        const accessInfo = await this.profilesService.checkUserProfileAccess(
          userId,
          accountId,
        );

        if (!accessInfo.hasAccess) {
          return {
            success: false,
            message: 'Access denied to this profile',
          };
        }
      }

      // Save profile data to database
      const saveResult = await this.profilesService.saveProfileData(
        accountId,
        profileData,
        userId,
      );

      // Log the access
      await this.profilesService.logProfileAccess(
        userId,
        accountId,
        'profile_save',
        {
          timestamp: new Date(),
          dataTypes: Object.keys(profileData),
        },
      );

      return {
        success: true,
        message: 'Profile data saved successfully',
        data: saveResult,
      };
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Failed to save profile data',
      };
    }
  }

  @Get(':accountId/profile-data')
  async getProfileData(
    @Param('accountId', ParseIntPipe) accountId: number,
    @Request() req,
  ) {
    const userId = req.user.sub;
    const userRole = req.user.role;

    try {
      // Check if user has access to this account (admin bypass)
      if (userRole !== 'admin') {
        const accessInfo = await this.profilesService.checkUserProfileAccess(
          userId,
          accountId,
        );

        if (!accessInfo.hasAccess) {
          return {
            success: false,
            message: 'Access denied to this profile',
          };
        }
      }

      // Get profile data from database or compressed files
      const profileData = await this.profilesService.getProfileData(accountId);

      // Log the access
      await this.profilesService.logProfileAccess(
        userId,
        accountId,
        'profile_load',
        { timestamp: new Date() },
      );

      return {
        success: true,
        data: profileData,
      };
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Failed to get profile data',
      };
    }
  }

  @Post(':accountId/launch-browser-with-data')
  async launchBrowserWithData(
    @Param('accountId', ParseIntPipe) accountId: number,
    @Request() req,
  ) {
    const userId = req.user.sub;
    const userRole = req.user.role;

    console.log(
      `🚀 [Controller] Launch browser with data request for account ${accountId} by user ${userId} (role: ${userRole})`,
    );

    try {
      // Check if user has access to this account (admin bypass)
      if (userRole !== 'admin') {
        const accessInfo = await this.profilesService.checkUserProfileAccess(
          userId,
          accountId,
        );

        if (!accessInfo.hasAccess) {
          console.log(
            `❌ [Controller] Access denied for user ${userId} to account ${accountId}`,
          );
          return {
            success: false,
            message: 'Access denied to this profile',
          };
        }
      }

      console.log(
        `✅ [Controller] Access granted, launching browser with data for account ${accountId}`,
      );

      // Launch browser with profile data via Python backend
      const launchResult =
        await this.profilesService.launchBrowserWithProfileData(
          accountId,
          userId,
        );

      console.log(
        `✅ [Controller] Browser launch with data result:`,
        launchResult,
      );

      // Log the access
      await this.profilesService.logProfileAccess(
        userId,
        accountId,
        'browser_launch_with_data',
        { timestamp: new Date() },
      );

      const response = {
        success: true,
        message: 'Browser launched successfully with profile data',
        data: launchResult,
      };

      console.log(`✅ [Controller] Returning response:`, response);
      return response;
    } catch (error) {
      console.error(`❌ [Controller] Launch browser with data error:`, error);
      const errorResponse = {
        success: false,
        message: error.message || 'Failed to launch browser with profile data',
        error: error.stack || error.toString(),
      };
      console.log(`❌ [Controller] Returning error response:`, errorResponse);
      return errorResponse;
    }
  }
}
