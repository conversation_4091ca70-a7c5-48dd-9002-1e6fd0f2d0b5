import { PartialType } from '@nestjs/mapped-types';
import { CreateUserProfileGroupAccessDto } from './create-user-profile-group-access.dto';
import { IsString, IsOptional, IsIn } from 'class-validator';

export class UpdateUserProfileGroupAccessDto extends PartialType(
  CreateUserProfileGroupAccessDto,
) {
  @IsString()
  @IsOptional()
  @IsIn(['active', 'suspended', 'revoked'])
  status?: string;
}
