import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ber,
  Is<PERSON><PERSON>al,
  IsNot<PERSON>mpty,
  <PERSON><PERSON>ength,
} from 'class-validator';

export class CreateProfileDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @IsNumber()
  account_id: number;

  @IsNumber()
  profile_group_id: number;

  @IsString()
  @IsOptional()
  @MaxLength(1000)
  description?: string;
}
