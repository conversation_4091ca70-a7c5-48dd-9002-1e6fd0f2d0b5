import {
  IsNumber,
  IsString,
  IsOptional,
  IsBoolean,
  IsEnum,
  IsDateString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

export enum AccessLevel {
  READ = 'read',
  WRITE = 'write',
  FULL = 'full',
}

export class ProfileSharePermissionsDto {
  @IsBoolean()
  can_modify_profile: boolean;

  @IsBoolean()
  can_view_history: boolean;

  @IsBoolean()
  can_export_data: boolean;

  @IsEnum(AccessLevel)
  access_level: AccessLevel;
}

export class CreateProfileShareDto {
  @IsNumber()
  account_id: number;

  @IsNumber()
  shared_with_user_id: number;

  @IsOptional()
  @ValidateNested()
  @Type(() => ProfileSharePermissionsDto)
  permissions?: ProfileSharePermissionsDto;

  @IsOptional()
  @IsDateString()
  expires_at?: string;
}
