import {
  IsN<PERSON>ber,
  IsOptional,
  IsDateString,
  IsObject,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { AccessPermissions } from '../entities/user-profile-group-access.entity';

class AccessPermissionsDto implements AccessPermissions {
  can_launch_browser: boolean;
  can_view_profiles: boolean;
  can_export_data: boolean;
  access_level: 'read' | 'write' | 'full';
}

export class CreateUserProfileGroupAccessDto {
  @IsNumber()
  user_id: number;

  @IsNumber()
  profile_group_id: number;

  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => AccessPermissionsDto)
  permissions?: AccessPermissions;

  @IsDateString()
  @IsOptional()
  expires_at?: string;
}
