import { PartialType } from '@nestjs/mapped-types';
import { IsOptional, IsString, IsEnum } from 'class-validator';
import { CreateProfileShareDto } from './create-profile-share.dto';

export enum ProfileShareStatus {
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  REVOKED = 'revoked',
}

export class UpdateProfileShareDto extends PartialType(CreateProfileShareDto) {
  @IsOptional()
  @IsEnum(ProfileShareStatus)
  status?: ProfileShareStatus;
}
