import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGeneratedC<PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Account } from '../../accounts/entities/account.entity';
import { User } from '../../users/entities/user.entity';
import { ProfileGroup } from './profile-group.entity';

@Entity('profiles')
export class Profile {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column()
  account_id: number;

  @Column()
  profile_group_id: number;

  @Column()
  created_by_admin_id: number;

  @Column({ default: 'active' })
  status: string; // active, inactive, archived

  @Column({ type: 'text', nullable: true })
  description: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  // Relations
  @ManyToOne(() => Account)
  @JoinColumn({ name: 'account_id' })
  account: Account;

  @ManyToOne(() => ProfileGroup, (profileGroup) => profileGroup.profiles)
  @JoinColumn({ name: 'profile_group_id' })
  profileGroup: ProfileGroup;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by_admin_id' })
  createdByAdmin: User;
}
