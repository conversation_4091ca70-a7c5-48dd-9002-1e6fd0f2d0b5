import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Account } from '../../accounts/entities/account.entity';
import { User } from '../../users/entities/user.entity';

export interface ProfileSharePermissions {
  can_modify_profile: boolean;
  can_view_history: boolean;
  can_export_data: boolean;
  access_level: 'read' | 'write' | 'full';
}

@Entity('profile_shares')
export class ProfileShare {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  account_id: number;

  @Column()
  shared_with_user_id: number;

  @Column()
  shared_by_admin_id: number;

  @Column({ default: 'active' })
  status: string; // active, suspended, revoked

  @Column({ type: 'jsonb', nullable: true })
  permissions: ProfileSharePermissions;

  @Column({ type: 'timestamp', nullable: true })
  expires_at: Date;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @ManyToOne(() => Account, (account) => account.profileShares)
  @JoinColumn({ name: 'account_id' })
  account: Account;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'shared_with_user_id' })
  sharedWithUser: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'shared_by_admin_id' })
  sharedByAdmin: User;
}
