import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { ProfileGroup } from './profile-group.entity';

export interface AccessPermissions {
  can_launch_browser: boolean;
  can_view_profiles: boolean;
  can_export_data: boolean;
  access_level: 'read' | 'write' | 'full';
}

@Entity('user_profile_group_access')
export class UserProfileGroupAccess {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  user_id: number;

  @Column()
  profile_group_id: number;

  @Column()
  granted_by_admin_id: number;

  @Column({ default: 'active' })
  status: string; // active, suspended, revoked

  @Column({ type: 'jsonb', nullable: true })
  permissions: AccessPermissions;

  @Column({ type: 'timestamp', nullable: true })
  expires_at: Date;

  @CreateDateColumn()
  granted_at: Date;

  // Relations
  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => ProfileGroup, (profileGroup) => profileGroup.userAccesses)
  @JoinColumn({ name: 'profile_group_id' })
  profileGroup: ProfileGroup;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'granted_by_admin_id' })
  grantedByAdmin: User;
}
