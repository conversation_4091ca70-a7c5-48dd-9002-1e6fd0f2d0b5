import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGenerated<PERSON><PERSON>umn,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Account } from '../../accounts/entities/account.entity';
import { User } from '../../users/entities/user.entity';

export interface ProfileAccessMetadata {
  ip_address?: string;
  user_agent?: string;
  session_duration?: number;
  data_modified?: string[];
  browser_fingerprint?: string;
  proxy_used?: string;
}

@Entity('profile_access_logs')
export class ProfileAccessLog {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  user_id: number;

  @Column()
  account_id: number;

  @Column()
  action: string; // login, logout, sync, export, modify, view

  @Column({ type: 'jsonb', nullable: true })
  metadata: ProfileAccessMetadata;

  @CreateDateColumn()
  created_at: Date;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => Account)
  @JoinColumn({ name: 'account_id' })
  account: Account;
}
