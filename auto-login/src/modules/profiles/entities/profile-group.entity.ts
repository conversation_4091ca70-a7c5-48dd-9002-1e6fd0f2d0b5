import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGenerated<PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Profile } from './profile.entity';
import { UserProfileGroupAccess } from './user-profile-group-access.entity';

@Entity('profile_groups')
export class ProfileGroup {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column()
  created_by_admin_id: number;

  @Column({ default: 'active' })
  status: string; // active, inactive, archived

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  // Relations
  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by_admin_id' })
  createdByAdmin: User;

  @OneToMany(() => Profile, (profile) => profile.profileGroup)
  profiles: Profile[];

  @OneToMany(() => UserProfileGroupAccess, (access) => access.profileGroup)
  userAccesses: UserProfileGroupAccess[];
}
