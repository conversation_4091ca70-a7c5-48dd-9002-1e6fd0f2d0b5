import {
  IsString,
  <PERSON>NotEmpty,
  IsNumber,
  IsOptional,
  IsArray,
  ValidateNested,
  IsBoolean,
} from 'class-validator';
import { Type } from 'class-transformer';
import { PackageFeatureDto } from './package-feature.dto';

class PackageDurationDto {
  @IsNotEmpty()
  @IsNumber()
  duration_days: number;

  @IsNotEmpty()
  @IsNumber()
  price: number;

  @IsOptional()
  @IsNumber()
  discount_percent?: number;
}

class PackageDiscountDto {
  @IsNotEmpty()
  @IsNumber()
  payment_method_id: number;

  @IsNotEmpty()
  @IsNumber()
  discount_percent: number;
}

export class CreatePackageDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PackageFeatureDto)
  features?: PackageFeatureDto[];

  @IsOptional()
  @IsBoolean()
  is_best_choice?: boolean;

  @IsOptional()
  @IsBoolean()
  has_trail?: boolean;

  @IsOptional()
  @IsBoolean()
  has_prompt_library?: boolean;

  @IsOptional()
  @IsBoolean()
  has_prompt_video?: boolean;

  @IsOptional()
  @IsBoolean()
  has_bot_insta?: boolean;

  @IsOptional()
  @IsBoolean()
  is_single_tool?: boolean;

  @IsOptional()
  @IsNumber()
  sort?: number;

  @IsOptional()
  @IsString()
  pk_img?: string;

  // price and duration_days fields have been removed from Package

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PackageDurationDto)
  durations?: PackageDurationDto[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PackageDiscountDto)
  discounts?: PackageDiscountDto[];
}
