import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Product } from './entities/product.entity';
import { ProductUser } from './entities/product-user.entity';
import { ProductDuration } from './entities/product-duration.entity';
import { AccountProduct } from './entities/account-product.entity';
import { Category } from './entities/category.entity';
import { ProductCategory } from './entities/product-category.entity';
import { PaymentMethod } from './entities/payment-method.entity';
import { ProductDiscount } from './entities/product-discount.entity';
import { ProductsController } from './products.controller';
import { ProductUsersController } from './product-users.controller';
import { CategoriesController } from './categories.controller';
import { ProductsService } from './products.service';
import { ProductUsersService } from './product-users.service';
import { AuthModule } from '../auth/auth.module';
import { CategoriesService } from './categories.service';
import { CurrencyModule } from '../currency/currency.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Product,
      ProductUser,
      ProductDuration,
      AccountProduct,
      Category,
      ProductCategory,
      PaymentMethod,
      ProductDiscount,
    ]),
    AuthModule,
    CurrencyModule,
  ],
  controllers: [
    ProductsController,
    ProductUsersController,
    CategoriesController,
  ],
  providers: [ProductsService, ProductUsersService, CategoriesService],
  exports: [ProductsService, ProductUsersService, CategoriesService],
})
export class ProductsModule {}
