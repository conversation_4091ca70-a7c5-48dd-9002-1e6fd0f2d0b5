import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Category } from './entities/category.entity';
import { ProductCategory } from './entities/product-category.entity';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';

@Injectable()
export class CategoriesService {
  constructor(
    @InjectRepository(Category)
    private readonly categoryRepository: Repository<Category>,
    @InjectRepository(ProductCategory)
    private readonly productCategoryRepository: Repository<ProductCategory>,
  ) {}

  async findAll(page = 1, limit = 10): Promise<{ data: any[]; meta: any }> {
    const [categories, total] = await this.categoryRepository.findAndCount({
      order: { sort: 'ASC' },
      skip: (page - 1) * limit,
      take: limit,
    });

    // Get product count for each category
    const categoriesWithProductCount = await Promise.all(
      categories.map(async (category) => {
        const productsCount = await this.productCategoryRepository.count({
          where: { category_id: category.id },
        });
        return {
          ...category,
          productsCount,
        };
      }),
    );

    return {
      data: categoriesWithProductCount,
      meta: {
        total,
        page: +page,
        limit: +limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: number): Promise<Category> {
    const category = await this.categoryRepository.findOne({
      where: { id },
    });
    if (!category) {
      throw new NotFoundException(`Category with ID ${id} not found`);
    }
    return category;
  }

  async create(createCategoryDto: CreateCategoryDto): Promise<Category> {
    try {
      // If sort is not provided, set it to the highest value + 1
      if (createCategoryDto.sort === undefined) {
        const highestSortCategory = await this.categoryRepository.findOne({
          order: { sort: 'DESC' },
        });
        createCategoryDto.sort = highestSortCategory
          ? highestSortCategory.sort + 1
          : 0;
      }

      const category = this.categoryRepository.create(createCategoryDto);
      return await this.categoryRepository.save(category);
    } catch (error) {
      if (error.code === '23505') {
        // Unique constraint violation
        throw new BadRequestException(
          'A category with this name already exists',
        );
      }
      throw error;
    }
  }

  async update(
    id: number,
    updateCategoryDto: UpdateCategoryDto,
  ): Promise<Category> {
    try {
      const category = await this.findOne(id);
      Object.assign(category, updateCategoryDto);
      return await this.categoryRepository.save(category);
    } catch (error) {
      if (error.code === '23505') {
        // Unique constraint violation
        throw new BadRequestException(
          'A category with this name already exists',
        );
      }
      throw error;
    }
  }

  async remove(id: number): Promise<{ success: boolean; message: string }> {
    const category = await this.findOne(id);

    // Check if category has products
    const productsCount = await this.productCategoryRepository.count({
      where: { category_id: id },
    });

    if (productsCount > 0) {
      // Remove all product-category relationships for this category
      await this.productCategoryRepository.delete({
        category_id: id,
      });
    }

    await this.categoryRepository.remove(category);
    return { success: true, message: 'Category deleted successfully' };
  }
}
