import {
  Controller,
  <PERSON>,
  Param,
  Body,
  UseGuards,
  UnauthorizedException,
  ParseIntPipe,
  HttpException,
  HttpStatus,
  Req,
  Post,
  Get,
  Query,
  Delete,
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { UsersService } from './users.service';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { CreateTrialUserDto } from './dto/create-trial-user.dto';

class UpdateUserStatusDto {
  status: string;
}

@Controller('users')
@UseGuards(JwtAuthGuard)
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Patch(':id/status')
  async updateStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateUserStatusDto: UpdateUserStatusDto,
    @Req() req,
  ) {
    // Check if the requesting user has admin role
    const requestingUser = req.user;
    if (requestingUser.role !== 'admin') {
      throw new UnauthorizedException(
        'Only admin users can update user status',
      );
    }

    try {
      const updatedUser = await this.usersService.updateStatus(
        id,
        updateUserStatusDto.status,
      );
      return updatedUser;
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to update user status',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get()
  @UseGuards(RolesGuard)
  @Roles('admin')
  async findAllUsers(
    @Query('page', new ParseIntPipe({ optional: true })) page = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit = 50,
    @Query('search') search?: string,
    @Query('status') status?: string,
  ) {
    try {
      return await this.usersService.findAllUsers(page, limit, search, status);
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve users',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('trial')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async createTrialUser(@Body() createTrialUserDto: CreateTrialUserDto) {
    try {
      const user = await this.usersService.createTrialUser(createTrialUserDto);
      return user;
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to create trial user',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('trial')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async findAllTrialUsers(
    @Query('page', new ParseIntPipe({ optional: true })) page = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit = 10,
  ) {
    try {
      return await this.usersService.findAllTrialUsers(page, limit);
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve trial users',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete('trial/:id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async removeTrialUser(@Param('id', ParseIntPipe) id: number) {
    try {
      await this.usersService.removeUser(id);
      return { message: 'Trial user deleted successfully' };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to delete trial user',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('trial/:id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async findTrialUserById(@Param('id', ParseIntPipe) id: number) {
    try {
      const user = await this.usersService.findTrialUserById(id);
      return user;
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to retrieve trial user',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id/package-access')
  @UseGuards(JwtAuthGuard)
  async checkPackageAccess(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: any,
  ) {
    try {
      // Check if user is accessing their own data or is admin
      if (req.user.sub !== id && req.user.role !== 'admin') {
        throw new UnauthorizedException('Access denied');
      }

      // Admin users always have access
      if (req.user.role === 'admin') {
        return {
          user_id: id,
          has_bot_insta_access: true,
          message: 'Admin user has full access to all features',
        };
      }

      const hasBotInstaAccess =
        await this.usersService.checkUserHasBotInstaPackage(id);

      return {
        user_id: id,
        has_bot_insta_access: hasBotInstaAccess,
        message: hasBotInstaAccess
          ? 'User has active package with bot Instagram feature'
          : 'User does not have active package with bot Instagram feature',
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to check package access',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
