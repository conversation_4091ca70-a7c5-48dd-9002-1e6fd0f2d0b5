<%- include('../../partials/layout', {
  title: 'Product Management',
  currentPath: '/admin/products',
  body: `
    <div class="container mx-auto">
      <div class="flex justify-between items-center mb-6">
        <div>
          <button id="createProductBtn" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
            Create New Product
          </button>
          <button id="manageCategoriesBtn" class="ml-2 bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
            Manage Categories
          </button>
          <button id="backToHomeBtn" class="ml-2 bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Back to Home
          </button>
        </div>
      </div>

      <!-- Search and Filter -->
      <div class="bg-white p-4 rounded-lg shadow mb-6">
        <div class="flex flex-col md:flex-row gap-4">
          <div class="flex-1">
            <label for="searchInput" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
            <input
              type="text"
              id="searchInput"
              placeholder="Search by name or description"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>
          <div class="w-full md:w-48">
            <label for="categorySelect" class="block text-sm font-medium text-gray-700 mb-1">Filter by Category</label>
            <select
              id="categorySelect"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="all">All Categories</option>
              <!-- Categories will be populated dynamically -->
            </select>
          </div>
          <div class="w-full md:w-48">
            <label for="stockSelect" class="block text-sm font-medium text-gray-700 mb-1">Stock Status</label>
            <select
              id="stockSelect"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="all">All Products</option>
              <option value="in_stock">In Stock</option>
              <option value="out_of_stock">Out of Stock</option>
            </select>
          </div>
          <div class="flex items-end">
            <button
              id="applyFilterBtn"
              class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            >
              Apply Filters
            </button>
          </div>
        </div>
      </div>

      <!-- Products Table -->
      <div class="bg-white shadow rounded-lg overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Image</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody id="productsTableBody" class="bg-white divide-y divide-gray-200">
            <!-- Product rows will be inserted here -->
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="mt-4 flex justify-between items-center">
        <div>
          <button id="prevPageBtn" class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded-l">
            Previous
          </button>
          <button id="nextPageBtn" class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded-r">
            Next
          </button>
        </div>
        <div class="text-gray-600">
          Page <span id="currentPage">1</span> of <span id="totalPages">1</span>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal opacity-0 pointer-events-none fixed w-full h-full top-0 left-0 flex items-center justify-center z-50 hidden">
      <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>

      <div class="modal-container bg-white w-11/12 md:max-w-md mx-auto rounded shadow-lg z-50 overflow-y-auto">
        <div class="modal-content py-4 text-left px-6">
          <div class="flex justify-between items-center pb-3">
            <p class="text-2xl font-bold">Confirm Delete</p>
            <div class="modal-close cursor-pointer z-50" onclick="hideDeleteModal()">
              <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18">
                <path d="M14.53 4.53l-1.06-1.06L9 7.94 4.53 3.47 3.47 4.53 7.94 9l-4.47 4.47 1.06 1.06L9 10.06l4.47 4.47 1.06-1.06L10.06 9z"></path>
              </svg>
            </div>
          </div>

          <p>Are you sure you want to delete this product? This action cannot be undone.</p>

          <div class="flex justify-end pt-2">
            <button class="modal-close px-4 bg-gray-200 p-3 rounded-lg text-black hover:bg-gray-300 mr-2" onclick="hideDeleteModal()">Cancel</button>
            <button class="px-4 bg-red-500 p-3 rounded-lg text-white hover:bg-red-600" onclick="deleteProduct()">Delete</button>
          </div>
        </div>
      </div>
    </div>
  `,
  additionalScripts: `
    <script>
      // Global variables
      let currentPage = 1;
      let searchTerm = '';
      let categoryFilter = 'all';
      let stockFilter = 'all';
      let deleteProductId = null;

      // Load products with pagination, search and filter
      async function loadProducts(page = 1) {
        try {
          let url = '/products?page=' + page + '&limit=10';

          // Add search and filter parameters if they exist
          if (searchTerm) {
            url += '&search=' + encodeURIComponent(searchTerm);
          }

          if (categoryFilter !== 'all') {
            url += '&categories=' + categoryFilter;
          }

          if (stockFilter !== 'all') {
            url += '&inStock=' + (stockFilter === 'in_stock' ? 'true' : 'false');
          }

          const response = await axios.get(url);
          const products = response.data.data;
          const totalPages = response.data.meta.totalPages || 1;

          document.getElementById('currentPage').textContent = page;
          document.getElementById('totalPages').textContent = totalPages;

          renderProductsTable(products);
        } catch (error) {
          handleApiError(error);
        }
      }

      // Load categories for filter dropdown
      async function loadCategories() {
        try {
          const response = await axios.get('/products/categories');
          const categories = response.data;
          
          const categorySelect = document.getElementById('categorySelect');
          categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = category.name;
            categorySelect.appendChild(option);
          });
        } catch (error) {
          console.error('Error loading categories:', error);
        }
      }

      // Render products table
      function renderProductsTable(products) {
        const tableBody = document.getElementById('productsTableBody');
        tableBody.innerHTML = '';

        if (products.length === 0) {
          const emptyRow = document.createElement('tr');
          emptyRow.innerHTML =
            '<td colspan="9" class="px-6 py-4 text-center text-gray-500">' +
              'No products found' +
            '</td>';
          tableBody.appendChild(emptyRow);
          return;
        }

        products.forEach(product => {
          const row = document.createElement('tr');
          row.className = 'hover:bg-gray-50';

          row.innerHTML =
            '<td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">' + product.id + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap">' +
              (product.image_url ? 
                '<img src="' + product.image_url + '" alt="' + product.name + '" class="h-10 w-10 rounded-full object-cover">' :
                '<div class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center"><span class="text-xs text-gray-500">No img</span></div>') +
            '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + product.name + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' +
              (product.productCategories && product.productCategories.length > 0 ?
                '<div class="space-y-1">' +
                product.productCategories.map(productCat =>
                  '<div class="flex items-center">' +
                    '<span class="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded mr-2">' + productCat.category.name + '</span>' +
                    (productCat.category.is_hot ? '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">🔥 Hot</span>' : '') +
                  '</div>'
                ).join('') + '</div>' :
                '<span class="text-gray-400">No category</span>') +
            '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm font-medium">' +
              '<div class="flex space-x-2">' +
                '<button onclick="viewProduct(' + product.id + ')" class="text-indigo-600 hover:text-indigo-900">View</button>' +
                '<button onclick="editProduct(' + product.id + ')" class="text-blue-600 hover:text-blue-900">Edit</button>' +
                '<button onclick="showDeleteModal(' + product.id + ')" class="text-red-600 hover:text-red-900">Delete</button>' +
              '</div>' +
            '</td>';

          tableBody.appendChild(row);
        });
      }

      // View product details
      function viewProduct(id) {
        window.location.href = '/admin/products/' + id;
      }

      // Edit product
      function editProduct(id) {
        window.location.href = '/admin/products/' + id + '/edit';
      }

      // Show delete confirmation modal
      function showDeleteModal(id) {
        deleteProductId = id;
        document.getElementById('deleteModal').classList.remove('hidden');
        document.getElementById('deleteModal').classList.remove('opacity-0');
        document.getElementById('deleteModal').classList.add('opacity-100');
        document.getElementById('deleteModal').classList.add('pointer-events-auto');
        document.body.classList.add('modal-active');
      }

      // Hide delete confirmation modal
      function hideDeleteModal() {
        document.getElementById('deleteModal').classList.add('opacity-0');
        document.getElementById('deleteModal').classList.remove('opacity-100');
        document.getElementById('deleteModal').classList.add('pointer-events-none');
        setTimeout(() => {
          document.getElementById('deleteModal').classList.add('hidden');
          document.body.classList.remove('modal-active');
        }, 300);
        deleteProductId = null;
      }

      // Delete product
      async function deleteProduct() {
        if (!deleteProductId) return;

        try {
          await axios.delete('/products/' + deleteProductId);
          hideDeleteModal();
          showToast('Product deleted successfully');
          loadProducts(currentPage);
        } catch (error) {
          hideDeleteModal();
          handleApiError(error);
        }
      }

      // Show toast notification
      function showToast(message, type = 'success') {
        const backgroundColor = type === 'success' ? '#48bb78' : '#f56565';

        Toastify({
          text: message,
          duration: 3000,
          close: true,
          gravity: "top",
          position: "right",
          backgroundColor,
          stopOnFocus: true
        }).showToast();
      }

      // Handle API error
      function handleApiError(error) {
        console.error('API Error:', error);
        let errorMessage = 'An error occurred. Please try again.';
        
        if (error.response && error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;
        } else if (error.message) {
          errorMessage = error.message;
        }
        
        showToast(errorMessage, 'error');
      }

      // Document ready
      document.addEventListener('DOMContentLoaded', async () => {
        // Initial load
        loadProducts(currentPage);
        loadCategories();

        // Pagination
        document.getElementById('prevPageBtn').addEventListener('click', () => {
          if (currentPage > 1) {
            currentPage--;
            loadProducts(currentPage);
          }
        });

        document.getElementById('nextPageBtn').addEventListener('click', () => {
          const totalPages = parseInt(document.getElementById('totalPages').textContent);
          if (currentPage < totalPages) {
            currentPage++;
            loadProducts(currentPage);
          }
        });

        // Search and filter
        document.getElementById('applyFilterBtn').addEventListener('click', () => {
          searchTerm = document.getElementById('searchInput').value.trim();
          categoryFilter = document.getElementById('categorySelect').value;
          stockFilter = document.getElementById('stockSelect').value;
          currentPage = 1;
          loadProducts(currentPage);
        });

        // Create new product
        document.getElementById('createProductBtn').addEventListener('click', () => {
          window.location.href = '/admin/products/create';
        });

        // Manage categories
        document.getElementById('manageCategoriesBtn').addEventListener('click', () => {
          window.location.href = '/admin/categories';
        });

        // Back to home
        document.getElementById('backToHomeBtn').addEventListener('click', () => {
          window.location.href = '/';
        });
      });
    </script>
  `
}) %>