#!/bin/bash

# Facebook Automation Desktop - Test All Modes
# This script tests both web and desktop modes

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get the script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

cd "$PROJECT_ROOT"

echo "🧪 Testing Facebook Automation Desktop Modes..."
echo "📁 Project root: $PROJECT_ROOT"

# Test 1: Backend Health Check
print_info "Testing backend health..."
if curl -s http://127.0.0.1:8000/health > /dev/null 2>&1; then
    print_success "Backend is running and healthy"
else
    print_error "Backend is not running. Please start backend first."
    exit 1
fi

# Test 2: Frontend Build
print_info "Testing frontend build..."
cd frontend
if npm run webpack:build > /dev/null 2>&1; then
    print_success "Frontend build successful"
else
    print_error "Frontend build failed"
    exit 1
fi

# Test 3: Check if dist files exist
print_info "Checking build artifacts..."
if [ -f "dist/index.html" ] && ls dist/main*.js > /dev/null 2>&1; then
    print_success "Build artifacts exist"
else
    print_error "Build artifacts missing"
    exit 1
fi

# Test 4: Check templates
print_info "Checking HTML templates..."
if [ -f "public/index.html" ] && [ -f "public/index.dev.html" ]; then
    print_success "Both HTML templates exist"
else
    print_error "HTML templates missing"
    exit 1
fi

cd "$PROJECT_ROOT"

# Test 5: Scripts exist and are executable
print_info "Checking scripts..."
scripts=("dev-web.sh" "frontend-web.sh" "run-production.sh")
for script in "${scripts[@]}"; do
    if [ -x "scripts/$script" ]; then
        print_success "Script $script is executable"
    else
        print_error "Script $script is missing or not executable"
        exit 1
    fi
done

# Test 6: API Endpoints
print_info "Testing API endpoints..."
endpoints=("/health" "/api/profiles/")
for endpoint in "${endpoints[@]}"; do
    if curl -s "http://127.0.0.1:8000$endpoint" > /dev/null 2>&1; then
        print_success "API endpoint $endpoint is accessible"
    else
        print_warning "API endpoint $endpoint may have issues"
    fi
done

print_success "🎉 All tests passed!"
echo ""
print_info "Available modes:"
print_info "1. Web Development: ./scripts/dev-web.sh"
print_info "2. Frontend Only: ./scripts/frontend-web.sh"
print_info "3. Desktop App: ./scripts/run-production.sh"
echo ""
print_info "URLs:"
print_info "- Frontend: http://localhost:3000"
print_info "- Backend: http://localhost:8000"
print_info "- API Docs: http://localhost:8000/docs"
