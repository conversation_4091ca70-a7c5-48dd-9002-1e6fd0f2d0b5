#!/bin/bash

# Facebook Automation Desktop - Test Facebook Login Workflow
# This script tests the complete Facebook login workflow

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# API base URL
API_BASE="http://127.0.0.1:8000/api"

echo "🔐 Testing Facebook Login Workflow..."

# Test 1: Create a test profile
print_info "Creating test profile for Facebook workflow..."
CREATE_RESPONSE=$(curl -s -X POST "$API_BASE/profiles/" \
    -H "Content-Type: application/json" \
    -d '{
        "name": "Facebook Test Profile",
        "proxy_type": "no_proxy"
    }')

if echo "$CREATE_RESPONSE" | grep -q '"id"'; then
    PROFILE_ID=$(echo "$CREATE_RESPONSE" | grep -o '"id":[0-9]*' | cut -d':' -f2)
    print_success "Test profile created (ID: $PROFILE_ID)"
else
    print_error "Failed to create test profile"
    exit 1
fi

# Test 2: Check initial browser status
print_info "Checking initial browser status..."
STATUS_RESPONSE=$(curl -s "$API_BASE/profiles/$PROFILE_ID/browser-status")
if echo "$STATUS_RESPONSE" | grep -q '"browser_active":false'; then
    print_success "Initial browser status: Not active (correct)"
else
    print_error "Unexpected initial browser status"
    exit 1
fi

# Test 3: Launch browser
print_info "Launching browser..."
LAUNCH_RESPONSE=$(curl -s -X POST "$API_BASE/profiles/$PROFILE_ID/launch-browser")
if echo "$LAUNCH_RESPONSE" | grep -q '"success":true'; then
    print_success "Browser launched successfully"
else
    print_error "Failed to launch browser"
    echo "Response: $LAUNCH_RESPONSE"
    exit 1
fi

# Test 4: Check browser status after launch
print_info "Checking browser status after launch..."
STATUS_RESPONSE=$(curl -s "$API_BASE/profiles/$PROFILE_ID/browser-status")
if echo "$STATUS_RESPONSE" | grep -q '"browser_active":true'; then
    print_success "Browser status: Active (correct)"
else
    print_error "Browser should be active after launch"
    exit 1
fi

# Test 5: Try to launch browser again (should detect already active)
print_info "Testing duplicate browser launch detection..."
LAUNCH2_RESPONSE=$(curl -s -X POST "$API_BASE/profiles/$PROFILE_ID/launch-browser")
if echo "$LAUNCH2_RESPONSE" | grep -q '"already_active":true'; then
    print_success "Duplicate launch detected correctly"
else
    print_warning "Duplicate launch detection may have issues"
    echo "Response: $LAUNCH2_RESPONSE"
fi

# Test 6: Open Facebook
print_info "Opening Facebook login page..."
FB_RESPONSE=$(curl -s -X POST "$API_BASE/profiles/$PROFILE_ID/open-facebook")
if echo "$FB_RESPONSE" | grep -q '"success":true'; then
    print_success "Facebook login page opened"
else
    print_error "Failed to open Facebook"
    echo "Response: $FB_RESPONSE"
    exit 1
fi

# Test 7: Check browser status after Facebook open
print_info "Checking browser status after Facebook open..."
STATUS_RESPONSE=$(curl -s "$API_BASE/profiles/$PROFILE_ID/browser-status")
if echo "$STATUS_RESPONSE" | grep -q '"browser_active":true'; then
    print_success "Browser still active after Facebook open (correct)"
else
    print_error "Browser should still be active"
    exit 1
fi

# Test 8: Test complete login (will fail since we didn't actually login)
print_info "Testing complete login endpoint..."
LOGIN_RESPONSE=$(curl -s -X POST "$API_BASE/profiles/$PROFILE_ID/complete-login")
if echo "$LOGIN_RESPONSE" | grep -q '"logged_in":false'; then
    print_success "Complete login endpoint works (login not completed as expected)"
else
    print_warning "Complete login response unexpected"
    echo "Response: $LOGIN_RESPONSE"
fi

# Test 9: Check browser status after complete login attempt
print_info "Checking browser status after complete login attempt..."
STATUS_RESPONSE=$(curl -s "$API_BASE/profiles/$PROFILE_ID/browser-status")
if echo "$STATUS_RESPONSE" | grep -q '"browser_active":true'; then
    print_success "Browser still active after complete login attempt (correct)"
else
    print_error "Browser should still be active"
    exit 1
fi

# Test 10: Close browser
print_info "Closing browser..."
CLOSE_RESPONSE=$(curl -s -X POST "$API_BASE/profiles/$PROFILE_ID/close-browser")
if echo "$CLOSE_RESPONSE" | grep -q '"success":true'; then
    print_success "Browser closed successfully"
else
    print_error "Failed to close browser"
    echo "Response: $CLOSE_RESPONSE"
    exit 1
fi

# Test 11: Check browser status after close
print_info "Checking browser status after close..."
STATUS_RESPONSE=$(curl -s "$API_BASE/profiles/$PROFILE_ID/browser-status")
if echo "$STATUS_RESPONSE" | grep -q '"browser_active":false'; then
    print_success "Browser status: Not active after close (correct)"
else
    print_error "Browser should not be active after close"
    exit 1
fi

# Test 12: Try to launch browser again after close
print_info "Testing browser launch after close..."
LAUNCH3_RESPONSE=$(curl -s -X POST "$API_BASE/profiles/$PROFILE_ID/launch-browser")
if echo "$LAUNCH3_RESPONSE" | grep -q '"success":true' && ! echo "$LAUNCH3_RESPONSE" | grep -q '"already_active":true'; then
    print_success "Browser launched successfully after close"
else
    print_error "Failed to launch browser after close"
    echo "Response: $LAUNCH3_RESPONSE"
    exit 1
fi

# Test 13: Close browser again
print_info "Closing browser again..."
CLOSE2_RESPONSE=$(curl -s -X POST "$API_BASE/profiles/$PROFILE_ID/close-browser")
if echo "$CLOSE2_RESPONSE" | grep -q '"success":true'; then
    print_success "Browser closed successfully (second time)"
else
    print_error "Failed to close browser (second time)"
    exit 1
fi

# Test 14: Clean up - delete test profile
print_info "Cleaning up test profile..."
DELETE_RESPONSE=$(curl -s -X DELETE "$API_BASE/profiles/$PROFILE_ID")
if echo "$DELETE_RESPONSE" | grep -q '"message":"Profile deleted successfully"'; then
    print_success "Test profile deleted successfully"
else
    print_error "Failed to delete test profile"
    echo "Response: $DELETE_RESPONSE"
fi

print_success "🎉 All Facebook workflow tests passed!"
echo ""
print_info "Facebook Login Workflow Summary:"
print_info "✅ Profile creation"
print_info "✅ Browser status checking"
print_info "✅ Browser launch"
print_info "✅ Duplicate launch detection"
print_info "✅ Facebook page opening"
print_info "✅ Complete login endpoint"
print_info "✅ Browser persistence during workflow"
print_info "✅ Browser close"
print_info "✅ Status tracking"
print_info "✅ Re-launch after close"
print_info "✅ Profile cleanup"
echo ""
print_info "The Facebook login workflow is working correctly!"
print_info "Users can now:"
print_info "1. Launch browser"
print_info "2. Open Facebook and login manually"
print_info "3. Complete login to save cookies"
print_info "4. Browser stays open for continued use"
print_info "5. Close browser when done"
print_info "6. Re-launch browser anytime"
