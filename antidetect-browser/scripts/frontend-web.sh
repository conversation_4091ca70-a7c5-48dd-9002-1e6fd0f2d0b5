#!/bin/bash

# Facebook Automation Desktop - Frontend Web Only
# This script runs only the frontend web server (assumes backend is already running)

set -e

echo "🌐 Starting Frontend Web Development Server..."

# Get the script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Change to frontend directory
cd "$PROJECT_ROOT/frontend"

echo "📁 Frontend directory: $PROJECT_ROOT/frontend"

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if backend is running
print_info "Checking if backend is running..."
if curl -s http://127.0.0.1:8000/health > /dev/null 2>&1; then
    print_success "Backend is running"
else
    print_warning "Backend is not running. Please start backend first:"
    print_info "cd backend && source venv/bin/activate && python -m uvicorn main:app --reload --port 8000"
    exit 1
fi

print_success "🚀 Starting frontend web server..."
print_info "Frontend: http://localhost:3000"
print_info "Backend API: http://localhost:8000"
print_info "API Docs: http://localhost:8000/docs"
print_warning "Press Ctrl+C to stop"

# Start webpack dev server
npm run serve:web
