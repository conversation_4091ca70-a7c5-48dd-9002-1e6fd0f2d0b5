#!/bin/bash

# Quick development script for Facebook Automation Desktop Application
# Simplified version of run-local.sh for daily development

set -e

echo "⚡ Quick Dev Start - Facebook Automation Desktop"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Get directories
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"

# Quick checks
if [ ! -d "$PROJECT_DIR/backend/venv" ]; then
    print_error "Setup not complete. Run: ./scripts/setup-local.sh"
    exit 1
fi

if [ ! -d "$PROJECT_DIR/frontend/node_modules" ]; then
    print_error "Frontend deps missing. Run: cd frontend && npm install"
    exit 1
fi

# Cleanup function
cleanup() {
    print_info "Stopping development servers..."
    pkill -f "uvicorn main:app" 2>/dev/null || true
    pkill -f "npm run dev" 2>/dev/null || true
    pkill -f "electron" 2>/dev/null || true
    print_success "Stopped"
    exit 0
}

trap cleanup SIGINT SIGTERM

# Parse simple arguments
case "${1:-both}" in
    "backend"|"be")
        MODE="backend"
        ;;
    "frontend"|"fe")
        MODE="frontend"
        ;;
    "both"|"all"|"")
        MODE="both"
        ;;
    "stop")
        cleanup
        ;;
    *)
        echo "Usage: $0 [backend|frontend|both|stop]"
        echo "  backend/be  - Start only backend"
        echo "  frontend/fe - Start only frontend"
        echo "  both/all    - Start both (default)"
        echo "  stop        - Stop all services"
        exit 1
        ;;
esac

print_info "Starting in $MODE mode..."

# Start backend
if [ "$MODE" = "backend" ] || [ "$MODE" = "both" ]; then
    print_info "🐍 Starting backend..."
    cd "$PROJECT_DIR/backend"
    source venv/bin/activate
    
    # Quick Redis check
    if ! pgrep redis-server > /dev/null; then
        print_warning "Redis not running, starting..."
        redis-server --daemonize yes 2>/dev/null || print_warning "Could not start Redis"
    fi
    
    # Start backend
    uvicorn main:app --reload --host 0.0.0.0 --port 8000 &
    BACKEND_PID=$!
    sleep 2
    
    if kill -0 $BACKEND_PID 2>/dev/null; then
        print_success "Backend running: http://localhost:8000"
    else
        print_error "Backend failed to start"
        exit 1
    fi
fi

# Start frontend
if [ "$MODE" = "frontend" ] || [ "$MODE" = "both" ]; then
    print_info "⚛️  Starting frontend..."
    cd "$PROJECT_DIR/frontend"
    
    # Set API URL
    export REACT_APP_API_URL="http://localhost:8000"
    export REACT_APP_WS_URL="ws://localhost:8000"
    
    # Start frontend
    npm run dev &
    FRONTEND_PID=$!
    sleep 3
    
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        print_success "Frontend running (Electron app should open)"
    else
        print_error "Frontend failed to start"
        exit 1
    fi
fi

# Show status
echo ""
print_success "🚀 Development environment ready!"
echo ""
if [ "$MODE" = "backend" ] || [ "$MODE" = "both" ]; then
    echo "  📡 Backend API: http://localhost:8000"
    echo "  📚 API Docs: http://localhost:8000/docs"
fi
if [ "$MODE" = "frontend" ] || [ "$MODE" = "both" ]; then
    echo "  🖥️  Desktop App: Electron window"
fi
echo ""
print_info "Press Ctrl+C to stop all services"

# Keep running
while true; do
    sleep 1
done
