#!/bin/bash

# Facebook Automation Desktop - Test Cookie Persistence
# This script tests cookie saving and loading functionality

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# API base URL
API_BASE="http://127.0.0.1:8000/api"

echo "🍪 Testing Cookie Persistence Workflow..."

# Test 1: Create a test profile
print_info "Creating test profile for cookie persistence..."
CREATE_RESPONSE=$(curl -s -X POST "$API_BASE/profiles/" \
    -H "Content-Type: application/json" \
    -d '{
        "name": "Cookie Persistence Test",
        "proxy_type": "no_proxy"
    }')

if echo "$CREATE_RESPONSE" | grep -q '"id"'; then
    PROFILE_ID=$(echo "$CREATE_RESPONSE" | grep -o '"id":[0-9]*' | cut -d':' -f2)
    print_success "Test profile created (ID: $PROFILE_ID)"
else
    print_error "Failed to create test profile"
    exit 1
fi

# Test 2: Open Facebook first time (no cookies)
print_info "Opening Facebook first time (no cookies expected)..."
FB1_RESPONSE=$(curl -s -X POST "$API_BASE/profiles/$PROFILE_ID/open-facebook")
if echo "$FB1_RESPONSE" | grep -q '"success":true'; then
    if echo "$FB1_RESPONSE" | grep -q '"cookies_loaded":false'; then
        print_success "First open: No cookies loaded (correct)"
    fi
    if echo "$FB1_RESPONSE" | grep -q '"logged_in":false'; then
        print_success "First open: Not logged in (correct)"
    fi
else
    print_error "Failed to open Facebook first time"
    exit 1
fi

# Test 3: Get profile details to check path
print_info "Getting profile details..."
PROFILE_RESPONSE=$(curl -s "$API_BASE/profiles/$PROFILE_ID")
if echo "$PROFILE_RESPONSE" | grep -q '"id"'; then
    print_success "Profile details retrieved"
else
    print_error "Failed to get profile details"
    exit 1
fi

# Test 4: Create fake Facebook cookies for testing
print_info "Creating fake Facebook cookies for testing..."
PROFILE_PATH="/tmp/profiles/Cookie Persistence Test_$(date +%s)"
mkdir -p "$PROFILE_PATH"

# Create realistic Facebook cookies
cat > "$PROFILE_PATH/facebook_cookies.json" << 'EOF'
[
  {
    "name": "c_user",
    "value": "100000000000000",
    "domain": ".facebook.com",
    "path": "/",
    "httpOnly": false,
    "secure": true,
    "sameSite": "None"
  },
  {
    "name": "xs",
    "value": "test_session_token_12345",
    "domain": ".facebook.com", 
    "path": "/",
    "httpOnly": true,
    "secure": true,
    "sameSite": "None"
  },
  {
    "name": "fr",
    "value": "test_fr_token_67890",
    "domain": ".facebook.com",
    "path": "/",
    "httpOnly": true,
    "secure": true,
    "sameSite": "None"
  }
]
EOF

print_success "Fake Facebook cookies created at $PROFILE_PATH/facebook_cookies.json"

# Test 5: Close browser to test reload
print_info "Closing browser to test cookie reload..."
CLOSE_RESPONSE=$(curl -s -X POST "$API_BASE/profiles/$PROFILE_ID/close-browser")
if echo "$CLOSE_RESPONSE" | grep -q '"success":true'; then
    print_success "Browser closed successfully"
else
    print_warning "Browser close may have failed (might not be open)"
fi

# Test 6: Open Facebook again (should load cookies)
print_info "Opening Facebook again (should load fake cookies)..."
FB2_RESPONSE=$(curl -s -X POST "$API_BASE/profiles/$PROFILE_ID/open-facebook")
if echo "$FB2_RESPONSE" | grep -q '"success":true'; then
    print_success "Facebook opened successfully on second attempt"
    
    # Check if cookies were loaded
    if echo "$FB2_RESPONSE" | grep -q '"cookies_loaded":true'; then
        print_success "Cookies were loaded successfully!"
    else
        print_warning "Cookies were not loaded (check cookie file path)"
        echo "Response: $FB2_RESPONSE"
    fi
    
    # Check login status
    if echo "$FB2_RESPONSE" | grep -q '"logged_in":true'; then
        print_success "Auto-login successful with cookies!"
    else
        print_info "Not auto-logged in (cookies may be invalid/expired)"
    fi
else
    print_error "Failed to open Facebook second time"
    echo "Response: $FB2_RESPONSE"
    exit 1
fi

# Test 7: Test complete login to save real cookies
print_info "Testing complete login to save cookies..."
LOGIN_RESPONSE=$(curl -s -X POST "$API_BASE/profiles/$PROFILE_ID/complete-login")
if echo "$LOGIN_RESPONSE" | grep -q '"success"'; then
    print_success "Complete login endpoint works"
    
    if echo "$LOGIN_RESPONSE" | grep -q '"logged_in":true'; then
        print_success "Login completed and cookies saved!"
    else
        print_info "Login not completed (expected since we're using fake cookies)"
    fi
else
    print_error "Complete login endpoint failed"
    echo "Response: $LOGIN_RESPONSE"
fi

# Test 8: Check if cookies file was updated
print_info "Checking if cookies file exists..."
if [ -f "$PROFILE_PATH/facebook_cookies.json" ]; then
    print_success "Facebook cookies file exists"
    COOKIE_COUNT=$(cat "$PROFILE_PATH/facebook_cookies.json" | grep -o '"name"' | wc -l)
    print_info "Number of cookies in file: $COOKIE_COUNT"
else
    print_warning "Facebook cookies file not found at expected location"
fi

# Test 9: Final cleanup
print_info "Cleaning up..."
curl -s -X POST "$API_BASE/profiles/$PROFILE_ID/close-browser" > /dev/null
rm -rf "$PROFILE_PATH" 2>/dev/null || true

DELETE_RESPONSE=$(curl -s -X DELETE "$API_BASE/profiles/$PROFILE_ID")
if echo "$DELETE_RESPONSE" | grep -q '"message":"Profile deleted successfully"'; then
    print_success "Test profile deleted successfully"
else
    print_warning "Failed to delete test profile (manual cleanup may be needed)"
fi

print_success "🎉 Cookie persistence test completed!"
echo ""
print_info "Cookie Persistence Summary:"
print_info "✅ Profile creation"
print_info "✅ First Facebook open (no cookies)"
print_info "✅ Fake cookie creation"
print_info "✅ Browser close/reopen cycle"
print_info "✅ Cookie loading detection"
print_info "✅ Complete login endpoint"
print_info "✅ Cookie file management"
print_info "✅ Cleanup"
echo ""
print_info "Next steps for real testing:"
print_info "1. Open Facebook in browser"
print_info "2. Login manually to Facebook"
print_info "3. Click 'Complete Login' to save real cookies"
print_info "4. Close browser"
print_info "5. Open Facebook again - should auto-login!"
