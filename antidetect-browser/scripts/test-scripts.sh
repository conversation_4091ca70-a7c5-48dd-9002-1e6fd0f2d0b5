#!/bin/bash

# Test script to verify all scripts are working correctly
# Facebook Automation Desktop Application

set -e

echo "🧪 Testing Facebook Automation Desktop Scripts"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_info() { echo -e "${BLUE}[TEST]${NC} $1"; }
print_success() { echo -e "${GREEN}[PASS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARN]${NC} $1"; }
print_error() { echo -e "${RED}[FAIL]${NC} $1"; }

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Test results
TESTS_PASSED=0
TESTS_FAILED=0

# Test function
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    print_info "Testing: $test_name"
    
    if eval "$test_command" >/dev/null 2>&1; then
        print_success "$test_name"
        ((TESTS_PASSED++))
    else
        print_error "$test_name"
        ((TESTS_FAILED++))
    fi
}

# Test script existence and permissions
test_script_files() {
    print_info "Testing script files..."
    
    local scripts=(
        "setup-local.sh"
        "setup-local.bat"
        "build-local.sh"
        "build-local.bat"
        "setup-production.sh"
        "build-production.sh"
        "deploy.sh"
        "run-local.sh"
        "run-local.bat"
        "dev.sh"
        "dev.bat"
        "manage.sh"
        "make-executable.sh"
    )
    
    for script in "${scripts[@]}"; do
        if [ -f "$SCRIPT_DIR/$script" ]; then
            print_success "File exists: $script"
            ((TESTS_PASSED++))
            
            # Test if .sh files are executable
            if [[ "$script" == *.sh ]]; then
                if [ -x "$SCRIPT_DIR/$script" ]; then
                    print_success "Executable: $script"
                    ((TESTS_PASSED++))
                else
                    print_error "Not executable: $script"
                    ((TESTS_FAILED++))
                fi
            fi
        else
            print_error "Missing file: $script"
            ((TESTS_FAILED++))
        fi
    done
}

# Test script help functions
test_help_functions() {
    print_info "Testing help functions..."
    
    local scripts_with_help=(
        "setup-local.sh --help"
        "build-local.sh --help"
        "setup-production.sh --help"
        "build-production.sh --help"
        "deploy.sh --help"
        "run-local.sh --help"
        "manage.sh --help"
    )
    
    for script_cmd in "${scripts_with_help[@]}"; do
        script_name=$(echo "$script_cmd" | cut -d' ' -f1)
        if timeout 10s "$SCRIPT_DIR/$script_cmd" >/dev/null 2>&1; then
            print_success "Help function: $script_name"
            ((TESTS_PASSED++))
        else
            print_warning "Help function issue: $script_name"
            ((TESTS_FAILED++))
        fi
    done
}

# Test project structure
test_project_structure() {
    print_info "Testing project structure..."
    
    local required_dirs=(
        "backend"
        "frontend"
        "scripts"
    )
    
    local required_files=(
        "backend/main.py"
        "backend/requirements.txt"
        "frontend/package.json"
    )
    
    for dir in "${required_dirs[@]}"; do
        if [ -d "$PROJECT_DIR/$dir" ]; then
            print_success "Directory exists: $dir"
            ((TESTS_PASSED++))
        else
            print_error "Missing directory: $dir"
            ((TESTS_FAILED++))
        fi
    done
    
    for file in "${required_files[@]}"; do
        if [ -f "$PROJECT_DIR/$file" ]; then
            print_success "File exists: $file"
            ((TESTS_PASSED++))
        else
            print_error "Missing file: $file"
            ((TESTS_FAILED++))
        fi
    done
}

# Test script syntax
test_script_syntax() {
    print_info "Testing script syntax..."
    
    # Test bash scripts
    for script in "$SCRIPT_DIR"/*.sh; do
        if [ -f "$script" ]; then
            script_name=$(basename "$script")
            if bash -n "$script" 2>/dev/null; then
                print_success "Syntax OK: $script_name"
                ((TESTS_PASSED++))
            else
                print_error "Syntax error: $script_name"
                ((TESTS_FAILED++))
            fi
        fi
    done
}

# Test manage.sh commands
test_manage_commands() {
    print_info "Testing manage.sh commands..."
    
    local commands=(
        "help"
        "status"
    )
    
    for cmd in "${commands[@]}"; do
        if timeout 10s "$SCRIPT_DIR/manage.sh" "$cmd" >/dev/null 2>&1; then
            print_success "Manage command: $cmd"
            ((TESTS_PASSED++))
        else
            print_warning "Manage command issue: $cmd"
            ((TESTS_FAILED++))
        fi
    done
}

# Test environment detection
test_environment_detection() {
    print_info "Testing environment detection..."
    
    # Test OS detection in scripts
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        print_success "OS detected: Linux"
        ((TESTS_PASSED++))
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        print_success "OS detected: macOS"
        ((TESTS_PASSED++))
    elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
        print_success "OS detected: Windows"
        ((TESTS_PASSED++))
    else
        print_warning "Unknown OS: $OSTYPE"
        ((TESTS_FAILED++))
    fi
    
    # Test command availability
    local commands=(
        "bash"
        "chmod"
        "find"
    )
    
    for cmd in "${commands[@]}"; do
        if command -v "$cmd" >/dev/null 2>&1; then
            print_success "Command available: $cmd"
            ((TESTS_PASSED++))
        else
            print_error "Command missing: $cmd"
            ((TESTS_FAILED++))
        fi
    done
}

# Test documentation
test_documentation() {
    print_info "Testing documentation..."
    
    if [ -f "$SCRIPT_DIR/README.md" ]; then
        print_success "README.md exists"
        ((TESTS_PASSED++))
        
        # Check if README contains key sections
        local sections=(
            "Setup Scripts"
            "Build Scripts"
            "Run Scripts"
            "Deploy Script"
        )
        
        for section in "${sections[@]}"; do
            if grep -q "$section" "$SCRIPT_DIR/README.md"; then
                print_success "README section: $section"
                ((TESTS_PASSED++))
            else
                print_warning "README missing section: $section"
                ((TESTS_FAILED++))
            fi
        done
    else
        print_error "README.md missing"
        ((TESTS_FAILED++))
    fi
}

# Test quick functionality (dry run)
test_quick_functionality() {
    print_info "Testing quick functionality (dry run)..."
    
    # Test make-executable
    if "$SCRIPT_DIR/make-executable.sh" >/dev/null 2>&1; then
        print_success "make-executable.sh works"
        ((TESTS_PASSED++))
    else
        print_error "make-executable.sh failed"
        ((TESTS_FAILED++))
    fi
    
    # Test dev.sh stop command
    if timeout 5s "$SCRIPT_DIR/dev.sh" stop >/dev/null 2>&1; then
        print_success "dev.sh stop works"
        ((TESTS_PASSED++))
    else
        print_warning "dev.sh stop issue"
        ((TESTS_FAILED++))
    fi
}

# Main test execution
main() {
    print_info "Starting script tests..."
    echo ""
    
    test_script_files
    echo ""
    
    test_project_structure
    echo ""
    
    test_script_syntax
    echo ""
    
    test_help_functions
    echo ""
    
    test_manage_commands
    echo ""
    
    test_environment_detection
    echo ""
    
    test_documentation
    echo ""
    
    test_quick_functionality
    echo ""
    
    # Show results
    print_info "Test Results:"
    echo "  ✅ Passed: $TESTS_PASSED"
    echo "  ❌ Failed: $TESTS_FAILED"
    echo "  📊 Total: $((TESTS_PASSED + TESTS_FAILED))"
    echo ""
    
    if [ $TESTS_FAILED -eq 0 ]; then
        print_success "🎉 All tests passed!"
        echo ""
        print_info "Scripts are ready to use:"
        echo "  ./scripts/setup-local.sh    # Setup development"
        echo "  ./scripts/run-local.sh      # Run application"
        echo "  ./scripts/manage.sh         # Unified management"
        echo ""
        exit 0
    else
        print_error "❌ Some tests failed!"
        echo ""
        print_info "Please check the failed tests and fix issues before using scripts."
        echo ""
        exit 1
    fi
}

# Run tests
main "$@"
