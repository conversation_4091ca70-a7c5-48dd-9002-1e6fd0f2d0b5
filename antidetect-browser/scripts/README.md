# Facebook Automation Desktop Application - Scripts

Bộ script tự động hóa cho việc setup, build và deploy ứng dụng Facebook Automation Desktop.

## 📁 Cấu trúc Scripts

```
scripts/
├── setup-local-desktop.sh  # Setup desktop app với Docker services (Linux/macOS) ⭐ MỚI
├── setup-local-desktop.bat # Setup desktop app với Docker services (Windows) ⭐ MỚI
├── run-local-desktop.sh    # Run desktop app với Docker PostgreSQL/Redis (Linux/macOS) ⭐ MỚI
├── run-local-desktop.bat   # Run desktop app với Docker PostgreSQL/Redis (Windows) ⭐ MỚI
├── setup-local.sh          # Setup môi trường development (Linux/macOS)
├── setup-local.bat         # Setup môi trường development (Windows)
├── build-local.sh          # Build cho development (Linux/macOS)
├── build-local.bat         # Build cho development (Windows)
├── setup-production.sh     # Setup môi trường production
├── build-production.sh     # Build cho production
├── deploy.sh               # Script deploy tự động
├── run-local.sh            # Run project trên local (Linux/macOS)
├── run-local.bat           # Run project trên local (Windows)
├── dev.sh                  # Quick dev start (Linux/macOS)
├── dev.bat                 # Quick dev start (Windows)
├── manage.sh               # Script quản lý tổng hợp
├── make-executable.sh      # Make scripts executable
└── README.md               # Tài liệu này
```

## 🚀 Quick Start

### Desktop Application (Khuyến nghị) ⭐

#### Linux/macOS
```bash
# Make scripts executable
./scripts/make-executable.sh

# Setup desktop app với Docker services
./scripts/setup-local-desktop.sh

# Run desktop application
./scripts/run-local-desktop.sh

# Hoặc chỉ chạy backend với Docker services
./scripts/run-local-desktop.sh --backend-only

# Hoặc chỉ chạy frontend (cần backend đang chạy)
./scripts/run-local-desktop.sh --frontend-only
```

#### Windows
```cmd
# Setup desktop app với Docker services
scripts\setup-local-desktop.bat

# Run desktop application
scripts\run-local-desktop.bat

# Hoặc chỉ chạy backend với Docker services
scripts\run-local-desktop.bat --backend-only

# Hoặc chỉ chạy frontend (cần backend đang chạy)
scripts\run-local-desktop.bat --frontend-only
```

### Traditional Development Environment

#### Linux/macOS
```bash
# Make scripts executable
./scripts/make-executable.sh

# Setup môi trường development
./scripts/setup-local.sh

# Run application
./scripts/run-local.sh
# Or quick start
./scripts/dev.sh

# Build application
./scripts/build-local.sh
```

#### Windows
```cmd
# Setup môi trường development
scripts\setup-local.bat

# Run application
scripts\run-local.bat
# Or quick start
scripts\dev.bat

# Build application
scripts\build-local.bat
```

### Production Environment

```bash
# Setup production server
sudo ./scripts/setup-production.sh

# Build cho production
./scripts/build-production.sh

# Deploy tự động
./scripts/deploy.sh local traditional
```

### Quick Management (Unified Interface)

```bash
# Use manage.sh for all operations
./scripts/manage.sh setup dev      # Setup development
./scripts/manage.sh run            # Run application
./scripts/manage.sh run backend    # Run only backend
./scripts/manage.sh run frontend   # Run only frontend
./scripts/manage.sh build prod     # Build for production
./scripts/manage.sh deploy local   # Deploy locally
./scripts/manage.sh status         # Show status
./scripts/manage.sh stop all       # Stop all services
```

## 📋 Chi tiết Scripts

### 0. Desktop Application Scripts (Khuyến nghị) ⭐

#### `setup-local-desktop.sh` / `setup-local-desktop.bat`
**Mục đích**: Thiết lập desktop application với Docker services

**Chức năng**:
- Kiểm tra Docker và Docker Compose
- Kiểm tra Python 3.8+ và Node.js 16+
- Tạo Python virtual environment cho backend
- Cài đặt backend dependencies (FastAPI, Camoufox, etc.)
- Cài đặt frontend dependencies (Electron, React, etc.)
- Tạo Docker environment file (.env.product)
- Test Docker services (PostgreSQL, Redis)
- Tạo các thư mục cần thiết

**Sử dụng**:
```bash
./scripts/setup-local-desktop.sh [OPTIONS]

Options:
  --skip-docker       Bỏ qua Docker setup và testing
  --skip-backend      Bỏ qua backend setup
  --skip-frontend     Bỏ qua frontend setup
  --test-only         Chỉ test existing setup
  --help              Hiển thị help

Examples:
  ./scripts/setup-local-desktop.sh                    # Full setup (khuyến nghị)
  ./scripts/setup-local-desktop.sh --skip-docker      # Setup không có Docker testing
  ./scripts/setup-local-desktop.sh --test-only        # Test existing setup
```

#### `run-local-desktop.sh` / `run-local-desktop.bat`
**Mục đích**: Chạy desktop application với Docker services

**Chức năng**:
- Khởi động PostgreSQL và Redis containers (Docker Compose)
- Chạy FastAPI backend server với database connectivity
- Chạy Electron desktop application
- Monitor tất cả services và auto-restart
- Cleanup tự động khi dừng (Ctrl+C)

**Kiến trúc**:
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Electron App  │    │   FastAPI        │    │   Docker        │
│   (Frontend)    │◄──►│   Backend        │◄──►│   Services      │
│   Desktop UI    │    │   Port: 8000     │    │   PostgreSQL    │
│                 │    │                  │    │   Redis         │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

**Sử dụng**:
```bash
./scripts/run-local-desktop.sh [OPTIONS]

Options:
  --backend-only      Chỉ start backend với Docker services
  --frontend-only     Chỉ start frontend app (cần backend đang chạy)
  --port PORT         Custom backend port (default: 8000)
  --status            Show service status
  --stop              Stop tất cả services
  --help              Hiển thị help

Examples:
  ./scripts/run-local-desktop.sh                      # Start tất cả (khuyến nghị)
  ./scripts/run-local-desktop.sh --backend-only       # Backend + Docker services only
  ./scripts/run-local-desktop.sh --frontend-only      # Electron app only
  ./scripts/run-local-desktop.sh --port 8080          # Custom backend port
  ./scripts/run-local-desktop.sh --status             # Show service status
  ./scripts/run-local-desktop.sh --stop               # Stop tất cả services
```

**Services được quản lý**:
- 🐘 **PostgreSQL**: Database (Docker container, port 5432)
- 🔴 **Redis**: Cache/Queue (Docker container, port 6379)
- 📡 **FastAPI Backend**: API server (local process, port 8000)
- 🖥️ **Electron Frontend**: Desktop app (local process)

### 1. Setup Scripts (Traditional)

#### `setup-local.sh` / `setup-local.bat`
**Mục đích**: Thiết lập môi trường development

**Chức năng**:
- Kiểm tra Node.js và Python
- Tạo Python virtual environment
- Cài đặt dependencies (frontend + backend)
- Setup database (SQLite cho development)
- Cài đặt Playwright browsers
- Tạo file environment (.env)

**Sử dụng**:
```bash
./scripts/setup-local.sh
```

#### `setup-production.sh`
**Mục đích**: Thiết lập môi trường production

**Chức năng**:
- Cài đặt system dependencies
- Tạo service user
- Setup PostgreSQL database
- Cấu hình Redis
- Tạo systemd services
- Cấu hình Nginx
- Setup SSL/TLS (tùy chọn)

**Sử dụng**:
```bash
sudo ./scripts/setup-production.sh [OPTIONS]

Options:
  --skip-deps     Bỏ qua cài đặt system dependencies
  --skip-db       Bỏ qua setup database
  --help          Hiển thị help
```

### 2. Build Scripts

#### `build-local.sh` / `build-local.bat`
**Mục đích**: Build application cho development

**Chức năng**:
- Clean previous builds
- Build React frontend
- Build FastAPI backend
- Tạo executable files
- Package Electron app (tùy chọn)

**Sử dụng**:
```bash
./scripts/build-local.sh [OPTIONS]

Options:
  --clean         Clean builds trước khi build
  --with-tests    Chạy tests trong quá trình build
  --package       Package Electron application
  --help          Hiển thị help
```

#### `build-production.sh`
**Mục đích**: Build application cho production

**Chức năng**:
- Build optimized frontend
- Build backend với production dependencies
- Tạo Docker images
- Tạo deployment packages
- Generate documentation

**Sử dụng**:
```bash
./scripts/build-production.sh [OPTIONS]

Options:
  --with-tests    Chạy tests trong quá trình build
  --no-clean      Không clean previous builds
  --no-docker     Bỏ qua Docker configuration
  --help          Hiển thị help
```

### 3. Run Scripts

#### `run-local.sh` / `run-local.bat`
**Mục đích**: Chạy application trên local development

**Chức năng**:
- Start FastAPI backend server
- Start React/Electron frontend
- Monitor services và auto-restart
- Port checking và conflict resolution
- Database migration tự động
- Redis service checking

**Sử dụng**:
```bash
./scripts/run-local.sh [OPTIONS]

Options:
  --backend-only      Chỉ start backend
  --frontend-only     Chỉ start frontend
  --port PORT         Custom backend port
  --no-db             Bỏ qua database setup
  --status            Show service status
  --stop              Stop all services
  --help              Show help

Examples:
  ./scripts/run-local.sh                    # Start both
  ./scripts/run-local.sh --backend-only     # Backend only
  ./scripts/run-local.sh --port 8080        # Custom port
```

#### `dev.sh` / `dev.bat`
**Mục đích**: Quick start cho daily development

**Chức năng**:
- Simplified version của run-local
- Fast startup với minimal checks
- Easy mode switching

**Sử dụng**:
```bash
./scripts/dev.sh [MODE]

Modes:
  backend/be      Start only backend
  frontend/fe     Start only frontend
  both/all        Start both (default)
  stop            Stop all services

Examples:
  ./scripts/dev.sh              # Start both
  ./scripts/dev.sh backend      # Backend only
  ./scripts/dev.sh stop         # Stop all
```

### 4. Deploy Script

#### `deploy.sh`
**Mục đích**: Deploy application tự động

**Chức năng**:
- Deploy local hoặc remote
- Hỗ trợ multiple deployment methods
- Docker và Kubernetes support
- Automated rollback (tùy chọn)

**Sử dụng**:
```bash
./scripts/deploy.sh TARGET METHOD [OPTIONS]

Targets:
  local       Deploy to local machine
  remote      Deploy to remote server
  docker      Deploy using Docker
  k8s         Deploy to Kubernetes

Methods:
  traditional Use systemd services
  docker      Use Docker containers
  compose     Use Docker Compose

Options:
  -h, --host HOST     Remote host
  -u, --user USER     Remote user
  -p, --path PATH     Remote path
  -b, --build PKG     Build package
  --help              Show help

Examples:
  ./scripts/deploy.sh local traditional
  ./scripts/deploy.sh remote docker -h server.com -u root
  ./scripts/deploy.sh docker compose
```

## 🔧 Configuration

### Environment Variables

#### Development (.env files)
```bash
# Backend
DATABASE_URL=sqlite:///./app.db
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=true

# Frontend
REACT_APP_API_URL=http://localhost:8000
NODE_ENV=development
```

#### Production
```bash
# Backend
DATABASE_URL=postgresql://user:pass@localhost/db
API_HOST=127.0.0.1
API_PORT=8000
DEBUG=false
SECRET_KEY=your-secret-key

# Frontend
REACT_APP_API_URL=/api
NODE_ENV=production
```

### System Requirements

#### Development
- Node.js 16+
- Python 3.8+
- Git

#### Production
- Ubuntu 20.04+ / CentOS 8+
- 2GB+ RAM
- 10GB+ disk space
- Root access

## 🐳 Docker Support

### Development
```bash
# Build và chạy với Docker
docker-compose up -d
```

### Production
```bash
# Build production images
./scripts/build-production.sh

# Deploy với Docker Compose
./scripts/deploy.sh docker compose
```

## 🔍 Troubleshooting

### Common Issues

1. **Permission denied**
   ```bash
   chmod +x scripts/*.sh
   ```

2. **Node.js version issues**
   ```bash
   # Install Node.js 16+
   curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
   sudo apt-get install -y nodejs
   ```

3. **Python virtual environment issues**
   ```bash
   # Recreate venv
   rm -rf backend/venv
   python3 -m venv backend/venv
   ```

4. **Database connection issues**
   ```bash
   # Check PostgreSQL status
   sudo systemctl status postgresql
   
   # Reset database
   sudo -u postgres dropdb facebook_automation
   sudo -u postgres createdb facebook_automation
   ```

### Logs

#### Development
```bash
# Backend logs
cd backend && source venv/bin/activate && python main.py

# Frontend logs
cd frontend && npm run dev
```

#### Production
```bash
# System logs
sudo journalctl -u facebook-automation-backend -f
sudo journalctl -u facebook-automation-worker -f

# Application logs
sudo tail -f /var/log/facebook-automation/backend.log
```

## 📞 Support

Nếu gặp vấn đề với scripts:

1. Kiểm tra logs chi tiết
2. Verify system requirements
3. Check file permissions
4. Review environment variables

## 🔄 Updates

Để update scripts:

```bash
# Pull latest changes
git pull origin main

# Re-run setup if needed
./scripts/setup-local.sh
```

---

**Lưu ý**: Luôn backup dữ liệu trước khi chạy production scripts!
