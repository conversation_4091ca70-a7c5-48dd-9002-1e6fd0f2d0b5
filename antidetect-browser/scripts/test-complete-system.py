#!/usr/bin/env python3
"""
Complete System Test for Facebook Automation Desktop
Tests all implemented features from Phase 1 and Phase 2
"""

import asyncio
import sys
import os
import json
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent.parent / "backend"
sys.path.insert(0, str(backend_path))

async def test_phase1_profile_management():
    """Test Phase 1: Profile Management"""
    print("🔧 Testing Phase 1: Profile Management...")
    
    try:
        # Test profile manager
        from app.services.profile_manager import AntidetectProfileManager
        from app.services.fingerprint_generator import FingerprintGenerator
        
        manager = AntidetectProfileManager()
        fingerprint_gen = FingerprintGenerator()
        
        # Test fingerprint generation
        fingerprint = await fingerprint_gen.generate_fingerprint()
        assert 'user_agent' in fingerprint
        assert 'viewport' in fingerprint
        print("  ✅ Fingerprint generation working")
        
        # Test profile creation (mock)
        profile_data = {
            'name': 'test_profile',
            'proxy_config': {
                'type': 'no_proxy',
                'host': None,
                'port': None
            }
        }
        
        result = await manager.create_profile(profile_data)
        assert result['success'] == True
        print("  ✅ Profile creation working")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Phase 1 test failed: {e}")
        return False

async def test_phase2_scraping_module():
    """Test Phase 2: Scraping Module"""
    print("🕷️ Testing Phase 2: Scraping Module...")
    
    try:
        # Test Facebook scraper initialization
        from app.services.facebook_scraper import FacebookScraper
        from app.services.scraping_task_manager import ScrapingTaskManager
        from app.services.export_service import ExportService
        from app.services.performance_optimizer import PerformanceOptimizer
        
        scraper = FacebookScraper()
        task_manager = ScrapingTaskManager()
        export_service = ExportService()
        optimizer = PerformanceOptimizer()
        
        # Test extraction strategies
        assert 'comments' in scraper.extraction_strategies
        assert 'likes' in scraper.extraction_strategies
        assert 'shares' in scraper.extraction_strategies
        print("  ✅ Extraction strategies initialized")
        
        # Test task manager
        assert hasattr(task_manager, 'create_scraping_task')
        assert hasattr(task_manager, 'start_scraping_task')
        print("  ✅ Task manager working")
        
        # Test export service
        assert hasattr(export_service, 'export_scraping_results')
        print("  ✅ Export service working")
        
        # Test performance optimizer
        stats = optimizer.get_performance_stats()
        assert 'connection_pool' in stats
        print("  ✅ Performance optimizer working")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Phase 2 test failed: {e}")
        return False

async def test_database_models():
    """Test database models and schema"""
    print("🗄️ Testing Database Models...")
    
    try:
        from app.core.database import init_db
        from app.models.profile import Profile
        from app.models.scraping import ScrapingTask, ScrapedUser
        from app.models.messaging import MessagingTask, MessageLog
        from app.models.system import SystemLog, SystemStats
        
        # Test database initialization
        await init_db()
        print("  ✅ Database initialization working")
        
        # Test model imports
        assert Profile is not None
        assert ScrapingTask is not None
        assert ScrapedUser is not None
        assert MessagingTask is not None
        assert MessageLog is not None
        assert SystemLog is not None
        assert SystemStats is not None
        print("  ✅ All database models imported successfully")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Database test failed: {e}")
        return False

async def test_api_routes():
    """Test API routes"""
    print("🌐 Testing API Routes...")
    
    try:
        from app.api.routes import profiles, scraping, messaging, system
        
        # Test route imports
        assert hasattr(profiles, 'router')
        assert hasattr(scraping, 'router')
        assert hasattr(messaging, 'router')
        assert hasattr(system, 'router')
        print("  ✅ All API routes imported successfully")
        
        return True
        
    except Exception as e:
        print(f"  ❌ API routes test failed: {e}")
        return False

async def test_crawl4ai_integration():
    """Test crawl4ai integration"""
    print("🕸️ Testing Crawl4ai Integration...")
    
    try:
        from crawl4ai import AsyncWebCrawler
        from crawl4ai.async_configs import BrowserConfig
        from crawl4ai.extraction_strategy import JsonCssExtractionStrategy
        
        # Test basic crawl4ai functionality
        config = BrowserConfig(headless=True)
        
        # Simple test without actually crawling
        assert AsyncWebCrawler is not None
        assert BrowserConfig is not None
        assert JsonCssExtractionStrategy is not None
        print("  ✅ Crawl4ai integration working")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Crawl4ai integration test failed: {e}")
        return False

async def test_configuration():
    """Test configuration and settings"""
    print("⚙️ Testing Configuration...")
    
    try:
        from app.core.config import settings
        from app.core.logger import setup_logger
        
        # Test settings
        assert settings.HOST is not None
        assert settings.PORT is not None
        assert settings.DATABASE_URL is not None
        print("  ✅ Configuration settings working")
        
        # Test logger
        logger = setup_logger("test")
        assert logger is not None
        print("  ✅ Logger setup working")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Configuration test failed: {e}")
        return False

async def generate_test_report(results):
    """Generate test report"""
    print("\n📊 Test Report:")
    print("=" * 50)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    failed_tests = total_tests - passed_tests
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<30} {status}")
    
    print("=" * 50)
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {failed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 All tests passed! System is ready for use.")
        return 0
    else:
        print(f"\n💥 {failed_tests} test(s) failed. Please check the issues above.")
        return 1

async def main():
    """Run all system tests"""
    print("🧪 Facebook Automation Desktop - Complete System Test")
    print("=" * 60)
    
    tests = {
        "Configuration": test_configuration,
        "Database Models": test_database_models,
        "API Routes": test_api_routes,
        "Crawl4ai Integration": test_crawl4ai_integration,
        "Phase 1 - Profile Management": test_phase1_profile_management,
        "Phase 2 - Scraping Module": test_phase2_scraping_module,
    }
    
    results = {}
    
    for test_name, test_func in tests.items():
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {e}")
            results[test_name] = False
        print()
    
    return await generate_test_report(results)

if __name__ == "__main__":
    exit_code = asyncio.run(main())
