@echo off
REM Build script for local development (Windows)
REM Facebook Automation Desktop Application

setlocal enabledelayedexpansion

echo 🔨 Building Facebook Automation Desktop Application for local development...

REM Function to print colored output
:print_status
echo [INFO] %~1
goto :eof

:print_success
echo [SUCCESS] %~1
goto :eof

:print_warning
echo [WARNING] %~1
goto :eof

:print_error
echo [ERROR] %~1
goto :eof

REM Clean previous builds
:clean_builds
call :print_status "Cleaning previous builds..."

REM Clean frontend build
if exist "frontend\dist" (
    rmdir /s /q "frontend\dist"
    call :print_status "Cleaned frontend dist directory"
)

REM Clean backend build
if exist "backend\dist" (
    rmdir /s /q "backend\dist"
    call :print_status "Cleaned backend dist directory"
)

REM Clean electron build
if exist "frontend\build" (
    rmdir /s /q "frontend\build"
    call :print_status "Cleaned electron build directory"
)

call :print_success "Build directories cleaned"
goto :eof

REM Build frontend
:build_frontend
call :print_status "Building frontend..."

cd frontend

REM Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    call :print_status "Installing frontend dependencies..."
    npm install
)

REM Build webpack bundle
call :print_status "Building webpack bundle..."
npm run webpack:build

call :print_success "Frontend build completed"

cd ..
goto :eof

REM Build backend
:build_backend
call :print_status "Building backend..."

cd backend

REM Activate virtual environment
if not exist "venv" (
    call :print_error "Virtual environment not found. Please run setup-local.bat first."
    exit /b 1
)

call venv\Scripts\activate.bat

REM Install dependencies if needed
call :print_status "Checking backend dependencies..."
pip install -r requirements.txt --quiet

REM Run tests (optional)
if "%~1"=="--with-tests" (
    call :print_status "Running backend tests..."
    if exist "pytest.ini" (
        python -m pytest tests\ -v || call :print_warning "Some tests failed"
    ) else if exist "tests" (
        python -m pytest tests\ -v || call :print_warning "Some tests failed"
    ) else (
        call :print_warning "No tests found to run"
    )
)

REM Create standalone executable
call :print_status "Creating backend executable..."

REM Install PyInstaller if not present
pip install pyinstaller --quiet

REM Build executable
pyinstaller --onefile ^
            --name facebook-automation-backend ^
            --distpath dist ^
            --workpath build ^
            --specpath . ^
            main.py

call :print_success "Backend build completed"

cd ..
goto :eof

REM Package Electron app
:package_electron
call :print_status "Packaging Electron application..."

cd frontend

REM Build Electron app
call :print_status "Building Electron package..."
npm run package

call :print_success "Electron application packaged"

cd ..
goto :eof

REM Verify builds
:verify_builds
call :print_status "Verifying builds..."

REM Check frontend build
if exist "frontend\dist" (
    dir /b "frontend\dist" >nul 2>&1
    if not errorlevel 1 (
        call :print_success "Frontend build verified"
    ) else (
        call :print_error "Frontend build failed or empty"
        exit /b 1
    )
) else (
    call :print_error "Frontend build failed or empty"
    exit /b 1
)

REM Check backend build
if exist "backend\dist\facebook-automation-backend.exe" (
    call :print_success "Backend build verified"
) else (
    call :print_error "Backend build failed"
    exit /b 1
)

REM Check Electron build
if exist "frontend\dist" (
    dir /b "frontend\dist\*.exe" >nul 2>&1
    if not errorlevel 1 (
        call :print_success "Electron package verified"
    ) else (
        call :print_warning "Electron package not found (this is normal for development builds)"
    )
)

call :print_success "Build verification completed"
goto :eof

REM Show build info
:show_build_info
call :print_status "Build Information:"
echo.

REM Frontend build info
if exist "frontend\dist" (
    for /f %%i in ('dir /s /-c "frontend\dist" ^| find "bytes"') do set FRONTEND_SIZE=%%i
    echo   📦 Frontend build: frontend\dist\
)

REM Backend build info
if exist "backend\dist\facebook-automation-backend.exe" (
    for %%i in ("backend\dist\facebook-automation-backend.exe") do set BACKEND_SIZE=%%~zi
    echo   🐍 Backend executable: backend\dist\facebook-automation-backend.exe
)

REM Electron build info
if exist "frontend\dist" (
    for /f %%i in ('dir /b "frontend\dist\*.exe" 2^>nul') do (
        echo   ⚡ Electron package: frontend\dist\%%i
    )
)

echo.
goto :eof

REM Main build function
:main
call :print_status "Starting local build process..."

REM Parse arguments
set WITH_TESTS=false
set CLEAN_FIRST=false
set PACKAGE_ELECTRON=false

:parse_args
if "%~1"=="" goto :start_build
if "%~1"=="--with-tests" (
    set WITH_TESTS=true
    shift
    goto :parse_args
)
if "%~1"=="--clean" (
    set CLEAN_FIRST=true
    shift
    goto :parse_args
)
if "%~1"=="--package" (
    set PACKAGE_ELECTRON=true
    shift
    goto :parse_args
)
if "%~1"=="--help" (
    echo Usage: %0 [OPTIONS]
    echo.
    echo Options:
    echo   --clean         Clean previous builds before building
    echo   --with-tests    Run tests during backend build
    echo   --package       Package Electron application
    echo   --help          Show this help message
    echo.
    exit /b 0
)
call :print_warning "Unknown option: %~1"
shift
goto :parse_args

:start_build
REM Clean if requested
if "%CLEAN_FIRST%"=="true" (
    call :clean_builds
)

REM Build components
call :build_frontend
if errorlevel 1 exit /b 1

if "%WITH_TESTS%"=="true" (
    call :build_backend --with-tests
) else (
    call :build_backend
)
if errorlevel 1 exit /b 1

REM Package Electron if requested
if "%PACKAGE_ELECTRON%"=="true" (
    call :package_electron
    if errorlevel 1 exit /b 1
)

REM Verify builds
call :verify_builds
if errorlevel 1 exit /b 1

REM Show build information
call :show_build_info

call :print_success "🎉 Local build completed successfully!"
echo.
call :print_status "To run the application:"
echo   1. Start backend: backend\dist\facebook-automation-backend.exe
echo   2. Start frontend: cd frontend ^&^& npm start
echo.

goto :eof

REM Run main function
call :main %*
