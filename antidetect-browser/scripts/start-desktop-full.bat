@echo off
setlocal enabledelayedexpansion

REM Facebook Automation Desktop - Full Application Startup Script (Windows)
REM This script runs the desktop app with the full main.py backend

echo [INFO] Starting Facebook Automation Desktop Application (Full Version)

REM Get script directory and project root
set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%SCRIPT_DIR%.."

echo [INFO] Project root: %PROJECT_ROOT%

REM Change to project root
cd /d "%PROJECT_ROOT%"

REM Check prerequisites
echo [INFO] Checking prerequisites...

python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is required but not installed
    pause
    exit /b 1
)

npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js and npm are required but not installed
    pause
    exit /b 1
)

REM Check Docker services (optional)
docker ps >nul 2>&1
if not errorlevel 1 (
    echo [SUCCESS] Docker services available
) else (
    echo [WARNING] Docker not available, using local services
)

REM Setup backend
echo [INFO] Setting up backend environment...
cd backend

REM Create virtual environment if it doesn't exist
if not exist "venv" (
    echo [INFO] Creating Python virtual environment...
    python -m venv venv
)

REM Activate virtual environment
call venv\Scripts\activate.bat

REM Install dependencies
echo [INFO] Installing backend dependencies...
if exist "requirements-minimal.txt" (
    pip install -r requirements-minimal.txt
) else (
    pip install -r requirements.txt
)

REM Create .env file if it doesn't exist
if not exist ".env" (
    echo [INFO] Creating backend .env file...
    (
        echo # Database Configuration
        echo DATABASE_URL=sqlite+aiosqlite:///./facebook_automation.db
        echo.
        echo # Redis Configuration ^(optional^)
        echo REDIS_URL=redis://localhost:6379/0
        echo.
        echo # API Configuration
        echo HOST=127.0.0.1
        echo PORT=8000
        echo DEBUG=true
        echo.
        echo # Security
        echo SECRET_KEY=desktop-dev-key-windows
        echo ACCESS_TOKEN_EXPIRE_MINUTES=30
        echo.
        echo # Application Settings
        echo MAX_CONCURRENT_BROWSERS=3
        echo BROWSER_TIMEOUT=30000
    ) > .env
    echo [SUCCESS] Created .env file with SQLite configuration
)

REM Initialize database
echo [INFO] Initializing database...
if exist "init_db.py" (
    python init_db.py
) else (
    echo [WARNING] init_db.py not found, database will be initialized on first request
)

REM Create necessary directories
if not exist "data" mkdir data
if not exist "data\profiles" mkdir data\profiles
if not exist "data\exports" mkdir data\exports
if not exist "data\logs" mkdir data\logs

REM Start auto-login service first
echo [INFO] Starting auto-login service...
cd ..\auto-login

REM Install auto-login dependencies if needed
if not exist "node_modules" (
    echo [INFO] Installing auto-login dependencies...
    call npm install
)

REM Start auto-login service in background
start /b npm run start:dev

REM Wait for auto-login service to start
echo [INFO] Waiting for auto-login service to start...
timeout /t 10 /nobreak >nul

REM Create test user for desktop development
echo [INFO] Creating test user for desktop development...
curl -s -X POST http://localhost:3000/register -H "Content-Type: application/json" -d "{\"email\":\"<EMAIL>\",\"password\":\"desktop123\"}" >nul 2>&1
echo [INFO] Test user credentials: <EMAIL> / desktop123

REM Go back to backend directory
cd ..\backend

REM Start backend server in background
echo [INFO] Starting backend server with authentication...
start /b python main.py

REM Wait for backend to start
echo [INFO] Waiting for backend to start...
timeout /t 5 /nobreak >nul

REM Test backend health
curl -s http://127.0.0.1:8000/health >nul 2>&1
if not errorlevel 1 (
    echo [SUCCESS] Backend health check passed
) else (
    echo [WARNING] Backend health check failed, but continuing...
)

REM Setup frontend
echo [INFO] Setting up frontend...
cd ..\frontend

REM Install frontend dependencies
if not exist "node_modules" (
    echo [INFO] Installing frontend dependencies...
    npm install
)

REM Build frontend for Electron
echo [INFO] Building frontend for Electron...
npm run webpack:build

REM Start Electron app
echo [INFO] Starting Electron desktop application...
echo [INFO] Backend logs: backend\logs\backend.log
echo.
echo [WARNING] Close this window to stop all services
echo === Starting Desktop Application ===

REM Start Electron and wait for it to finish
npm start

echo [INFO] Desktop application closed

REM Cleanup
echo [INFO] Cleaning up processes...
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im electron.exe >nul 2>&1

pause
