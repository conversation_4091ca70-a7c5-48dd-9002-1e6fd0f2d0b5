#!/bin/bash

# Facebook Automation Desktop - Production Run Script
# This script runs the built application in production mode

set -e

echo "🚀 Starting Facebook Automation Desktop (Production Mode)..."

# Get the script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Change to project root
cd "$PROJECT_ROOT"

echo "📁 Project root: $PROJECT_ROOT"

# Check if backend is already running
echo "🔍 Checking if backend is running..."
if curl -s http://127.0.0.1:8000/health > /dev/null 2>&1; then
    echo "✅ Backend is already running"
else
    echo "🔄 Starting backend..."
    cd backend
    
    # Activate virtual environment if it exists
    if [ -d "venv" ]; then
        echo "🐍 Activating virtual environment..."
        source venv/bin/activate
    fi
    
    # Start backend in background
    python -m uvicorn main:app --host 127.0.0.1 --port 8000 &
    BACKEND_PID=$!
    
    # Wait for backend to start
    echo "⏳ Waiting for backend to start..."
    for i in {1..30}; do
        if curl -s http://127.0.0.1:8000/health > /dev/null 2>&1; then
            echo "✅ Backend started successfully"
            break
        fi
        sleep 1
    done
    
    cd "$PROJECT_ROOT"
fi

# Build frontend
echo "🔨 Building frontend..."
cd frontend
npm run webpack:build
cd "$PROJECT_ROOT"

# Start Electron app
echo "🖥️  Starting Electron app..."
cd frontend
npm start

# Cleanup function
cleanup() {
    echo "🧹 Cleaning up..."
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
    fi
}

# Set trap to cleanup on exit
trap cleanup EXIT
