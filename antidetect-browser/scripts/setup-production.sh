#!/bin/bash

# Setup script for production environment
# Facebook Automation Desktop Application

set -e  # Exit on any error

echo "🚀 Setting up Facebook Automation Desktop Application for production..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
INSTALL_DIR="/opt/facebook-automation"
SERVICE_USER="fbautomation"
LOG_DIR="/var/log/facebook-automation"
DATA_DIR="/var/lib/facebook-automation"

# Check if running as root
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "This script must be run as root for production setup"
        print_status "Please run: sudo $0"
        exit 1
    fi
}

# Install system dependencies
install_system_deps() {
    print_status "Installing system dependencies..."
    
    # Detect OS
    if [ -f /etc/debian_version ]; then
        # Debian/Ubuntu
        apt-get update
        apt-get install -y \
            python3 \
            python3-pip \
            python3-venv \
            nodejs \
            npm \
            nginx \
            redis-server \
            postgresql \
            postgresql-contrib \
            supervisor \
            curl \
            wget \
            unzip \
            git
            
    elif [ -f /etc/redhat-release ]; then
        # RHEL/CentOS/Fedora
        yum update -y
        yum install -y \
            python3 \
            python3-pip \
            nodejs \
            npm \
            nginx \
            redis \
            postgresql \
            postgresql-server \
            supervisor \
            curl \
            wget \
            unzip \
            git
            
    else
        print_warning "Unsupported OS. Please install dependencies manually:"
        print_status "Required: Python 3.8+, Node.js 16+, Redis, PostgreSQL, Nginx, Supervisor"
    fi
    
    print_success "System dependencies installed"
}

# Create service user
create_service_user() {
    print_status "Creating service user..."
    
    if ! id "$SERVICE_USER" &>/dev/null; then
        useradd -r -s /bin/false -d "$DATA_DIR" "$SERVICE_USER"
        print_success "Created service user: $SERVICE_USER"
    else
        print_warning "Service user already exists: $SERVICE_USER"
    fi
}

# Create directories
create_directories() {
    print_status "Creating application directories..."
    
    # Create directories
    mkdir -p "$INSTALL_DIR"
    mkdir -p "$LOG_DIR"
    mkdir -p "$DATA_DIR"
    mkdir -p "$DATA_DIR/uploads"
    mkdir -p "$DATA_DIR/exports"
    mkdir -p "$DATA_DIR/profiles"
    
    # Set permissions
    chown -R "$SERVICE_USER:$SERVICE_USER" "$INSTALL_DIR"
    chown -R "$SERVICE_USER:$SERVICE_USER" "$LOG_DIR"
    chown -R "$SERVICE_USER:$SERVICE_USER" "$DATA_DIR"
    
    chmod 755 "$INSTALL_DIR"
    chmod 755 "$LOG_DIR"
    chmod 755 "$DATA_DIR"
    
    print_success "Directories created and configured"
}

# Setup database
setup_database() {
    print_status "Setting up PostgreSQL database..."
    
    # Initialize PostgreSQL if needed
    if [ -f /etc/redhat-release ]; then
        postgresql-setup initdb || true
        systemctl enable postgresql
        systemctl start postgresql
    else
        systemctl enable postgresql
        systemctl start postgresql
    fi
    
    # Create database and user
    sudo -u postgres psql << EOF
CREATE DATABASE facebook_automation;
CREATE USER fbautomation WITH PASSWORD 'secure_password_change_me';
GRANT ALL PRIVILEGES ON DATABASE facebook_automation TO fbautomation;
\q
EOF
    
    print_success "Database setup completed"
}

# Setup Redis
setup_redis() {
    print_status "Setting up Redis..."
    
    # Enable and start Redis
    systemctl enable redis-server || systemctl enable redis
    systemctl start redis-server || systemctl start redis
    
    # Configure Redis for production
    cat > /etc/redis/redis.conf.d/facebook-automation.conf << EOF
# Facebook Automation Redis Configuration
maxmemory 256mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
EOF
    
    systemctl restart redis-server || systemctl restart redis
    
    print_success "Redis setup completed"
}

# Install application
install_application() {
    print_status "Installing application..."
    
    # Copy application files
    cp -r backend "$INSTALL_DIR/"
    cp -r frontend "$INSTALL_DIR/"
    cp -r scripts "$INSTALL_DIR/"
    
    # Setup Python environment
    cd "$INSTALL_DIR/backend"
    python3 -m venv venv
    source venv/bin/activate
    pip install --upgrade pip
    pip install -r requirements.txt
    pip install gunicorn
    
    # Setup Node.js environment
    cd "$INSTALL_DIR/frontend"
    npm install --production
    npm run build
    
    # Set permissions
    chown -R "$SERVICE_USER:$SERVICE_USER" "$INSTALL_DIR"
    
    print_success "Application installed"
}

# Create environment files
create_production_env() {
    print_status "Creating production environment files..."
    
    # Backend .env file
    cat > "$INSTALL_DIR/backend/.env" << EOF
# Database
DATABASE_URL=postgresql://fbautomation:secure_password_change_me@localhost/facebook_automation

# API Settings
API_HOST=127.0.0.1
API_PORT=8000
DEBUG=false

# Security
SECRET_KEY=$(openssl rand -hex 32)
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Redis
REDIS_URL=redis://localhost:6379

# Logging
LOG_LEVEL=INFO
LOG_FILE=$LOG_DIR/backend.log

# Data directories
UPLOAD_DIR=$DATA_DIR/uploads
EXPORT_DIR=$DATA_DIR/exports
PROFILE_DIR=$DATA_DIR/profiles
EOF
    
    # Frontend .env file
    cat > "$INSTALL_DIR/frontend/.env" << EOF
# API Configuration
REACT_APP_API_URL=http://localhost:8000
REACT_APP_WS_URL=ws://localhost:8000

# Environment
NODE_ENV=production
EOF
    
    # Set permissions
    chown "$SERVICE_USER:$SERVICE_USER" "$INSTALL_DIR/backend/.env"
    chown "$SERVICE_USER:$SERVICE_USER" "$INSTALL_DIR/frontend/.env"
    chmod 600 "$INSTALL_DIR/backend/.env"
    chmod 600 "$INSTALL_DIR/frontend/.env"
    
    print_success "Environment files created"
}

# Setup systemd services
setup_systemd_services() {
    print_status "Setting up systemd services..."
    
    # Backend service
    cat > /etc/systemd/system/facebook-automation-backend.service << EOF
[Unit]
Description=Facebook Automation Backend
After=network.target postgresql.service redis.service
Requires=postgresql.service redis.service

[Service]
Type=exec
User=$SERVICE_USER
Group=$SERVICE_USER
WorkingDirectory=$INSTALL_DIR/backend
Environment=PATH=$INSTALL_DIR/backend/venv/bin
ExecStart=$INSTALL_DIR/backend/venv/bin/gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker -b 127.0.0.1:8000
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
    
    # Celery worker service
    cat > /etc/systemd/system/facebook-automation-worker.service << EOF
[Unit]
Description=Facebook Automation Celery Worker
After=network.target redis.service
Requires=redis.service

[Service]
Type=exec
User=$SERVICE_USER
Group=$SERVICE_USER
WorkingDirectory=$INSTALL_DIR/backend
Environment=PATH=$INSTALL_DIR/backend/venv/bin
ExecStart=$INSTALL_DIR/backend/venv/bin/celery -A app.celery worker --loglevel=info
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
    
    # Enable services
    systemctl daemon-reload
    systemctl enable facebook-automation-backend
    systemctl enable facebook-automation-worker
    
    print_success "Systemd services configured"
}

# Setup Nginx
setup_nginx() {
    print_status "Setting up Nginx..."
    
    # Create Nginx configuration
    cat > /etc/nginx/sites-available/facebook-automation << EOF
server {
    listen 80;
    server_name localhost;
    
    # Frontend static files
    location / {
        root $INSTALL_DIR/frontend/dist;
        try_files \$uri \$uri/ /index.html;
    }
    
    # API proxy
    location /api/ {
        proxy_pass http://127.0.0.1:8000/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # WebSocket proxy
    location /ws/ {
        proxy_pass http://127.0.0.1:8000/ws/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF
    
    # Enable site
    ln -sf /etc/nginx/sites-available/facebook-automation /etc/nginx/sites-enabled/
    rm -f /etc/nginx/sites-enabled/default
    
    # Test and reload Nginx
    nginx -t
    systemctl enable nginx
    systemctl restart nginx
    
    print_success "Nginx configured"
}

# Run database migrations
run_migrations() {
    print_status "Running database migrations..."
    
    cd "$INSTALL_DIR/backend"
    source venv/bin/activate
    alembic upgrade head
    
    print_success "Database migrations completed"
}

# Start services
start_services() {
    print_status "Starting services..."
    
    systemctl start facebook-automation-backend
    systemctl start facebook-automation-worker
    systemctl start nginx
    
    print_success "Services started"
}

# Show status
show_status() {
    print_status "Service Status:"
    echo ""
    
    systemctl status facebook-automation-backend --no-pager -l
    echo ""
    systemctl status facebook-automation-worker --no-pager -l
    echo ""
    systemctl status nginx --no-pager -l
    echo ""
    
    print_success "Production setup completed!"
    echo ""
    print_status "Application is available at: http://localhost"
    print_status "Logs are available in: $LOG_DIR"
    print_status "Data is stored in: $DATA_DIR"
    echo ""
    print_warning "Remember to:"
    echo "  1. Change default passwords in $INSTALL_DIR/backend/.env"
    echo "  2. Configure SSL/TLS for production use"
    echo "  3. Set up backup procedures for $DATA_DIR"
    echo "  4. Configure firewall rules"
}

# Main setup function
main() {
    print_status "Starting production setup..."

    # Parse arguments
    SKIP_DEPS=false
    SKIP_DB=false

    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-deps)
                SKIP_DEPS=true
                shift
                ;;
            --skip-db)
                SKIP_DB=true
                shift
                ;;
            --help)
                echo "Usage: $0 [OPTIONS]"
                echo ""
                echo "Options:"
                echo "  --skip-deps     Skip system dependencies installation"
                echo "  --skip-db       Skip database setup"
                echo "  --help          Show this help message"
                echo ""
                exit 0
                ;;
            *)
                print_warning "Unknown option: $1"
                shift
                ;;
        esac
    done

    check_root

    if [ "$SKIP_DEPS" = false ]; then
        install_system_deps
    fi

    create_service_user
    create_directories

    if [ "$SKIP_DB" = false ]; then
        setup_database
    fi

    setup_redis
    install_application
    create_production_env
    setup_systemd_services
    setup_nginx
    run_migrations
    start_services
    show_status
}

# Run main function
main "$@"
