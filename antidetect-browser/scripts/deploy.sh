#!/bin/bash

# Deployment script for Facebook Automation Desktop Application
# Supports multiple deployment targets and methods

set -e  # Exit on any error

echo "🚀 Facebook Automation Desktop Application Deployment Script"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
DEPLOY_TARGET=""
DEPLOY_METHOD=""
REMOTE_HOST=""
REMOTE_USER=""
REMOTE_PATH="/opt/facebook-automation"
BUILD_PACKAGE=""

# Show help
show_help() {
    cat << EOF
Usage: $0 [OPTIONS] TARGET METHOD

Deploy Facebook Automation Desktop Application

TARGETS:
  local       Deploy to local machine
  remote      Deploy to remote server
  docker      Deploy using Docker
  k8s         Deploy to Kubernetes

METHODS:
  traditional Use traditional deployment (systemd services)
  docker      Use Docker containers
  compose     Use Docker Compose

OPTIONS:
  -h, --host HOST         Remote host for deployment
  -u, --user USER         Remote user for deployment
  -p, --path PATH         Remote deployment path (default: /opt/facebook-automation)
  -b, --build PACKAGE     Use specific build package
  --help                  Show this help message

EXAMPLES:
  $0 local traditional                    # Deploy locally with systemd
  $0 remote docker -h server.com -u root # Deploy to remote server with Docker
  $0 docker compose                       # Deploy with Docker Compose
  $0 k8s docker                          # Deploy to Kubernetes

EOF
}

# Parse arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--host)
                REMOTE_HOST="$2"
                shift 2
                ;;
            -u|--user)
                REMOTE_USER="$2"
                shift 2
                ;;
            -p|--path)
                REMOTE_PATH="$2"
                shift 2
                ;;
            -b|--build)
                BUILD_PACKAGE="$2"
                shift 2
                ;;
            --help)
                show_help
                exit 0
                ;;
            -*)
                print_error "Unknown option: $1"
                show_help
                exit 1
                ;;
            *)
                if [ -z "$DEPLOY_TARGET" ]; then
                    DEPLOY_TARGET="$1"
                elif [ -z "$DEPLOY_METHOD" ]; then
                    DEPLOY_METHOD="$1"
                else
                    print_error "Too many arguments: $1"
                    show_help
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    # Validate required arguments
    if [ -z "$DEPLOY_TARGET" ] || [ -z "$DEPLOY_METHOD" ]; then
        print_error "Both TARGET and METHOD are required"
        show_help
        exit 1
    fi
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    case $DEPLOY_METHOD in
        docker|compose)
            if ! command -v docker &> /dev/null; then
                print_error "Docker is required but not installed"
                exit 1
            fi
            
            if [ "$DEPLOY_METHOD" = "compose" ] && ! command -v docker-compose &> /dev/null; then
                print_error "Docker Compose is required but not installed"
                exit 1
            fi
            ;;
        traditional)
            if [ "$DEPLOY_TARGET" = "remote" ]; then
                if ! command -v ssh &> /dev/null; then
                    print_error "SSH is required for remote deployment"
                    exit 1
                fi
                
                if [ -z "$REMOTE_HOST" ]; then
                    print_error "Remote host is required for remote deployment"
                    exit 1
                fi
            fi
            ;;
    esac
    
    print_success "Prerequisites check passed"
}

# Find or build package
prepare_package() {
    print_status "Preparing deployment package..."
    
    if [ -n "$BUILD_PACKAGE" ]; then
        if [ ! -f "$BUILD_PACKAGE" ]; then
            print_error "Specified build package not found: $BUILD_PACKAGE"
            exit 1
        fi
        print_success "Using specified package: $BUILD_PACKAGE"
        return
    fi
    
    # Look for latest build package
    if [ -d "dist" ]; then
        LATEST_TAR=$(ls -t dist/*.tar.gz 2>/dev/null | head -1)
        LATEST_ZIP=$(ls -t dist/*.zip 2>/dev/null | head -1)
        
        if [ -n "$LATEST_TAR" ]; then
            BUILD_PACKAGE="$LATEST_TAR"
            print_success "Using latest tar package: $BUILD_PACKAGE"
            return
        elif [ -n "$LATEST_ZIP" ]; then
            BUILD_PACKAGE="$LATEST_ZIP"
            print_success "Using latest zip package: $BUILD_PACKAGE"
            return
        fi
    fi
    
    # Build if no package found
    print_warning "No build package found, building now..."
    ./scripts/build-production.sh
    
    # Try again to find package
    if [ -d "dist" ]; then
        LATEST_TAR=$(ls -t dist/*.tar.gz 2>/dev/null | head -1)
        if [ -n "$LATEST_TAR" ]; then
            BUILD_PACKAGE="$LATEST_TAR"
            print_success "Using newly built package: $BUILD_PACKAGE"
        else
            print_error "Failed to create build package"
            exit 1
        fi
    else
        print_error "Build failed - no dist directory created"
        exit 1
    fi
}

# Deploy locally with traditional method
deploy_local_traditional() {
    print_status "Deploying locally with traditional method..."
    
    # Extract package
    TEMP_DIR=$(mktemp -d)
    tar -xzf "$BUILD_PACKAGE" -C "$TEMP_DIR"
    
    # Run setup script
    cd "$TEMP_DIR"
    sudo ./scripts/setup-production.sh
    
    # Cleanup
    rm -rf "$TEMP_DIR"
    
    print_success "Local traditional deployment completed"
}

# Deploy locally with Docker
deploy_local_docker() {
    print_status "Deploying locally with Docker..."
    
    # Extract package
    TEMP_DIR=$(mktemp -d)
    tar -xzf "$BUILD_PACKAGE" -C "$TEMP_DIR"
    
    cd "$TEMP_DIR"
    
    if [ "$DEPLOY_METHOD" = "compose" ]; then
        # Deploy with Docker Compose
        docker-compose -f docker-compose.prod.yml up -d
        
        print_status "Waiting for services to start..."
        sleep 10
        
        # Check service status
        docker-compose -f docker-compose.prod.yml ps
    else
        # Build and run individual containers
        docker build -t facebook-automation-backend ./backend
        docker build -t facebook-automation-frontend ./frontend
        
        # Create network
        docker network create facebook-automation || true
        
        # Start database
        docker run -d --name fb-postgres --network facebook-automation \
            -e POSTGRES_DB=facebook_automation \
            -e POSTGRES_USER=fbautomation \
            -e POSTGRES_PASSWORD=password \
            postgres:15
        
        # Start Redis
        docker run -d --name fb-redis --network facebook-automation redis:7-alpine
        
        # Start backend
        docker run -d --name fb-backend --network facebook-automation \
            -e DATABASE_URL=***************************************************/facebook_automation \
            -e REDIS_URL=redis://fb-redis:6379 \
            facebook-automation-backend
        
        # Start frontend
        docker run -d --name fb-frontend --network facebook-automation \
            -p 80:80 facebook-automation-frontend
    fi
    
    # Cleanup
    cd - > /dev/null
    rm -rf "$TEMP_DIR"
    
    print_success "Local Docker deployment completed"
}

# Deploy to remote server
deploy_remote() {
    print_status "Deploying to remote server: $REMOTE_HOST..."
    
    # Prepare SSH connection
    SSH_USER=${REMOTE_USER:-root}
    SSH_HOST="$SSH_USER@$REMOTE_HOST"
    
    # Test SSH connection
    if ! ssh -o ConnectTimeout=10 "$SSH_HOST" "echo 'SSH connection successful'"; then
        print_error "Failed to connect to remote host: $REMOTE_HOST"
        exit 1
    fi
    
    # Copy package to remote server
    print_status "Copying package to remote server..."
    scp "$BUILD_PACKAGE" "$SSH_HOST:/tmp/"
    
    PACKAGE_NAME=$(basename "$BUILD_PACKAGE")
    
    # Execute deployment on remote server
    ssh "$SSH_HOST" << EOF
set -e

# Extract package
cd /tmp
tar -xzf "$PACKAGE_NAME"

# Run deployment based on method
if [ "$DEPLOY_METHOD" = "traditional" ]; then
    ./scripts/setup-production.sh
elif [ "$DEPLOY_METHOD" = "docker" ]; then
    # Install Docker if not present
    if ! command -v docker &> /dev/null; then
        curl -fsSL https://get.docker.com -o get-docker.sh
        sh get-docker.sh
    fi
    
    # Deploy with Docker Compose
    docker-compose -f docker-compose.prod.yml up -d
fi

# Cleanup
rm -rf /tmp/facebook-automation-*
rm -f /tmp/$PACKAGE_NAME

echo "Remote deployment completed"
EOF
    
    print_success "Remote deployment completed"
}

# Deploy to Kubernetes
deploy_kubernetes() {
    print_status "Deploying to Kubernetes..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        print_error "kubectl is required for Kubernetes deployment"
        exit 1
    fi
    
    # Extract package to get Kubernetes manifests
    TEMP_DIR=$(mktemp -d)
    tar -xzf "$BUILD_PACKAGE" -C "$TEMP_DIR"
    
    cd "$TEMP_DIR"
    
    # Create Kubernetes manifests if they don't exist
    if [ ! -d "k8s" ]; then
        mkdir k8s
        
        # Create namespace
        cat > k8s/namespace.yaml << 'EOF'
apiVersion: v1
kind: Namespace
metadata:
  name: facebook-automation
EOF
        
        # Create ConfigMap
        cat > k8s/configmap.yaml << 'EOF'
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: facebook-automation
data:
  DATABASE_URL: "************************************************/facebook_automation"
  REDIS_URL: "redis://redis:6379"
  DEBUG: "false"
EOF
        
        # Create backend deployment
        cat > k8s/backend-deployment.yaml << 'EOF'
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend
  namespace: facebook-automation
spec:
  replicas: 2
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
    spec:
      containers:
      - name: backend
        image: facebook-automation-backend:latest
        ports:
        - containerPort: 8000
        envFrom:
        - configMapRef:
            name: app-config
---
apiVersion: v1
kind: Service
metadata:
  name: backend
  namespace: facebook-automation
spec:
  selector:
    app: backend
  ports:
  - port: 8000
    targetPort: 8000
EOF
        
        # Create frontend deployment
        cat > k8s/frontend-deployment.yaml << 'EOF'
apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend
  namespace: facebook-automation
spec:
  replicas: 2
  selector:
    matchLabels:
      app: frontend
  template:
    metadata:
      labels:
        app: frontend
    spec:
      containers:
      - name: frontend
        image: facebook-automation-frontend:latest
        ports:
        - containerPort: 80
---
apiVersion: v1
kind: Service
metadata:
  name: frontend
  namespace: facebook-automation
spec:
  type: LoadBalancer
  selector:
    app: frontend
  ports:
  - port: 80
    targetPort: 80
EOF
    fi
    
    # Apply Kubernetes manifests
    kubectl apply -f k8s/
    
    print_status "Waiting for deployment to be ready..."
    kubectl wait --for=condition=available --timeout=300s deployment/backend -n facebook-automation
    kubectl wait --for=condition=available --timeout=300s deployment/frontend -n facebook-automation
    
    # Show status
    kubectl get pods -n facebook-automation
    kubectl get services -n facebook-automation
    
    # Cleanup
    cd - > /dev/null
    rm -rf "$TEMP_DIR"
    
    print_success "Kubernetes deployment completed"
}

# Main deployment function
main() {
    print_status "Starting deployment process..."
    print_status "Target: $DEPLOY_TARGET, Method: $DEPLOY_METHOD"
    
    check_prerequisites
    prepare_package
    
    case $DEPLOY_TARGET in
        local)
            case $DEPLOY_METHOD in
                traditional)
                    deploy_local_traditional
                    ;;
                docker|compose)
                    deploy_local_docker
                    ;;
                *)
                    print_error "Unsupported method for local deployment: $DEPLOY_METHOD"
                    exit 1
                    ;;
            esac
            ;;
        remote)
            deploy_remote
            ;;
        docker)
            deploy_local_docker
            ;;
        k8s)
            deploy_kubernetes
            ;;
        *)
            print_error "Unsupported deployment target: $DEPLOY_TARGET"
            exit 1
            ;;
    esac
    
    print_success "🎉 Deployment completed successfully!"
    
    # Show access information
    case $DEPLOY_TARGET in
        local|docker)
            print_status "Application is available at: http://localhost"
            ;;
        remote)
            print_status "Application is available at: http://$REMOTE_HOST"
            ;;
        k8s)
            print_status "Check service endpoint with: kubectl get services -n facebook-automation"
            ;;
    esac
}

# Parse arguments and run main function
parse_arguments "$@"
main
