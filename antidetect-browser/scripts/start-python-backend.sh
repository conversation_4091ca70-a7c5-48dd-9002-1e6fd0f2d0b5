#!/bin/bash

echo "🐍 Starting Python Backend for Profile View Feature"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if we're in the right directory
if [ ! -d "backend" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Function to check if port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        return 0
    else
        return 1
    fi
}

# Check if Python backend is already running
if check_port 8000; then
    print_status "Python backend is already running on port 8000"
    print_info "You can access it at: http://localhost:8000"
    print_info "API docs available at: http://localhost:8000/docs"
    exit 0
fi

print_info "Starting Python FastAPI backend..."

# Check if virtual environment exists
if [ ! -d "backend/venv" ]; then
    print_info "Creating Python virtual environment..."
    cd backend
    python3 -m venv venv
    cd ..
    print_status "Virtual environment created"
fi

# Activate virtual environment and install dependencies
print_info "Activating virtual environment and installing dependencies..."
cd backend

# Activate virtual environment
source venv/bin/activate

# Install dependencies if requirements.txt exists
if [ -f "requirements.txt" ]; then
    print_info "Installing Python dependencies..."
    pip install -r requirements.txt
    print_status "Dependencies installed"
else
    print_warning "requirements.txt not found, installing basic dependencies..."
    pip install fastapi uvicorn camoufox playwright sqlalchemy asyncpg
fi

# Check if Camoufox is installed
print_info "Checking Camoufox installation..."
if python -c "import camoufox" 2>/dev/null; then
    print_status "Camoufox is installed"
else
    print_info "Installing Camoufox..."
    pip install camoufox
    print_status "Camoufox installed"
fi

# Start the Python backend
print_info "Starting FastAPI server on port 8000..."
print_status "Python backend starting..."

# Start the server in the background
python main.py &
PYTHON_PID=$!

# Wait a moment for the server to start
sleep 5

# Check if the server started successfully
if check_port 8000; then
    print_status "Python backend started successfully!"
    echo ""
    print_info "Backend Information:"
    echo "   • URL: http://localhost:8000"
    echo "   • API Docs: http://localhost:8000/docs"
    echo "   • Health Check: http://localhost:8000/health"
    echo "   • Process ID: $PYTHON_PID"
    echo ""
    print_info "Available Endpoints:"
    echo "   • POST /api/profiles/{profile_id}/launch-browser"
    echo "   • GET  /api/profiles/active-browsers"
    echo "   • POST /api/profiles/{profile_id}/close-browser"
    echo ""
    print_status "Backend is ready to handle profile view requests!"
    echo ""
    print_info "To test the complete workflow:"
    echo "1. Keep this terminal open (backend running)"
    echo "2. In another terminal, start NestJS: cd auto-login && npm run start:dev"
    echo "3. In another terminal, start Frontend: cd frontend && npm start"
    echo "4. Open http://localhost:3001 and test profile view feature"
    echo ""
    print_warning "Press Ctrl+C to stop the Python backend"
    
    # Function to cleanup on exit
    cleanup() {
        echo -e "\n🛑 Stopping Python backend..."
        kill $PYTHON_PID 2>/dev/null
        print_status "Python backend stopped"
        exit 0
    }
    
    # Set trap to cleanup on script exit
    trap cleanup SIGINT SIGTERM
    
    # Wait indefinitely
    while true; do
        sleep 1
    done
    
else
    print_error "Failed to start Python backend"
    print_info "Check the logs above for error details"
    kill $PYTHON_PID 2>/dev/null
    exit 1
fi
