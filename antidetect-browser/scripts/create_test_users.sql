-- Create test users for profile sharing
-- Run this script against the antidetect_autologin database

-- Admin user
INSERT INTO users (username, email, password_hash, role, created_at, updated_at) 
VALUES ('admin', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', NOW(), NOW())
ON CONFLICT (email) DO NOTHING;

-- Regular users
INSERT INTO users (username, email, password_hash, role, created_at, updated_at) 
VALUES ('user1', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', NOW(), NOW())
ON CONFLICT (email) DO NOTHING;

INSERT INTO users (username, email, password_hash, role, created_at, updated_at) 
VALUES ('user2', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', NOW(), NOW())
ON CONFLICT (email) DO NOTHING;

-- Create accounts for users
INSERT INTO accounts (user_id, package_type, status, created_at, updated_at)
SELECT u.id, 'premium', 'active', NOW(), NOW()
FROM users u WHERE u.username = 'admin'
ON CONFLICT (user_id) DO NOTHING;

INSERT INTO accounts (user_id, package_type, status, created_at, updated_at)
SELECT u.id, 'basic', 'active', NOW(), NOW()
FROM users u WHERE u.username = 'user1'
ON CONFLICT (user_id) DO NOTHING;

INSERT INTO accounts (user_id, package_type, status, created_at, updated_at)
SELECT u.id, 'basic', 'active', NOW(), NOW()
FROM users u WHERE u.username = 'user2'
ON CONFLICT (user_id) DO NOTHING;
