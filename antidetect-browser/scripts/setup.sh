#!/bin/bash

# Facebook Automation Desktop - Setup Script

echo "🚀 Setting up Facebook Automation Desktop..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 16+ first."
    exit 1
fi

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.8+ first."
    exit 1
fi

echo "✅ Node.js version: $(node --version)"
echo "✅ Python version: $(python3 --version)"

# Install Node.js dependencies
echo "📦 Installing Node.js dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install Node.js dependencies"
    exit 1
fi

# Setup Python virtual environment
echo "🐍 Setting up Python virtual environment..."
cd backend

if [ ! -d "venv" ]; then
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install Python dependencies
echo "📦 Installing Python dependencies..."
pip install --upgrade pip
pip install -r requirements.txt

if [ $? -ne 0 ]; then
    echo "❌ Failed to install Python dependencies"
    exit 1
fi

# Install crawl4ai
echo "🕷️ Installing crawl4ai..."
cd ../crawl4ai
pip install -e .

if [ $? -ne 0 ]; then
    echo "❌ Failed to install crawl4ai"
    exit 1
fi

cd ..

# Install Playwright browsers
echo "🎭 Installing Playwright browsers..."
python -m playwright install chromium

if [ $? -ne 0 ]; then
    echo "❌ Failed to install Playwright browsers"
    exit 1
fi

# Create data directories
echo "📁 Creating data directories..."
mkdir -p backend/data/{profiles,exports,logs}

# Initialize database
echo "🗄️ Initializing database..."
cd backend
python -c "
import asyncio
from app.core.database import init_db

async def main():
    await init_db()
    print('Database initialized successfully')

asyncio.run(main())
"

if [ $? -ne 0 ]; then
    echo "❌ Failed to initialize database"
    exit 1
fi

cd ..

echo "✅ Setup completed successfully!"
echo ""
echo "🎯 Next steps:"
echo "1. Start Redis server (if using caching): redis-server"
echo "2. Run the application: npm run dev"
echo ""
echo "📚 For more information, see README.md"
