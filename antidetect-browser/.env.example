# Facebook Automation Desktop - Environment Variables

# API Settings
HOST=127.0.0.1
PORT=8000
DEBUG=true

# Database Settings
DATABASE_URL=sqlite+aiosqlite:///./facebook_automation.db

# Redis Settings (for caching and task queue)
REDIS_URL=redis://localhost:6379/0

# Security
SECRET_KEY=your-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Browser Settings
BROWSER_TIMEOUT=30000
MAX_CONCURRENT_BROWSERS=5

# Scraping Settings
MAX_SCRAPING_WORKERS=3
SCRAPING_DELAY_MIN=2
SCRAPING_DELAY_MAX=5

# Messaging Settings
MAX_MESSAGING_WORKERS=5
MESSAGE_DELAY_MIN=5
MESSAGE_DELAY_MAX=15

# Rate Limiting
REQUESTS_PER_MINUTE=30
REQUESTS_PER_HOUR=500
