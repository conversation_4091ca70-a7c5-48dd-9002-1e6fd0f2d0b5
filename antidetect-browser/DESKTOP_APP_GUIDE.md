# Facebook Automation Desktop Application - Quick Start Guide

## 🚀 Hướng dẫn chạy Desktop App trên Local với Docker Services

### Tổng quan
Desktop application sử dụng:
- **Docker Compose** cho PostgreSQL và Redis (từ `auto-login/docker-compose.yml`)
- **FastAPI Backend** chạy local (Python virtual environment)
- **Electron Frontend** chạy local (Desktop application)

### Kiến trúc hệ thống
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Electron App  │    │   FastAPI        │    │   Docker        │
│   (Frontend)    │◄──►│   Backend        │◄──►│   Services      │
│   Desktop UI    │    │   Port: 8000     │    │   PostgreSQL    │
│                 │    │                  │    │   Redis         │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 📋 Yêu cầu hệ thống

### Bắt buộc
- **Docker & Docker Compose** (cho PostgreSQL và Redis)
- **Python 3.8+** (cho backend)
- **Node.js 16+** (cho frontend/Electron)
- **Git**

### Kiểm tra yêu cầu
```bash
# Kiểm tra Docker
docker --version
docker-compose --version

# Kiểm tra Python
python3 --version

# Kiểm tra Node.js
node --version
```

## 🛠️ Cài đặt và chạy

### Bước 1: Setup môi trường
```bash
# Linux/macOS
./scripts/setup-local-desktop.sh

# Windows
.\scripts\setup-local-desktop.bat
```

Script này sẽ:
- ✅ Kiểm tra Docker, Python, Node.js
- ✅ Tạo Python virtual environment
- ✅ Cài đặt backend dependencies
- ✅ Cài đặt frontend dependencies
- ✅ Tạo Docker environment file
- ✅ Test Docker services

### Bước 2: Chạy application
```bash
# Linux/macOS
./scripts/run-local-desktop.sh

# Windows
.\scripts\run-local-desktop.bat
```

Script này sẽ:
- 🐘 Khởi động PostgreSQL container
- 🔴 Khởi động Redis container
- 📡 Khởi động FastAPI backend (port 8000)
- 🖥️ Khởi động Electron desktop app

## 🎯 Các tùy chọn chạy

### Chạy tất cả services (khuyến nghị)
```bash
./scripts/run-local-desktop.sh
```

### Chỉ chạy backend với Docker services
```bash
./scripts/run-local-desktop.sh --backend-only
```
Hữu ích khi:
- Muốn test API endpoints
- Phát triển frontend riêng biệt
- Debug backend issues

### Chỉ chạy frontend (cần backend đang chạy)
```bash
./scripts/run-local-desktop.sh --frontend-only
```
Hữu ích khi:
- Backend đã chạy ở terminal khác
- Chỉ muốn restart Electron app

### Sử dụng custom port
```bash
./scripts/run-local-desktop.sh --port 8080
```

### Kiểm tra trạng thái services
```bash
./scripts/run-local-desktop.sh --status
```

### Dừng tất cả services
```bash
./scripts/run-local-desktop.sh --stop
```

## 🔍 Kiểm tra services

### Sau khi chạy thành công, bạn sẽ thấy:
- ✅ PostgreSQL: Running (Docker)
- ✅ Redis: Running (Docker)
- ✅ Backend: Running (PID: xxxx) - http://localhost:8000
- ✅ Frontend: Running (PID: xxxx) - Electron Desktop App

### Endpoints có sẵn:
- 📡 **Backend API**: http://localhost:8000
- 📚 **API Documentation**: http://localhost:8000/docs
- 🖥️ **Desktop App**: Electron window tự động mở

## 🐛 Troubleshooting

### Lỗi thường gặp

#### 1. Docker không chạy
```bash
# Khởi động Docker daemon
sudo systemctl start docker  # Linux
# Hoặc mở Docker Desktop app  # Windows/macOS
```

#### 2. Port đã được sử dụng
```bash
# Kiểm tra process đang sử dụng port
lsof -i :8000  # Linux/macOS
netstat -ano | findstr :8000  # Windows

# Sử dụng port khác
./scripts/run-local-desktop.sh --port 8080
```

#### 3. Dependencies thiếu
```bash
# Chạy lại setup
./scripts/setup-local-desktop.sh
```

#### 4. Database connection lỗi
```bash
# Kiểm tra Docker containers
docker-compose -f auto-login/docker-compose.yml ps

# Restart Docker services
cd auto-login
docker-compose down
docker-compose up -d postgres redis
```

#### 5. Virtual environment lỗi
```bash
# Xóa và tạo lại venv
rm -rf backend/venv
./scripts/setup-local-desktop.sh --skip-frontend --skip-docker
```

### Logs và debugging

#### Backend logs
```bash
# Xem logs trong terminal khi chạy
./scripts/run-local-desktop.sh --backend-only

# Hoặc chạy trực tiếp
cd backend
source venv/bin/activate
uvicorn main:app --reload --port 8000
```

#### Docker logs
```bash
cd auto-login
docker-compose logs postgres
docker-compose logs redis
```

#### Frontend logs
```bash
# Xem logs trong terminal khi chạy
./scripts/run-local-desktop.sh --frontend-only

# Hoặc chạy trực tiếp
cd frontend
npm run start
```

## 🔄 Development workflow

### Workflow hàng ngày
1. **Khởi động**: `./scripts/run-local-desktop.sh`
2. **Phát triển**: Code trong `backend/` và `frontend/`
3. **Test**: Backend tự động reload, frontend cần restart
4. **Dừng**: Ctrl+C trong terminal

### Hot reload
- **Backend**: Tự động reload khi thay đổi code
- **Frontend**: Cần restart Electron app để thấy thay đổi

### Database changes
```bash
# Chạy migrations
cd backend
source venv/bin/activate
alembic upgrade head
```

## 📊 Monitoring

### Kiểm tra services đang chạy
```bash
# Docker containers
docker ps

# Local processes
ps aux | grep uvicorn  # Backend
ps aux | grep electron  # Frontend
```

### Resource usage
```bash
# Docker stats
docker stats

# System resources
htop  # Linux/macOS
# Task Manager  # Windows
```

## 🛑 Dừng services

### Dừng tất cả (khuyến nghị)
```bash
# Ctrl+C trong terminal đang chạy
# Hoặc
./scripts/run-local-desktop.sh --stop
```

### Dừng từng service
```bash
# Dừng Docker services
cd auto-login
docker-compose down

# Dừng backend
pkill -f "uvicorn main:app"

# Dừng frontend
pkill -f "electron"
```

## 📝 Notes

- **Khuyến nghị**: Sử dụng desktop app scripts cho development experience tốt nhất
- **Database**: PostgreSQL data được persist trong Docker volume
- **Profiles**: Camoufox profiles được lưu trong `data/profiles/`
- **Logs**: Application logs trong `backend/logs/` và `frontend/logs/`
- **Cleanup**: Script tự động cleanup khi dừng (Ctrl+C)

## 🔗 Liên kết hữu ích

- **API Documentation**: http://localhost:8000/docs
- **Backend Source**: `backend/`
- **Frontend Source**: `frontend/`
- **Docker Compose**: `auto-login/docker-compose.yml`
- **Scripts Documentation**: `scripts/README.md`
