#!/usr/bin/env python3
"""
Complete fix for scraping system - create database and test
"""

import asyncio
import json
import logging
import sys
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def create_database_tables():
    """Create database tables"""
    try:
        print("📊 Creating database tables...")
        
        from app.core.database import engine, Base
        from app.models.profile import Profile
        from app.models.scraping import ScrapingTask
        from app.models.messaging import MessagingTask
        from app.models.system import AppSettings
        
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        print("✅ Database tables created successfully")
        return True
        
    except Exception as e:
        print(f"❌ Database creation failed: {e}")
        return False

async def create_test_profile():
    """Create a test profile for scraping"""
    try:
        print("👤 Creating test profile...")
        
        from app.core.database import AsyncSessionLocal
        from app.models.profile import Profile
        from sqlalchemy import select
        
        async with AsyncSessionLocal() as db:
            # Check if profile already exists
            result = await db.execute(select(Profile))
            existing_profiles = result.scalars().all()
            
            if existing_profiles:
                print(f"✅ Found {len(existing_profiles)} existing profiles")
                return existing_profiles[0]
            
            # Create new profile
            profile_path = "data/profiles/test_profile_1"
            Path(profile_path).mkdir(parents=True, exist_ok=True)
            
            test_profile = Profile(
                name="Test Profile for Scraping",
                profile_path=profile_path,
                proxy_type="no_proxy",
                status="active"
            )
            
            db.add(test_profile)
            await db.commit()
            await db.refresh(test_profile)
            
            print(f"✅ Created test profile: {test_profile.name} (ID: {test_profile.id})")
            return test_profile
            
    except Exception as e:
        print(f"❌ Profile creation failed: {e}")
        return None

async def test_fallback_scraping():
    """Test fallback scraping method"""
    try:
        print("🔧 Testing fallback scraping method...")
        
        from app.services.facebook_scraper import FacebookScraper
        
        # Create test profile
        test_profile = await create_test_profile()
        if not test_profile:
            return False
        
        # Test fallback session creation
        scraper = FacebookScraper()
        
        print(f"🚀 Creating fallback browser session...")
        crawler = await scraper._create_fallback_session(test_profile, "test_fallback")
        
        if crawler:
            print("✅ Fallback browser created successfully!")
            print("🖥️  Browser window should be visible")
            
            # Test page access
            page = crawler.crawler_strategy.page
            if page:
                print("✅ Page object accessible")
                
                # Test navigation
                print("🌐 Testing navigation to Google...")
                await page.goto("https://www.google.com")
                await asyncio.sleep(2)
                
                title = await page.title()
                print(f"📄 Page title: {title}")
                
                # Test Facebook navigation
                print("🌐 Testing Facebook navigation...")
                await page.goto("https://www.facebook.com")
                await asyncio.sleep(3)
                
                fb_title = await page.title()
                print(f"📄 Facebook title: {fb_title}")
                
                print("✅ Navigation tests successful!")
                
                # Keep browser open for inspection
                print("\n🔍 Browser is now open for inspection")
                print("   You should see a Facebook page in the browser window")
                
                choice = input("Keep browser open for manual testing? (y/N): ").lower().strip()
                if choice == 'y':
                    print("🖥️  Browser will remain open for manual testing")
                    print("   You can manually login to Facebook and test scraping")
                    input("Press Enter when done testing...")
                
                # Cleanup
                await scraper.cleanup_session("test_fallback")
                print("✅ Browser cleanup completed")
                
            else:
                print("❌ No page object available")
                return False
                
        else:
            print("❌ Fallback browser creation failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Fallback scraping test failed: {e}")
        logger.exception("Fallback test error:")
        return False

async def test_scraping_task_manager():
    """Test scraping task manager integration"""
    try:
        print("\n📋 Testing ScrapingTaskManager integration...")
        
        from app.services.scraping_task_manager import ScrapingTaskManager
        from app.models.scraping import ScrapingConfig, ScrapingType
        
        # Get test profile
        test_profile = await create_test_profile()
        if not test_profile:
            return False
        
        # Create task manager
        task_manager = ScrapingTaskManager()
        
        # Create test config
        config = ScrapingConfig(
            profile_id=test_profile.id,
            target_url="https://www.facebook.com/groups/test/posts/123",
            scraping_types=[ScrapingType.COMMENTS],
            max_results=10
        )
        
        # Create task
        task_id = await task_manager.create_scraping_task(config)
        print(f"✅ Scraping task created: {task_id}")
        
        # Check task status
        if task_id in task_manager.active_tasks:
            print("✅ Task registered in active_tasks")
            
            # Get task progress
            progress = await task_manager.get_task_progress(task_id)
            print(f"📊 Task progress: {progress}")
            
        else:
            print("❌ Task not found in active_tasks")
            return False
        
        print("✅ ScrapingTaskManager integration working")
        return True
        
    except Exception as e:
        print(f"❌ ScrapingTaskManager test failed: {e}")
        return False

async def create_facebook_cookies_sample():
    """Create sample Facebook cookies for testing"""
    try:
        print("\n🍪 Creating sample Facebook cookies...")
        
        test_profile = await create_test_profile()
        if not test_profile:
            return False
        
        profile_path = Path(test_profile.profile_path)
        cookies_file = profile_path / 'facebook_cookies.json'
        
        # Sample cookies (these are fake for demo)
        sample_cookies = [
            {
                "name": "c_user",
                "value": "100000000000000",
                "domain": ".facebook.com",
                "path": "/",
                "httpOnly": False,
                "secure": True,
                "sameSite": "None"
            },
            {
                "name": "xs",
                "value": "sample_session_token",
                "domain": ".facebook.com", 
                "path": "/",
                "httpOnly": True,
                "secure": True,
                "sameSite": "None"
            }
        ]
        
        with open(cookies_file, 'w') as f:
            json.dump(sample_cookies, f, indent=2)
        
        print(f"✅ Sample cookies created at: {cookies_file}")
        print("   Note: These are sample cookies for testing only")
        print("   Use 'Open Facebook' button to get real cookies")
        
        return True
        
    except Exception as e:
        print(f"❌ Cookie creation failed: {e}")
        return False

async def main():
    """Main fix and test function"""
    
    print("🔧 Complete Scraping System Fix")
    print("=" * 50)
    
    # Step 1: Create database
    db_success = await create_database_tables()
    if not db_success:
        print("❌ Database setup failed, cannot continue")
        return
    
    # Step 2: Create test profile
    profile_success = await create_test_profile()
    if not profile_success:
        print("❌ Profile creation failed, cannot continue")
        return
    
    # Step 3: Create sample cookies
    await create_facebook_cookies_sample()
    
    # Step 4: Test fallback scraping
    fallback_success = await test_fallback_scraping()
    
    # Step 5: Test task manager
    task_success = await test_scraping_task_manager()
    
    print(f"\n📊 Fix Results Summary")
    print("=" * 30)
    print(f"✅ Database: {'✅ Working' if db_success else '❌ Failed'}")
    print(f"✅ Profile: {'✅ Created' if profile_success else '❌ Failed'}")
    print(f"✅ Fallback Scraping: {'✅ Working' if fallback_success else '❌ Failed'}")
    print(f"✅ Task Manager: {'✅ Working' if task_success else '❌ Failed'}")
    
    if all([db_success, profile_success, fallback_success, task_success]):
        print("\n🎉 All fixes successful!")
        print("✅ Scraping system is now ready for use")
        
        print(f"\n📝 Next Steps:")
        print("1. Start the system: ./scripts/dev-web.sh")
        print("2. Access web interface: http://localhost:3000")
        print("3. Use 'Open Facebook' to login and save real cookies")
        print("4. Create scraping tasks - browser will open with cookies")
        
    else:
        print("\n⚠️  Some fixes failed")
        print("🔧 Check the error messages above for details")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  Fix interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
