/**
 * Bulk Messaging Page Component - Profile-based messaging
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  Card, Form, Input, Select, Button, Space, Upload, Table, Progress,
  message, Row, Col, InputNumber, Switch, Tag, Modal, Divider, Tooltip,
  Alert, List, Statistic, Steps, Typography
} from 'antd';
import {
  MessageOutlined, UploadOutlined, PlayCircleOutlined, StopOutlined,
  EyeOutlined, DownloadOutlined, DeleteOutlined, ReloadOutlined,
  UserOutlined, CheckCircleOutlined, CloseCircleOutlined, ClockCircleOutlined,
  FileExcelOutlined, SendOutlined, UpOutlined, DownOutlined
} from '@ant-design/icons';
import { apiService } from '../services/api';

const { Option } = Select;
const { TextArea } = Input;
const { Text } = Typography;
const { Step } = Steps;

const BulkMessaging = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [tasks, setTasks] = useState([]);
  const [profiles, setProfiles] = useState([]);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [fileValidation, setFileValidation] = useState(null);
  const [activeTask, setActiveTask] = useState(null);

  // Debug wrapper for setActiveTask
  const setActiveTaskWithDebug = (taskId) => {
    console.log('🔄 [BulkMessaging] setActiveTask called with:', taskId);
    console.log('🔄 [BulkMessaging] Previous activeTask:', activeTask);
    setActiveTask(taskId);
  };
  const [taskProgress, setTaskProgress] = useState({});
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [selectedTaskResults, setSelectedTaskResults] = useState(null);
  const [currentStep, setCurrentStep] = useState(0);

  // Debug wrapper for setCurrentStep
  const setCurrentStepWithDebug = (step) => {
    console.log('🔄 [BulkMessaging] setCurrentStep called with:', step);
    console.log('🔄 [BulkMessaging] Previous currentStep:', currentStep);
    console.trace('🔄 [BulkMessaging] setCurrentStep call stack');
    setCurrentStep(step);
  };
  const [completedTasks, setCompletedTasks] = useState([]);
  const [showCompletedTasks, setShowCompletedTasks] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [lastCompletedTasksLoad, setLastCompletedTasksLoad] = useState(0);
  const [completedPollingCount, setCompletedPollingCount] = useState({});
  const pollingIntervalRef = useRef(null);
  const completedTasksRef = useRef(new Set());
  const pollingCountRef = useRef({});
  const [completedTasksReloadCount, setCompletedTasksReloadCount] = useState(0);
  const [shouldStopPolling, setShouldStopPolling] = useState(false);

  // Load initial data only once on component mount
  useEffect(() => {
    if (!isInitialized) {
      loadInitialData();
      setIsInitialized(true);
    }
  }, [isInitialized]);

  // Set up polling for active tasks
  useEffect(() => {
    let interval = null;

    if (activeTask) {
      // Check if task is already completed before setting up polling
      const currentTaskStatus = taskProgress[activeTask];
      if (currentTaskStatus && ['completed', 'failed', 'cancelled'].includes(currentTaskStatus.status)) {
        console.log('� [BulkMessaging] Task already completed, not setting up polling');
        return;
      }

      console.log('�🔄 [BulkMessaging] Setting up polling interval for activeTask:', activeTask);
      interval = setInterval(() => {
        // Check if polling should stop
        if (shouldStopPolling) {
          console.log('🛑 [BulkMessaging] Polling stopped by shouldStopPolling flag');
          clearInterval(interval);
          return;
        }

        // Track polling count to prevent infinite polling
        const currentCount = pollingCountRef.current[activeTask] || 0;
        pollingCountRef.current[activeTask] = currentCount + 1;

        console.log(`🔄 [BulkMessaging] Polling task status for: ${activeTask} (count: ${currentCount + 1})`);
        console.log('🕐 [BulkMessaging] Poll timestamp:', new Date().toISOString());

        // Stop polling after 50 attempts (50 * 3s = 2.5 minutes max)
        if (currentCount >= 50) {
          console.log('🛑 [BulkMessaging] Max polling attempts reached, stopping polling');
          clearInterval(pollingIntervalRef.current);
          pollingIntervalRef.current = null;
          return;
        }

        pollTaskStatus(activeTask);
      }, 3000);
    } else {
      console.log('🧹 [BulkMessaging] Cleaning up polling interval');
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [activeTask, shouldStopPolling]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      console.log('🔑 [BulkMessaging] Loading initial data...');

      // Load profiles
      console.log('🔄 [BulkMessaging] Loading profiles...');
      const profilesResponse = await apiService.getProfiles();
      console.log('📊 [BulkMessaging] Profiles response:', profilesResponse);

      // Handle normalized response - get the actual profiles array
      let profilesData;
      if (Array.isArray(profilesResponse)) {
        profilesData = profilesResponse;
      } else if (profilesResponse.data && Array.isArray(profilesResponse.data)) {
        profilesData = profilesResponse.data;
      } else if (profilesResponse.originalData && Array.isArray(profilesResponse.originalData)) {
        profilesData = profilesResponse.originalData;
      } else {
        console.error('❌ [BulkMessaging] Invalid profiles response format:', profilesResponse);
        profilesData = [];
      }

      console.log('📊 [BulkMessaging] Profiles data:', profilesData);
      const facebookLoggedInProfiles = profilesData.filter(p => p.facebook_logged_in);
      console.log('📊 [BulkMessaging] Facebook logged in profiles:', facebookLoggedInProfiles);
      console.log('📊 [BulkMessaging] Setting profiles state with:', facebookLoggedInProfiles.length, 'profiles');
      console.log('📊 [BulkMessaging] Profile details:', facebookLoggedInProfiles.map(p => ({id: p.id, name: p.name, facebook_logged_in: p.facebook_logged_in})));
      setProfiles(facebookLoggedInProfiles);
      console.log('✅ [BulkMessaging] Profiles state updated');

      // Debug: Check profiles state after setting
      setTimeout(() => {
        console.log('🔍 [BulkMessaging] Profiles state after timeout:', profiles.length);
      }, 100);

      // Load completed tasks from database - auto-set activeTask to show latest results
      await loadCompletedTasks(true);

    } catch (error) {
      message.error('Failed to load data: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const loadCompletedTasks = async (autoSetActiveTask = true) => {
    try {
      // Prevent too frequent calls (debounce)
      const now = Date.now();
      if (now - lastCompletedTasksLoad < 2000) {
        console.log('📊 [BulkMessaging] Skipping completed tasks load - too frequent');
        return;
      }
      setLastCompletedTasksLoad(now);

      console.log('📊 [BulkMessaging] Loading completed tasks...');
      const response = await apiService.get('/api/messaging/bulk/statistics?limit=1000');
      console.log('📊 [BulkMessaging] Completed tasks response:', response);

      // Handle normalized response - try multiple paths
      let completedTasksData;
      if (response.statistics && Array.isArray(response.statistics)) {
        completedTasksData = response.statistics;
        console.log('📊 [BulkMessaging] Using direct response.statistics');
      } else if (response.data && response.data.statistics && Array.isArray(response.data.statistics)) {
        completedTasksData = response.data.statistics;
        console.log('📊 [BulkMessaging] Using response.data.statistics');
      } else if (response.originalData && response.originalData.statistics && Array.isArray(response.originalData.statistics)) {
        completedTasksData = response.originalData.statistics;
        console.log('📊 [BulkMessaging] Using response.originalData.statistics');
      } else if (Array.isArray(response)) {
        completedTasksData = response;
        console.log('📊 [BulkMessaging] Using direct array response');
      } else {
        console.log('❌ [BulkMessaging] Could not find statistics array in response:', Object.keys(response));
        completedTasksData = [];
      }

      console.log('✅ [BulkMessaging] Loaded completed tasks:', completedTasksData.length);
      console.log('📊 [BulkMessaging] Raw completed tasks data length check:', completedTasksData);
      console.log('📊 [BulkMessaging] Completed tasks details:', completedTasksData.map(t => t.task_name).join(','));

      // Convert to taskProgress format for UI compatibility
      const progressData = {};
      completedTasksData.forEach(stat => {
        if (stat.status === 'completed') {
          progressData[stat.task_id] = {
            task_id: stat.task_id,
            name: stat.task_name,
            status: stat.status,
            progress: 100,
            total_recipients: stat.total_recipients,
            messages_sent: stat.messages_sent,
            messages_failed: stat.messages_failed,
            messages_skipped: stat.messages_skipped,
            consecutive_failures: 0,
            final_statistics: {
              total_recipients: stat.total_recipients,
              messages_sent: stat.messages_sent,
              messages_failed: stat.messages_failed,
              messages_skipped: stat.messages_skipped,
              success_rate: stat.success_rate,
              total_time_seconds: stat.total_time_seconds,
              total_time_formatted: stat.total_time_formatted,
              average_time_per_message: stat.average_time_per_message,
              completed_at: stat.completed_at,
              browser_closed: stat.browser_closed
            }
          };
        }
      });

      console.log('🔄 [BulkMessaging] Setting completed tasks state:', completedTasksData.length);
      setCompletedTasks(completedTasksData);

      // Update task progress with completed tasks data
      if (Object.keys(progressData).length > 0) {
        console.log('📊 [BulkMessaging] Latest completed task:', completedTasksData[0]);
        console.log('✅ [BulkMessaging] Updating task progress with completed task data');
        setTaskProgress(prev => ({
          ...prev,
          ...progressData
        }));

        // Only auto-set activeTask if explicitly requested and no current activeTask
        if (autoSetActiveTask && !activeTask && completedTasksData.length > 0) {
          const latestCompleted = completedTasksData[0];
          if (latestCompleted.status === 'completed') {
            console.log('🎯 [BulkMessaging] Auto-setting activeTask to latest completed:', latestCompleted.task_id);
            setActiveTaskWithDebug(latestCompleted.task_id);
            setCurrentStepWithDebug(0); // Show completed results
          }
        }

        // If current activeTask is in completed tasks, update its status but keep step
        if (activeTask && progressData[activeTask]) {
          console.log('🎯 [BulkMessaging] Active task found in completed tasks, updating status:', activeTask);
          console.log('✅ [BulkMessaging] Active task status updated to completed');
          console.log('🔄 [BulkMessaging] Keeping current step to show completed results');
          // Don't change currentStep - let it stay at 2 to show Step 3 with final statistics
        }
      }

    } catch (error) {
      console.error('Failed to load completed tasks:', error);
      // Don't show error message as this is not critical
    }
  };

  const handleFileUpload = async (file) => {
    try {
      setLoading(true);
      const formData = new FormData();
      formData.append('file', file);

      const response = await apiService.post('/api/messaging/bulk/validate-excel', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });

      console.log('📊 [BulkMessaging] Full API response:', response);
      console.log('📊 [BulkMessaging] Original data:', response.originalData);

      // Use original data if available, otherwise use normalized data
      let result;
      if (response.originalData && typeof response.originalData === 'object' && 'valid' in response.originalData) {
        result = response.originalData;
        console.log('📊 [BulkMessaging] Using original data:', result);
      } else if (response.data && typeof response.data === 'object' && 'valid' in response.data) {
        result = response.data;
        console.log('📊 [BulkMessaging] Using nested data:', result);
      } else {
        result = response;
        console.log('📊 [BulkMessaging] Using direct response:', result);
      }

      if (result.valid) {
        setUploadedFile({
          file: file,
          file_path: result.file_path || file.name,
          total_recipients: result.total_recipients,
          url_type_breakdown: result.url_type_breakdown
        });
        setFileValidation(result);
        setCurrentStepWithDebug(1);
        message.success(`Validated ${result.total_recipients} recipients successfully`);
      } else {
        message.error('File validation failed: ' + result.error);
        setFileValidation(result);
      }

      return false; // Prevent default upload behavior
    } catch (error) {
      message.error('Failed to validate file: ' + error.message);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const handleStartBulkMessaging = async (values) => {
    try {
      setLoading(true);

      const config = {
        name: values.name,
        sender_profile_id: values.sender_profile_id,
        excel_file_path: uploadedFile?.file_path,
        message_content: values.message_content,
        message_type: values.message_type || 'text',
        delay_between_messages_min: values.delay_between_messages_min || 10,
        delay_between_messages_max: values.delay_between_messages_max || 30,
        scroll_distance_min: values.scroll_distance_min || 200,
        scroll_distance_max: values.scroll_distance_max || 800,
        reading_time_min: values.reading_time_min || 2.0,
        reading_time_max: values.reading_time_max || 5.0,
        stop_on_consecutive_failures: values.stop_on_consecutive_failures || 3,
        skip_sent_recipients: values.skip_sent_recipients !== false
      };

      const response = await apiService.post('/api/messaging/bulk/start', { config });
      console.log('🚀 [BulkMessaging] Task start response:', response);

      // Handle normalized response - get the actual task_id
      let result;
      if (response.task_id) {
        result = response;
        console.log('🚀 [BulkMessaging] Using direct response');
      } else if (response.data && response.data.task_id) {
        result = response.data;
        console.log('🚀 [BulkMessaging] Using response.data');
      } else if (response.originalData && response.originalData.task_id) {
        result = response.originalData;
        console.log('🚀 [BulkMessaging] Using response.originalData');
      } else {
        console.error('❌ [BulkMessaging] Could not find task_id in response:', Object.keys(response));
        throw new Error('Invalid response format - no task_id found');
      }

      console.log('🚀 [BulkMessaging] Task start result:', result);
      console.log('🚀 [BulkMessaging] Setting activeTask to:', result.task_id);
      console.log('🚀 [BulkMessaging] Setting currentStep to: 2');

      // Reset completed tasks tracking for new task
      completedTasksRef.current.clear();
      pollingCountRef.current = {};
      setCompletedTasksReloadCount(0);
      setShouldStopPolling(false);

      setActiveTaskWithDebug(result.task_id);
      setCurrentStepWithDebug(2);
      message.success('Bulk messaging task started successfully');

      console.log('🚀 [BulkMessaging] After setState - activeTask should be:', result.task_id);

      // Reset form but keep current step and active task
      form.resetFields();
      setUploadedFile(null);
      setFileValidation(null);
      // Don't reset currentStep - keep it at 2 to show progress

    } catch (error) {
      message.error('Failed to start bulk messaging: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const pollTaskStatus = async (taskId) => {
    try {
      const response = await apiService.get(`/api/messaging/bulk/status/${taskId}`);
      console.log('🔄 [BulkMessaging] Poll response for', taskId, ':', response);
      console.log('🔄 [BulkMessaging] Response keys:', Object.keys(response));

      // Handle normalized response - get the actual status data
      let status;
      if (response.status && typeof response.status === 'string') {
        status = response;
        console.log('🔄 [BulkMessaging] Using direct response');
      } else if (response.data && response.data.status) {
        status = response.data;
        console.log('🔄 [BulkMessaging] Using response.data');
      } else if (response.originalData && response.originalData.status) {
        status = response.originalData;
        console.log('🔄 [BulkMessaging] Using response.originalData');
      } else {
        console.error('❌ [BulkMessaging] Could not find status in response:', Object.keys(response));
        status = response; // fallback
      }

      console.log('🔄 [BulkMessaging] Final status object:', status);
      console.log('🔄 [BulkMessaging] Status value:', status.status);

      setTaskProgress(prev => ({ ...prev, [taskId]: status }));

      // Stop polling if task is completed, but keep activeTask for UI display
      if (['completed', 'failed', 'cancelled'].includes(status.status)) {
        // Check if we've already processed this completion to avoid duplicate processing
        const completionCount = (pollingCountRef.current[`${taskId}_completed`] || 0) + 1;
        pollingCountRef.current[`${taskId}_completed`] = completionCount;

        console.log(`✅ [BulkMessaging] Task completed (${completionCount}/2), stopping polling and reloading completed tasks`);

        // Mark task as completed to stop polling
        completedTasksRef.current.add(taskId);
        setShouldStopPolling(true);

        console.log('🛑 [BulkMessaging] Set shouldStopPolling=true to stop polling');

        // Stop processing if we've already handled completion twice
        if (completionCount > 2) {
          console.log('⏹️ [BulkMessaging] Max completion processing reached, stopping');
          return;
        }

        // Update task progress with final status
        setTaskProgress(prev => ({
          ...prev,
          [taskId]: {
            ...prev[taskId],
            ...status,
            stopPolling: true
          }
        }));

        // Reload completed tasks to get the final statistics from database (max 2 times)
        if (completedTasksReloadCount < 2) {
          console.log(`🔄 [BulkMessaging] Reloading completed tasks (${completedTasksReloadCount + 1}/2)`);
          setCompletedTasksReloadCount(prev => prev + 1);
          setTimeout(() => {
            loadCompletedTasks(false); // Don't auto-set activeTask, keep current one
          }, 1000); // Small delay to ensure database is updated
        } else {
          console.log('⏹️ [BulkMessaging] Max reload limit reached (2/2), stopping further reloads');
        }

        return; // Stop further polling
      }

    } catch (error) {
      console.error('Failed to poll task status:', error);
      // If task not found (404), it might be completed, reload completed tasks
      if (error.response && error.response.status === 404) {
        console.log('🔍 [BulkMessaging] Task not found in active tasks, checking completed tasks');
        // Task likely completed and moved to completed tasks
        setTimeout(() => {
          loadCompletedTasks(false); // Don't auto-set activeTask, keep current one
        }, 1000);
      }
    }
  };

  const handleStopTask = async (taskId) => {
    try {
      await apiService.post(`/api/messaging/bulk/stop/${taskId}`);
      message.success('Task stopped successfully');
      setActiveTaskWithDebug(null);
      setCurrentStepWithDebug(0);
    } catch (error) {
      message.error('Failed to stop task: ' + error.message);
    }
  };

  const handleViewResults = async (taskId) => {
    try {
      const results = await apiService.get(`/api/messaging/bulk/results/${taskId}`);
      setSelectedTaskResults(results);
      setPreviewModalVisible(true);
    } catch (error) {
      message.error('Failed to load results: ' + error.message);
    }
  };

  const getStatusTag = (status) => {
    const statusConfig = {
      pending: { color: 'default', text: 'Pending' },
      running: { color: 'processing', text: 'Running' },
      completed: { color: 'success', text: 'Completed' },
      failed: { color: 'error', text: 'Failed' },
      cancelled: { color: 'default', text: 'Cancelled' }
    };

    const config = statusConfig[status] || statusConfig.pending;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const steps = [
    {
      title: 'Upload Excel',
      description: 'Upload recipient list',
      icon: <FileExcelOutlined />
    },
    {
      title: 'Configure',
      description: 'Set message and options',
      icon: <MessageOutlined />
    },
    {
      title: 'Send Messages',
      description: 'Monitor progress',
      icon: <SendOutlined />
    }
  ];

  return (
    <div>
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <h1>Bulk Messaging (Profile-based)</h1>
          <p>Send messages to users via their profile pages with human-like behavior</p>
        </Col>
        <Col>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => {
              setLastCompletedTasksLoad(0); // Reset debounce
              loadInitialData();
            }}
            loading={loading}
          >
            Refresh
          </Button>
        </Col>
      </Row>

      {/* Progress Steps */}
      <Card style={{ marginBottom: 24 }}>
        {console.log('🔍 [BulkMessaging] Rendering Steps with currentStep:', currentStep)}
        <Steps current={currentStep} items={steps} />
      </Card>

      <Row gutter={[16, 16]}>
        {/* Step 1: File Upload */}
        <Col xs={24} lg={12}>
          <Card title="Step 1: Upload Recipient List">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Alert
                message="Excel File Format"
                description="Your Excel file should have 2 columns: 'full_name' and 'profile_url'. Profile URLs should be Facebook profile URLs or paths like '/groups/123/user/456'."
                type="info"
                showIcon
              />

              <Upload
                beforeUpload={handleFileUpload}
                accept=".csv,.xlsx,.xls"
                showUploadList={false}
                disabled={loading}
              >
                <Button 
                  icon={<UploadOutlined />} 
                  loading={loading}
                  size="large"
                  block
                >
                  Upload Excel/CSV File
                </Button>
              </Upload>

              {fileValidation && (
                <div>
                  {fileValidation.valid ? (
                    <Alert
                      message="File Validated Successfully"
                      description={
                        <div>
                          <p><strong>Total Recipients:</strong> {fileValidation.total_recipients}</p>
                          {fileValidation.url_type_breakdown && (
                            <div>
                              <p><strong>URL Types:</strong></p>
                              <ul>
                                <li>Groups: {fileValidation.url_type_breakdown.groups}</li>
                                <li>Profile PHP: {fileValidation.url_type_breakdown.profile_php}</li>
                                <li>People: {fileValidation.url_type_breakdown.people}</li>
                                <li>Direct Username: {fileValidation.url_type_breakdown.direct_username}</li>
                                <li>Other: {fileValidation.url_type_breakdown.other}</li>
                              </ul>
                            </div>
                          )}
                        </div>
                      }
                      type="success"
                      showIcon
                    />
                  ) : (
                    <Alert
                      message="File Validation Failed"
                      description={fileValidation.error}
                      type="error"
                      showIcon
                    />
                  )}

                  {fileValidation.preview && fileValidation.preview.length > 0 && (
                    <div style={{ marginTop: 16 }}>
                      <h4>Preview (First 5 rows):</h4>
                      <Table
                        size="small"
                        dataSource={fileValidation.preview}
                        columns={[
                          { title: 'Row', dataIndex: 'row', key: 'row' },
                          { 
                            title: 'Data', 
                            dataIndex: 'data', 
                            key: 'data',
                            render: (data) => (
                              <pre style={{ fontSize: '12px', margin: 0 }}>
                                {JSON.stringify(data, null, 2)}
                              </pre>
                            )
                          }
                        ]}
                        pagination={false}
                      />
                    </div>
                  )}
                </div>
              )}
            </Space>
          </Card>
        </Col>

        {/* Step 2: Configuration */}
        <Col xs={24} lg={12}>
          <Card title="Step 2: Configure Messaging">
            <Form
              form={form}
              layout="vertical"
              onFinish={handleStartBulkMessaging}
            >
              <Form.Item
                name="name"
                label="Task Name"
                rules={[{ required: true, message: 'Please enter task name' }]}
              >
                <Input placeholder="Enter task name" />
              </Form.Item>

              <Form.Item
                name="sender_profile_id"
                label="Sender Profile"
                rules={[{ required: true, message: 'Please select sender profile' }]}
              >
                <Select placeholder="Select profile to send messages from">
                  {console.log('🔍 [BulkMessaging] Rendering profiles dropdown with:', profiles.length, 'profiles')}
                  {profiles.map(profile => {
                    console.log('🔍 [BulkMessaging] Rendering profile option:', profile.id, profile.name);
                    return (
                      <Option key={profile.id} value={profile.id}>
                        {profile.name} {profile.facebook_username ? `(${profile.facebook_username})` : '(Facebook Logged In)'}
                      </Option>
                    );
                  })}
                </Select>
              </Form.Item>

              <Form.Item
                name="message_content"
                label="Message Content"
                rules={[{ required: true, message: 'Please enter message content' }]}
              >
                <TextArea
                  rows={4}
                  placeholder="Enter your message content..."
                />
              </Form.Item>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="delay_between_messages_min"
                    label="Min Delay (seconds)"
                    initialValue={10}
                  >
                    <InputNumber min={5} max={300} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="delay_between_messages_max"
                    label="Max Delay (seconds)"
                    initialValue={30}
                  >
                    <InputNumber min={10} max={600} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="stop_on_consecutive_failures"
                label="Stop after consecutive failures"
                initialValue={3}
              >
                <InputNumber min={1} max={10} style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="skip_sent_recipients"
                label="Skip already sent recipients"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch />
              </Form.Item>

              <Button
                type="primary"
                htmlType="submit"
                icon={<PlayCircleOutlined />}
                loading={loading}
                disabled={!uploadedFile || !fileValidation?.valid}
                block
                size="large"
              >
                Start Bulk Messaging
              </Button>
            </Form>
          </Card>
        </Col>
      </Row>

      {/* Active Task Status */}
      {console.log('🔍 [BulkMessaging] Checking Step 3 render condition. activeTask:', activeTask, 'hasTaskProgress:', !!taskProgress[activeTask])}
      {activeTask && taskProgress[activeTask] && (
        <Card title="Step 3: Messaging Progress" style={{ marginTop: 24 }}>
          <Row gutter={16}>
            <Col xs={24} lg={16}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <strong>Status:</strong> {getStatusTag(taskProgress[activeTask].status)}
                </div>

                <Progress
                  percent={taskProgress[activeTask].progress}
                  status={taskProgress[activeTask].status === 'failed' ? 'exception' :
                          taskProgress[activeTask].status === 'completed' ? 'success' : 'active'}
                  strokeColor={{
                    '0%': '#108ee9',
                    '100%': '#87d068',
                  }}
                />

                <Row gutter={16}>
                  <Col span={6}>
                    <Statistic
                      title="Total"
                      value={taskProgress[activeTask].total_recipients}
                      prefix={<UserOutlined />}
                    />
                  </Col>
                  <Col span={6}>
                    <Statistic
                      title="Sent"
                      value={taskProgress[activeTask].messages_sent}
                      prefix={<CheckCircleOutlined />}
                      valueStyle={{ color: '#3f8600' }}
                    />
                  </Col>
                  <Col span={6}>
                    <Statistic
                      title="Failed"
                      value={taskProgress[activeTask].messages_failed}
                      prefix={<CloseCircleOutlined />}
                      valueStyle={{ color: '#cf1322' }}
                    />
                  </Col>
                  <Col span={6}>
                    <Statistic
                      title="Consecutive Failures"
                      value={taskProgress[activeTask].consecutive_failures}
                      prefix={<ClockCircleOutlined />}
                      valueStyle={{ color: '#faad14' }}
                    />
                  </Col>
                </Row>

                {/* Final Statistics - Show when task is completed */}
                {console.log('🔍 [BulkMessaging] Checking final statistics display. activeTask:', activeTask)}
                {console.log('🔍 [BulkMessaging] Current step:', currentStep)}
                {console.log('🔍 [BulkMessaging] taskProgress[activeTask]:', taskProgress[activeTask])}
                {activeTask && taskProgress[activeTask] && console.log('🔍 [BulkMessaging] Task status:', taskProgress[activeTask].status)}
                {activeTask && taskProgress[activeTask] && console.log('🔍 [BulkMessaging] Has final_statistics:', !!taskProgress[activeTask].final_statistics)}
                {console.log('🔍 [BulkMessaging] Should show final statistics:', !!(taskProgress[activeTask] && taskProgress[activeTask].status === 'completed' && taskProgress[activeTask].final_statistics))}
                {taskProgress[activeTask] && taskProgress[activeTask].status === 'completed' && taskProgress[activeTask].final_statistics && (
                  <div style={{ marginTop: 24 }}>
                    <Divider orientation="left">📊 Final Statistics</Divider>
                    <Alert
                      message="Task Completed Successfully!"
                      description={
                        <div>
                          <p><strong>🎉 Bulk messaging task has been completed and browser has been closed.</strong></p>
                          <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
                            <Col span={8}>
                              <Statistic
                                title="Success Rate"
                                value={taskProgress[activeTask].final_statistics.success_rate}
                                suffix="%"
                                valueStyle={{ color: '#3f8600' }}
                              />
                            </Col>
                            <Col span={8}>
                              <Statistic
                                title="Total Time"
                                value={taskProgress[activeTask].final_statistics.total_time_formatted}
                                valueStyle={{ color: '#1890ff' }}
                              />
                            </Col>
                            <Col span={8}>
                              <Statistic
                                title="Avg Time/Message"
                                value={taskProgress[activeTask].final_statistics.average_time_per_message}
                                suffix="s"
                                valueStyle={{ color: '#722ed1' }}
                              />
                            </Col>
                          </Row>
                          <div style={{ marginTop: 16 }}>
                            <Text type="secondary">
                              🌐 Browser Status: {taskProgress[activeTask].final_statistics.browser_closed ? 'Closed' : 'Open'}
                            </Text>
                            <br />
                            <Text type="secondary">
                              ⏰ Completed at: {new Date(taskProgress[activeTask].final_statistics.completed_at).toLocaleString()}
                            </Text>
                          </div>
                        </div>
                      }
                      type="success"
                      showIcon
                    />
                  </div>
                )}
              </Space>
            </Col>

            <Col xs={24} lg={8}>
              <Space direction="vertical" style={{ width: '100%' }}>
                {taskProgress[activeTask].status === 'running' ? (
                  <Button
                    danger
                    icon={<StopOutlined />}
                    onClick={() => handleStopTask(activeTask)}
                    block
                  >
                    Stop Task
                  </Button>
                ) : taskProgress[activeTask].status === 'completed' ? (
                  <Button
                    type="default"
                    icon={<DeleteOutlined />}
                    onClick={() => {
                      setTaskProgress(prev => {
                        const newProgress = { ...prev };
                        delete newProgress[activeTask];
                        return newProgress;
                      });
                      setActiveTaskWithDebug(null);
                    }}
                    block
                  >
                    Clear Results
                  </Button>
                ) : null}

                <Button
                  icon={<EyeOutlined />}
                  onClick={() => handleViewResults(activeTask)}
                  block
                >
                  View Results
                </Button>
              </Space>
            </Col>
          </Row>
        </Card>
      )}

      {/* Completed Tasks History */}
      {console.log('🔍 [BulkMessaging] Rendering completed tasks section. completedTasks.length:', completedTasks.length)}
      {console.log('🔍 [BulkMessaging] completedTasks data:', completedTasks)}
      {completedTasks.length > 0 && (
        <Card title="📊 Recent Completed Tasks" style={{ marginTop: 24 }}>
          <div style={{ marginBottom: 16 }}>
            <Button
              type="link"
              onClick={() => setShowCompletedTasks(!showCompletedTasks)}
              icon={showCompletedTasks ? <UpOutlined /> : <DownOutlined />}
            >
              {showCompletedTasks ? 'Hide' : 'Show'} {completedTasks.length} completed task(s)
            </Button>
          </div>

          {showCompletedTasks && (
            <List
              dataSource={completedTasks}
              renderItem={(task) => (
                <List.Item
                  actions={[
                    <Button
                      type="link"
                      onClick={() => {
                        console.log('🔍 [BulkMessaging] Found active task on page load, checking status:', task.task_id);
                        setActiveTaskWithDebug(task.task_id);
                        setShowCompletedTasks(false);
                        setCurrentStepWithDebug(0); // Show completed results
                      }}
                    >
                      View Details
                    </Button>
                  ]}
                >
                  <List.Item.Meta
                    title={
                      <div>
                        <Text strong>{task.task_name}</Text>
                        <Tag color="green" style={{ marginLeft: 8 }}>Completed</Tag>
                      </div>
                    }
                    description={
                      <div>
                        <Text type="secondary">
                          📤 {task.messages_sent} sent • ❌ {task.messages_failed} failed •
                          📈 {task.success_rate}% success • ⏱️ {task.total_time_formatted}
                        </Text>
                        <br />
                        <Text type="secondary">
                          Completed: {new Date(task.completed_at).toLocaleString()}
                        </Text>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          )}
        </Card>
      )}

      {/* Results Modal */}
      <Modal
        title="Bulk Messaging Results"
        open={previewModalVisible}
        onCancel={() => setPreviewModalVisible(false)}
        width={1000}
        footer={[
          <Button key="close" onClick={() => setPreviewModalVisible(false)}>
            Close
          </Button>
        ]}
      >
        {selectedTaskResults && (
          <div>
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={6}>
                <Statistic title="Total Recipients" value={selectedTaskResults.total_recipients} />
              </Col>
              <Col span={6}>
                <Statistic title="Messages Sent" value={selectedTaskResults.messages_sent} />
              </Col>
              <Col span={6}>
                <Statistic title="Failed" value={selectedTaskResults.messages_failed} />
              </Col>
              <Col span={6}>
                <Statistic
                  title="Success Rate"
                  value={selectedTaskResults.success_rate.toFixed(1)}
                  suffix="%"
                />
              </Col>
            </Row>

            <Divider />

            <Row gutter={16}>
              <Col span={12}>
                <h4>Successful Recipients ({selectedTaskResults.successful_recipients.length})</h4>
                <List
                  size="small"
                  dataSource={selectedTaskResults.successful_recipients.slice(0, 10)}
                  renderItem={(item) => (
                    <List.Item>
                      <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
                      {item.recipient.full_name}
                    </List.Item>
                  )}
                />
              </Col>
              <Col span={12}>
                <h4>Failed Recipients ({selectedTaskResults.failed_recipients.length})</h4>
                <List
                  size="small"
                  dataSource={selectedTaskResults.failed_recipients.slice(0, 10)}
                  renderItem={(item) => (
                    <List.Item>
                      <CloseCircleOutlined style={{ color: '#ff4d4f', marginRight: 8 }} />
                      {item.recipient.full_name}
                      <br />
                      <small style={{ color: '#999' }}>{item.result.error}</small>
                    </List.Item>
                  )}
                />
              </Col>
            </Row>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default BulkMessaging;
