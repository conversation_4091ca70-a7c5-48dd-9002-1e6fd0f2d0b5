/**
 * Profile Manager Page Component
 */

import React, { useState, useEffect } from 'react';
import {
  Table, Button, Space, Modal, Form, Input, Select, Card, Tag,
  message, Popconfirm, Tooltip, Row, Col, InputNumber, Tabs, Badge, Alert, Spin
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined, PlayCircleOutlined,
  StopOutlined, LoginOutlined, EyeOutlined, ReloadOutlined,
  FacebookOutlined, InstagramOutlined, CheckCircleOutlined, CloseOutlined,
  ShareAltOutlined, SyncOutlined, UserOutlined, RocketOutlined
} from '@ant-design/icons';
import AdminProfileSharing from '../components/ProfileSharing/AdminProfileSharing';
import UserSharedProfiles from '../components/ProfileSharing/UserSharedProfiles';
import ProfileSyncStatus from '../components/ProfileSharing/ProfileSyncStatus';
import ProfileDataManager from '../components/ProfileDataManager';
import { apiService } from '../services/api';

const { Option } = Select;

const ProfileManager = () => {
  const [profiles, setProfiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingProfile, setEditingProfile] = useState(null);
  const [loginModalVisible, setLoginModalVisible] = useState(false);
  const [selectedProfile, setSelectedProfile] = useState(null);
  const [activeTab, setActiveTab] = useState('profiles');
  const [userRole, setUserRole] = useState('user'); // This should come from auth context
  const [profileDataManagerVisible, setProfileDataManagerVisible] = useState(false);
  const [selectedProfileForData, setSelectedProfileForData] = useState(null);
  const [checkingLoginStatus, setCheckingLoginStatus] = useState(false);
  const [loginStatusResults, setLoginStatusResults] = useState({});
  const [autoCheckCompleted, setAutoCheckCompleted] = useState(false);
  const [profilesLoaded, setProfilesLoaded] = useState(false);
  const [form] = Form.useForm();
  const [loginForm] = Form.useForm();

  useEffect(() => {
    console.log('🚀 [ProfileManager] Component mounted, loading data...');
    console.log('🚀 [ProfileManager] Initial state - checkingLoginStatus:', checkingLoginStatus);
    console.log('🚀 [ProfileManager] Initial state - loginStatusResults:', loginStatusResults);

    loadProfiles();
    loadUserRole();
  }, []);

  // Auto check login status when profiles are loaded (only once)
  useEffect(() => {
    console.log('🔄 [AUTO_CHECK_EFFECT] useEffect triggered');
    console.log('🔄 [AUTO_CHECK_EFFECT] profiles.length:', profiles.length);
    console.log('🔄 [AUTO_CHECK_EFFECT] profilesLoaded:', profilesLoaded);
    console.log('🔄 [AUTO_CHECK_EFFECT] checkingLoginStatus:', checkingLoginStatus);
    console.log('🔄 [AUTO_CHECK_EFFECT] autoCheckCompleted:', autoCheckCompleted);

    // Only auto-check if profiles are loaded, there are profiles, and we haven't checked yet
    if (profilesLoaded && profiles.length > 0 && !checkingLoginStatus && !autoCheckCompleted) {
      console.log('🔄 [AUTO_CHECK_EFFECT] Conditions met, starting auto-check...');
      console.log('🔄 [AUTO_CHECK_EFFECT] Profiles count:', profiles.length);

      // Set flag to prevent multiple auto-checks
      setAutoCheckCompleted(true);

      // Show info message about auto-check
      message.info('Automatically checking login status for all profiles...');

      // Auto check login status for all profiles
      console.log('🔄 [AUTO_CHECK_EFFECT] Calling handleCheckLoginStatus...');
      console.log('🔄 [AUTO_CHECK_EFFECT] handleCheckLoginStatus function:', typeof handleCheckLoginStatus);

      // Use setTimeout to avoid potential timing issues
      setTimeout(() => {
        console.log('🔄 [AUTO_CHECK_EFFECT] Executing handleCheckLoginStatus in setTimeout...');
        handleCheckLoginStatus();
      }, 100);
    } else if (profilesLoaded && profiles.length === 0 && !autoCheckCompleted) {
      // If profiles are loaded but no profiles exist, mark auto-check as completed
      console.log('🔄 [AUTO_CHECK_EFFECT] Profiles loaded but no profiles found, skipping auto-check');
      setAutoCheckCompleted(true);
    } else {
      console.log('🔄 [AUTO_CHECK_EFFECT] Conditions not met, skipping auto-check');
      console.log('🔄 [AUTO_CHECK_EFFECT] Reason: profilesLoaded =', profilesLoaded, ', profiles.length =', profiles.length, ', checkingLoginStatus =', checkingLoginStatus, ', autoCheckCompleted =', autoCheckCompleted);
    }
  }, [profilesLoaded, profiles.length, autoCheckCompleted]); // Added profilesLoaded to dependencies

  const loadUserRole = async () => {
    try {
      // This should get user role from auth context or API
      // For now, we'll assume admin role for demo
      setUserRole('admin');
    } catch (error) {
      console.error('Failed to load user role:', error);
    }
  };

  const loadProfiles = async (resetAutoCheck = true) => {
    console.log('🚀 [loadProfiles] Starting loadProfiles function...');
    console.log('🚀 [loadProfiles] resetAutoCheck:', resetAutoCheck);
    try {
      setLoading(true);
      // Reset flags when loading profiles
      setProfilesLoaded(false);
      if (resetAutoCheck) {
        setAutoCheckCompleted(false);
        console.log('📋 [loadProfiles] Reset auto-check flag');
      }
      console.log('📋 [loadProfiles] Loading profiles...');

      // Try to use the new API endpoint that includes access control
      try {
        console.log('🔄 [loadProfiles] Trying new API: /api/profiles/my-profiles');
        const response = await apiService.get('/api/profiles/my-profiles');
        console.log('📊 [loadProfiles] New API response:', response);
        console.log('📊 [loadProfiles] New API response structure:', JSON.stringify(response, null, 2));

        // Extract data from standardized response format
        let data = [];

        if (response && typeof response === 'object') {
          // Check for nested data structure: response.data.data (double wrapped)
          if (response.data && response.data.success !== undefined && response.data.data !== undefined) {
            console.log('📊 New API: Double-wrapped standardized response detected');
            if (response.data.success && Array.isArray(response.data.data)) {
              data = response.data.data;
            }
          }
          // Check if it's a standardized response with success/data structure
          else if (response.success !== undefined && response.data !== undefined) {
            console.log('📊 New API: Standardized response detected');
            if (response.success && Array.isArray(response.data)) {
              data = response.data;
            }
          }
          // Check if response.data exists and is an array (axios response)
          else if (response.data && Array.isArray(response.data)) {
            console.log('📊 New API: Axios response detected');
            data = response.data;
          }
          // Check if response itself is an array (direct data)
          else if (Array.isArray(response)) {
            console.log('📊 New API: Direct array response detected');
            data = response;
          }
        }

        if (data && Array.isArray(data) && data.length > 0) {
          // Check if data has nested structure (item.profile) or direct structure
          let transformedProfiles;
          if (data[0] && data[0].profile) {
            // Nested structure: {profile: {...}, access_info: {...}, account_info: {...}}
            transformedProfiles = data.map(item => ({
              ...item.profile,
              access_info: item.access_info,
              account_info: item.account_info
            }));
            console.log('✅ [loadProfiles] Using nested structure transformation');
          } else {
            // Direct structure: profiles array directly
            transformedProfiles = data;
            console.log('✅ [loadProfiles] Using direct structure (no transformation needed)');
          }

          console.log('✅ [loadProfiles] Loaded profiles from new API:', transformedProfiles.length);
          console.log('📊 [loadProfiles] Profile IDs:', transformedProfiles.map(p => ({ id: p.id, name: p.name })));
          setProfiles(transformedProfiles);
          setProfilesLoaded(true);
          console.log('🏁 [loadProfiles] New API success - profiles loaded flag set to true');
          return;
        }
      } catch (newApiError) {
        console.log('⚠️ [loadProfiles] New API not available, falling back to original API:', newApiError.message);
      }

      // Fallback to original API for backward compatibility
      console.log('🔄 [loadProfiles] Trying original API: /api/profiles/');
      const response = await apiService.getProfiles();
      console.log('✅ [loadProfiles] API response:', response);
      console.log('📊 [loadProfiles] Response type:', typeof response);
      console.log('📊 [loadProfiles] Response structure:', JSON.stringify(response, null, 2));

      // Extract data from standardized response format
      let data = [];

      if (response && typeof response === 'object') {
        // Check for nested data structure: response.data.data (double wrapped)
        if (response.data && response.data.success !== undefined && response.data.data !== undefined) {
          console.log('📊 Double-wrapped standardized response detected');
          if (response.data.success && Array.isArray(response.data.data)) {
            data = response.data.data;
          } else {
            console.error('❌ API call failed or nested data is not an array:', response.data);
            data = [];
          }
        }
        // Check if it's a standardized response with success/data structure
        else if (response.success !== undefined && response.data !== undefined) {
          console.log('📊 Standardized response detected');
          if (response.success && Array.isArray(response.data)) {
            data = response.data;
          } else {
            console.error('❌ API call failed or data is not an array:', response);
            data = [];
          }
        }
        // Check if response.data exists and is an array (axios response)
        else if (response.data && Array.isArray(response.data)) {
          console.log('📊 Axios response detected');
          data = response.data;
        }
        // Check if response itself is an array (direct data)
        else if (Array.isArray(response)) {
          console.log('📊 Direct array response detected');
          data = response;
        }
        // Try other possible structures
        else if (response.profiles && Array.isArray(response.profiles)) {
          console.log('📊 Profiles wrapper detected');
          data = response.profiles;
        } else if (response.items && Array.isArray(response.items)) {
          console.log('📊 Items wrapper detected');
          data = response.items;
        } else {
          console.error('❌ Could not find array data in response:', response);
          data = [];
        }
      } else {
        console.error('❌ Invalid response format:', response);
        data = [];
      }

      console.log('✅ [loadProfiles] Loaded profiles from original API:', data?.length || 0);
      console.log('📊 [loadProfiles] Profile IDs:', data?.map(p => ({ id: p.id, name: p.name })) || []);
      setProfiles(data || []);
      setProfilesLoaded(true);
      console.log('🏁 [loadProfiles] Original API success - profiles loaded flag set to true');
    } catch (error) {
      console.error('❌ [loadProfiles] Failed to load profiles:', error);
      console.error('❌ [loadProfiles] Error message:', error.message);
      console.error('❌ [loadProfiles] Error stack:', error.stack);
      message.error('Failed to load profiles: ' + error.message);
    } finally {
      console.log('🏁 [loadProfiles] Finally block - setting loading false');
      setLoading(false);
    }
  };

  // Function to refresh profiles without triggering auto-check
  const refreshProfilesOnly = async () => {
    console.log('🔄 [refreshProfilesOnly] Refreshing profiles without auto-check...');
    try {
      // Try to use the new API endpoint that includes access control
      try {
        console.log('🔄 [refreshProfilesOnly] Trying new API: /api/profiles/my-profiles');
        const response = await apiService.getProfiles();
        console.log('✅ [refreshProfilesOnly] New API response:', response);

        let data = [];
        if (response && typeof response === 'object') {
          // Check for nested data structure: response.data.data (double wrapped)
          if (response.data && response.data.success !== undefined && response.data.data !== undefined) {
            console.log('📊 New API: Double-wrapped standardized response detected');
            if (response.data.success && Array.isArray(response.data.data)) {
              data = response.data.data;
            }
          }
          // Check if it's a standardized response with success/data structure
          else if (response.success !== undefined && response.data !== undefined) {
            console.log('📊 New API: Standardized response detected');
            if (response.success && Array.isArray(response.data)) {
              data = response.data;
            }
          }
          // Check if response.data exists and is an array (axios response)
          else if (response.data && Array.isArray(response.data)) {
            console.log('📊 New API: Axios response detected');
            data = response.data;
          }
          // Check if response itself is an array (direct data)
          else if (Array.isArray(response)) {
            console.log('📊 New API: Direct array response detected');
            data = response;
          }
        }

        if (data && Array.isArray(data) && data.length > 0) {
          // Check if data has nested structure (item.profile) or direct structure
          let transformedProfiles;
          if (data[0] && data[0].profile) {
            // Nested structure: {profile: {...}, access_info: {...}, account_info: {...}}
            transformedProfiles = data.map(item => ({
              ...item.profile,
              access_info: item.access_info,
              account_info: item.account_info
            }));
            console.log('✅ [refreshProfilesOnly] Using nested structure transformation');
          } else {
            // Direct structure: profiles array directly
            transformedProfiles = data;
            console.log('✅ [refreshProfilesOnly] Using direct structure (no transformation needed)');
          }

          console.log('✅ [refreshProfilesOnly] Loaded profiles from new API:', transformedProfiles.length);
          setProfiles(transformedProfiles);
          return;
        }
      } catch (newApiError) {
        console.log('⚠️ [refreshProfilesOnly] New API not available, falling back to original API:', newApiError.message);
      }

      // Fallback to original API
      console.log('🔄 [refreshProfilesOnly] Trying original API: /api/profiles/');
      const response = await apiService.getProfiles();

      // Extract data from response (same logic as loadProfiles)
      let data = [];
      if (response && typeof response === 'object') {
        if (response.data && response.data.success !== undefined && response.data.data !== undefined) {
          if (response.data.success && Array.isArray(response.data.data)) {
            data = response.data.data;
          }
        } else if (response.success !== undefined && response.data !== undefined) {
          if (response.success && Array.isArray(response.data)) {
            data = response.data;
          }
        } else if (response.data && Array.isArray(response.data)) {
          data = response.data;
        } else if (Array.isArray(response)) {
          data = response;
        } else if (response.profiles && Array.isArray(response.profiles)) {
          data = response.profiles;
        } else if (response.items && Array.isArray(response.items)) {
          data = response.items;
        }
      }

      console.log('✅ [refreshProfilesOnly] Loaded profiles:', data?.length || 0);
      setProfiles(data || []);
    } catch (error) {
      console.error('❌ [refreshProfilesOnly] Failed to refresh profiles:', error);
      message.error('Failed to refresh profiles: ' + error.message);
    }
  };

  const handleCreateProfile = () => {
    setEditingProfile(null);
    setModalVisible(true);
    form.resetFields();
  };

  const handleEditProfile = (profile) => {
    setEditingProfile(profile);
    setModalVisible(true);
    form.setFieldsValue({
      name: profile.name,
      type: profile.type || 'facebook',  // Add profile type
      proxy_type: profile.proxy_config?.type || 'no_proxy',
      proxy_host: profile.proxy_config?.host || '',
      proxy_port: profile.proxy_config?.port || '',
      proxy_username: profile.proxy_config?.username || '',
      proxy_password: profile.proxy_config?.password || ''
    });
  };

  const handleSubmit = async (values) => {
    try {
      const profileData = {
        name: values.name,
        type: values.type || 'facebook',  // Add profile type
        proxy_config: {
          type: values.proxy_type || 'no_proxy',
          host: values.proxy_host,
          port: values.proxy_port,
          username: values.proxy_username,
          password: values.proxy_password
        }
      };

      if (editingProfile) {
        await apiService.updateProfile(editingProfile.id, profileData);
        message.success('Profile updated successfully');
      } else {
        await apiService.createProfile(profileData);
        message.success('Profile created successfully');
      }

      setModalVisible(false);
      refreshProfilesOnly();
    } catch (error) {
      message.error('Failed to save profile: ' + error.message);
    }
  };

  const handleCheckLoginStatus = async (profileIds = null) => {
    console.log('🔍 [handleCheckLoginStatus] Function called with profileIds:', profileIds);
    console.log('🔍 [handleCheckLoginStatus] Current profiles:', profiles);

    try {
      setCheckingLoginStatus(true);
      console.log('🔍 [handleCheckLoginStatus] Set checking status to true');

      // If no profileIds provided, check all profiles
      const idsToCheck = profileIds || profiles.map(p => p.id);
      console.log('🔍 [handleCheckLoginStatus] IDs to check:', idsToCheck);

      if (idsToCheck.length === 0) {
        message.warning('No profiles available to check');
        setCheckingLoginStatus(false);
        return;
      }

      message.info(`Checking login status for ${idsToCheck.length} profile(s)... This might take a moment.`);
      console.log('🌐 [handleCheckLoginStatus] Calling apiService.checkLoginStatus...');

      const response = await apiService.checkLoginStatus(idsToCheck);
      console.log('✅ [handleCheckLoginStatus] API response received:', response);

      if (response.success) {
        console.log('✅ [handleCheckLoginStatus] Response successful, processing results...');
        console.log('📊 [handleCheckLoginStatus] Full response:', response);

        // Handle standardized response format
        const responseData = response.data || response;
        const results = responseData.results || [];
        const summary = responseData.summary || {};

        console.log('📊 [handleCheckLoginStatus] Extracted results:', results);
        console.log('📊 [handleCheckLoginStatus] Extracted summary:', summary);

        // Update login status results
        const statusMap = {};
        if (Array.isArray(results)) {
          results.forEach(result => {
            statusMap[result.profile_id] = result;
          });
        }
        setLoginStatusResults(prevResults => ({ ...prevResults, ...statusMap }));
        console.log('📊 [handleCheckLoginStatus] Status map updated:', statusMap);

        // Show summary message
        console.log('📊 [handleCheckLoginStatus] Summary:', summary);
        const totalChecked = summary.total_checked || 0;
        const loggedIn = summary.logged_in || 0;
        const loggedOut = summary.logged_out || 0;
        const errors = summary.errors || 0;

        message.success(
          `Login status check completed: ${loggedIn} logged in, ${loggedOut} logged out, ${errors} errors`
        );

        // Reload profiles to get updated status (don't trigger auto-check)
        console.log('🔄 [handleCheckLoginStatus] Reloading profiles...');
        await refreshProfilesOnly();
        console.log('✅ [handleCheckLoginStatus] Profiles reloaded');
      } else {
        console.error('❌ [handleCheckLoginStatus] Response failed:', response);
        message.error('Failed to check login status: ' + (response.message || 'Unknown error'));
      }
    } catch (error) {
      console.error('❌ [handleCheckLoginStatus] Exception caught:', error);
      message.error('Failed to check login status: ' + error.message);
    } finally {
      console.log('🏁 [handleCheckLoginStatus] Setting checking status to false');
      setCheckingLoginStatus(false);
    }
  };

  const handleCheckSingleProfile = async (profileId, platform) => {
    console.log('🔍 [handleCheckSingleProfile] Function called with:', { profileId, platform });

    try {
      setCheckingLoginStatus(true);
      console.log('🔍 [handleCheckSingleProfile] Set checking status to true');

      message.info('Checking login status...');
      console.log('🌐 [handleCheckSingleProfile] Calling apiService.checkSingleProfileLoginStatus...');

      const response = await apiService.checkSingleProfileLoginStatus(profileId, platform);
      console.log('✅ [handleCheckSingleProfile] API response received:', response);

      if (response.success) {
        console.log('✅ [handleCheckSingleProfile] Response successful:', response);

        // Handle standardized response format
        const responseData = response.data || response;
        const results = responseData.results || [];

        if (results.length > 0) {
          const result = results[0];

          // Update login status results
          setLoginStatusResults(prev => ({
            ...prev,
            [profileId]: result
          }));

          const statusText = result.login_status === 'logged_in' ? 'logged in' :
                            result.login_status === 'logged_out' ? 'logged out' : 'error';
          message.success(`Profile ${result.name} is ${statusText}`);

          // Reload profiles to get updated status
          await refreshProfilesOnly();
        } else {
          message.warning('No results returned from login status check');
        }
      } else {
        message.error('Failed to check login status: ' + (response.message || 'Unknown error'));
      }
    } catch (error) {
      message.error('Failed to check login status: ' + error.message);
    } finally {
      setCheckingLoginStatus(false);
    }
  };

  const handleDeleteProfile = async (profileId) => {
    try {
      console.log('🗑️ Deleting profile:', profileId);
      console.log('Current profiles before deletion:', profiles.map(p => ({ id: p.id, name: p.name })));

      const response = await apiService.deleteProfile(profileId);
      console.log('✅ Delete response:', response);

      message.success('Profile deleted successfully');

      // Immediately refresh the profile list
      console.log('🔄 Refreshing profile list after deletion...');
      await refreshProfilesOnly();

      console.log('✅ Profile list refreshed');

    } catch (error) {
      console.error('❌ Delete profile error:', error);
      message.error('Failed to delete profile: ' + error.message);
    }
  };

  const handleTestProfile = async (profile) => {
    try {
      message.loading('Testing profile...', 0);
      const result = await apiService.testProfile(profile.id);
      message.destroy();
      
      if (result.success) {
        message.success(`Profile test successful! IP: ${result.ip_address}`);
      } else {
        message.error(`Profile test failed: ${result.message}`);
      }

      refreshProfilesOnly();
    } catch (error) {
      message.destroy();
      message.error('Failed to test profile: ' + error.message);
    }
  };

  const handleLoginFacebook = (profile) => {
    setSelectedProfile(profile);
    setLoginModalVisible(true);
    loginForm.resetFields();
  };

  const handleOpenProfileDataManager = (profile) => {
    setSelectedProfileForData(profile);
    setProfileDataManagerVisible(true);
  };

  const handleLaunchBrowser = async (profile) => {
    try {
      setLoading(true);
      console.log('🚀 Launching browser for profile:', profile);

      // First check browser status
      try {
        const statusResult = await apiService.getBrowserStatus(profile.id);
        if (statusResult && statusResult.success && statusResult.browser_active) {
          message.info('Browser is already launched for this profile');
          return;
        }
      } catch (statusError) {
        console.log('⚠️ Could not check browser status, proceeding with launch:', statusError.message);
      }

      // Try the NestJS launch endpoint first (for account-based profiles)
      try {
        console.log(`🔄 Trying NestJS API: /profiles/${profile.id}/launch-browser`);
        const response = await apiService.launchBrowserWithProfile(profile.id);
        console.log('✅ Launch browser response:', response);
        console.log('📊 Response status:', response?.status);
        console.log('📊 Response data:', response?.data);
        console.log('📊 Response data type:', typeof response?.data);
        console.log('📊 Response data success:', response?.data?.success);

        if (response && (response.success || response.data?.success)) {
          if (response.navigated_to_facebook || response.data?.navigated_to_facebook) {
            message.success('Browser launched successfully and navigated to Facebook login page');
          } else {
            message.success('Browser launched successfully');
          }
          refreshProfilesOnly();
          return;
        }
      } catch (newApiError) {
        console.log('⚠️ NestJS launch API not available, falling back to FastAPI:', newApiError.message);
      }

      // Fallback to original launch method
      console.log('🔄 Trying original API: /api/profiles/${profile.id}/launch-browser');
      const result = await apiService.launchBrowser(profile.id, false);
      console.log('✅ Original launch response:', result);

      if (result && result.success) {
        if (result.already_active) {
          message.info('Browser is already launched for this profile');
        } else if (result.navigated_to_facebook) {
          message.success('Browser launched successfully and navigated to Facebook login page');
        } else {
          message.success('Browser launched successfully');
        }
        refreshProfilesOnly(); // Refresh profiles
      } else {
        message.error(result?.message || 'Failed to launch browser');
      }
    } catch (error) {
      console.error('❌ Launch browser error:', error);
      message.error('Failed to launch browser: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleOpenSocial = async (profile) => {
    try {
      setLoading(true);
      const platformName = profile.type === 'instagram' ? 'Instagram' : 'Facebook';
      const result = await apiService.openSocial(profile.id);
      if (result.success) {
        if (result.logged_in) {
          message.success(`${platformName} opened successfully! You are already logged in with saved cookies.`);
        } else {
          message.info(`${platformName} login page opened. Please complete login manually, then click "Complete Login".`);
        }
      } else {
        message.error(result.message || `Failed to open ${platformName}`);
      }
    } catch (error) {
      message.error(`Failed to open ${platformName}: ` + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleCompleteSocialLogin = async (profile) => {
    const platformName = profile.type === 'instagram' ? 'Instagram' : 'Facebook';
    console.log(`🚀 [handleCompleteSocialLogin] Starting ${platformName} login for profile:`, profile.id);
    try {
      setLoading(true);
      console.log(`🔄 [handleCompleteSocialLogin] Calling apiService.completeFacebookLogin...`);
      const result = await apiService.completeFacebookLogin(profile.id);
      console.log(`✅ [handleCompleteSocialLogin] API call completed`);

      // Debug logging
      console.log('🔍 [handleCompleteFacebookLogin] Complete Facebook Login Response:', result);
      console.log('🔍 [handleCompleteFacebookLogin] Result type:', typeof result);
      console.log('🔍 [handleCompleteFacebookLogin] Result keys:', Object.keys(result || {}));
      console.log('🔍 [handleCompleteFacebookLogin] Result JSON:', JSON.stringify(result, null, 2));

      // API service already handles response format, so use result directly
      const isSuccess = result && result.success === true;
      const isLoggedIn = result && result.logged_in === true;

      console.log('🔍 [handleCompleteFacebookLogin] result.success:', result?.success);
      console.log('🔍 [handleCompleteFacebookLogin] result.logged_in:', result?.logged_in);
      console.log('🔍 [handleCompleteFacebookLogin] result.message:', result?.message);
      console.log('🔍 [handleCompleteFacebookLogin] result.facebook_username:', result?.facebook_username);
      console.log('🔍 [handleCompleteFacebookLogin] Computed isSuccess:', isSuccess);
      console.log('🔍 [handleCompleteFacebookLogin] Computed isLoggedIn:', isLoggedIn);

      console.log('🔍 [handleCompleteFacebookLogin] Checking success condition...');
      if (isSuccess && isLoggedIn) {
        console.log('✅ [handleCompleteFacebookLogin] SUCCESS condition met!');

        // Show success message with username if available
        const username = result.facebook_username ? ` (${result.facebook_username})` : '';
        const successMessage = `🎉 ${platformName} login completed successfully! Cookies saved${username}.`;
        console.log(`📢 [handleCompleteSocialLogin] Showing success message:`, successMessage);
        message.success(successMessage);

        // Force refresh profiles to update Facebook Status and Status
        console.log('🔄 [handleCompleteFacebookLogin] Starting profile refresh...');

        try {
          // Clear any cached data and force refresh
          console.log('🔄 [handleCompleteFacebookLogin] Clearing current profiles...');
          setProfiles([]); // Clear current profiles to force re-render

          console.log('🔄 [handleCompleteFacebookLogin] Calling loadProfiles()...');
          await refreshProfilesOnly();
          console.log('✅ [handleCompleteFacebookLogin] loadProfiles() completed successfully');

          // Additional feedback
          setTimeout(() => {
            console.log('✅ [handleCompleteFacebookLogin] Profile status updated successfully');
            message.info('Profile status refreshed');
          }, 1000);
        } catch (loadError) {
          console.error('❌ [handleCompleteFacebookLogin] Error during loadProfiles():', loadError);
          console.error('❌ [handleCompleteFacebookLogin] LoadError stack:', loadError.stack);
          message.error('Failed to refresh profile status');
        }
      } else {
        console.log('❌ [handleCompleteFacebookLogin] SUCCESS condition NOT met');
        console.log('❌ [handleCompleteFacebookLogin] isSuccess:', isSuccess, 'isLoggedIn:', isLoggedIn);
        console.log('🔍 [handleCompleteFacebookLogin] Showing warning message');
        const warningMessage = result?.message || 'Login not completed. Please complete the login process.';
        console.log('📢 [handleCompleteFacebookLogin] Warning message:', warningMessage);
        message.warning(warningMessage);
      }
    } catch (error) {
      console.error('❌ [handleCompleteFacebookLogin] Complete Facebook Login Error:', error);
      console.error('❌ [handleCompleteFacebookLogin] Error message:', error.message);
      console.error('❌ [handleCompleteFacebookLogin] Error stack:', error.stack);
      console.error('❌ [handleCompleteFacebookLogin] Error response:', error.response);
      message.error(`Failed to complete ${platformName} login: ` + error.message);
    } finally {
      console.log('🏁 [handleCompleteFacebookLogin] Finally block - setting loading false');
      setLoading(false);
    }
  };

  const handleCloseBrowser = async (profile) => {
    try {
      setLoading(true);
      const result = await apiService.closeBrowser(profile.id);
      if (result.success) {
        message.success('Browser closed successfully');
        refreshProfilesOnly(); // Refresh profiles without triggering auto-check
      } else {
        message.error(result.message || 'Failed to close browser');
      }
    } catch (error) {
      message.error('Failed to close browser: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleLoginSubmit = async (values) => {
    try {
      const result = await apiService.loginFacebook(selectedProfile.id, values);
      
      if (result.success) {
        if (result.manual_login_required) {
          message.info('Facebook login page opened. Please login manually in the browser.');
        } else {
          message.success('Facebook login successful');
        }
      } else {
        message.error(`Facebook login failed: ${result.message}`);
      }
      
      setLoginModalVisible(false);
      refreshProfilesOnly();
    } catch (error) {
      message.error('Failed to login Facebook: ' + error.message);
    }
  };

  const getStatusTag = (status) => {
    const statusConfig = {
      created: { color: 'default', text: 'Created' },
      active: { color: 'processing', text: 'Active' },
      logged_in: { color: 'success', text: 'Logged In' },
      error: { color: 'error', text: 'Error' },
      disabled: { color: 'default', text: 'Disabled' }
    };
    
    const config = statusConfig[status] || statusConfig.created;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
      render: (text, record) => (
        <Space>
          {record.type === 'instagram' ? (
            <InstagramOutlined style={{ color: '#E4405F' }} />
          ) : (
            <FacebookOutlined style={{ color: '#1877F2' }} />
          )}
          {text}
        </Space>
      )
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type) => (
        <Tag color={type === 'instagram' ? 'magenta' : 'blue'}>
          {type === 'instagram' ? 'Instagram' : 'Facebook'}
        </Tag>
      ),
      filters: [
        { text: 'Facebook', value: 'facebook' },
        { text: 'Instagram', value: 'instagram' }
      ],
      onFilter: (value, record) => record.type === value
    },
    {
      title: 'Proxy',
      key: 'proxy',
      render: (_, record) => {
        const proxy = record.proxy_config;
        if (!proxy || proxy.type === 'no_proxy') {
          return <Tag color="default">No Proxy</Tag>;
        }
        return (
          <Tooltip title={`${proxy.host}:${proxy.port}`}>
            <Tag color="blue">{proxy.type.toUpperCase()}</Tag>
          </Tooltip>
        );
      }
    },
    {
      title: 'Social Status',
      key: 'social_status',
      render: (_, record) => {
        const statusResult = loginStatusResults[record.id];
        if (statusResult) {
          const statusColor = statusResult.login_status === 'logged_in' ? 'success' :
                             statusResult.login_status === 'logged_out' ? 'default' : 'error';
          const statusText = statusResult.login_status === 'logged_in' ? 'Logged In' :
                            statusResult.login_status === 'logged_out' ? 'Logged Out' : 'Error';
          return (
            <Tooltip title={`Last checked: ${new Date(statusResult.last_checked).toLocaleString()}`}>
              <Tag color={statusColor}>{statusText}</Tag>
            </Tooltip>
          );
        }
        return record.facebook_logged_in ?
          <Tag color="success">Logged In ({record.facebook_username})</Tag> :
          <Tag color="default">Not Logged In</Tag>;
      }
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => getStatusTag(status),
      filters: [
        { text: 'Created', value: 'created' },
        { text: 'Active', value: 'active' },
        { text: 'Logged In', value: 'logged_in' },
        { text: 'Error', value: 'error' }
      ],
      onFilter: (value, record) => record.status === value
    },
    {
      title: 'Last Used',
      dataIndex: 'last_used',
      key: 'last_used',
      render: (date) => date ? new Date(date).toLocaleString() : 'Never',
      sorter: (a, b) => new Date(a.last_used || 0) - new Date(b.last_used || 0)
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space size="small" wrap>
          <Tooltip title="Launch Browser & Open Facebook - Open Camoufox browser and navigate to facebook.com/login">
            <Button
              icon={<RocketOutlined />}
              size="small"
              type="primary"
              onClick={() => handleLaunchBrowser(record)}
              disabled={checkingLoginStatus}
            />
          </Tooltip>

          <Tooltip title={`Open ${record.type === 'instagram' ? 'Instagram' : 'Facebook'} (Alternative) - Navigate to login page if browser is already running`}>
            <Button
              icon={record.type === 'instagram' ? <InstagramOutlined /> : <FacebookOutlined />}
              size="small"
              onClick={() => handleOpenSocial(record)}
              disabled={checkingLoginStatus}
            />
          </Tooltip>

          <Tooltip title={`Step 3: Complete ${record.type === 'instagram' ? 'Instagram' : 'Facebook'} Login - Save cookies after manual login`}>
            <Button
              icon={<CheckCircleOutlined />}
              size="small"
              type="primary"
              onClick={() => handleCompleteSocialLogin(record)}
              disabled={checkingLoginStatus}
            />
          </Tooltip>

          <Tooltip title="Close Browser">
            <Button
              icon={<CloseOutlined />}
              size="small"
              onClick={() => handleCloseBrowser(record)}
              disabled={checkingLoginStatus}
            />
          </Tooltip>

          <Tooltip title="Check Login Status">
            <Button
              icon={<SyncOutlined />}
              size="small"
              type="default"
              loading={checkingLoginStatus}
              disabled={checkingLoginStatus}
              onClick={() => {
                console.log('🖱️ [BUTTON] Individual check button clicked for profile:', record);
                console.log('🖱️ [BUTTON] Profile ID:', record.id, 'Platform:', record.type || 'facebook');
                handleCheckSingleProfile(record.id, record.type || 'facebook');
              }}
            />
          </Tooltip>

          <Tooltip title="Test Profile">
            <Button
              icon={<PlayCircleOutlined />}
              size="small"
              onClick={() => handleTestProfile(record)}
              disabled={checkingLoginStatus}
            />
          </Tooltip>

          <Tooltip title="Manage Browser Data">
            <Button
              icon={<SyncOutlined />}
              size="small"
              type="dashed"
              onClick={() => handleOpenProfileDataManager(record)}
              disabled={checkingLoginStatus}
            />
          </Tooltip>

          <Tooltip title="Edit Profile">
            <Button
              icon={<EditOutlined />}
              size="small"
              onClick={() => handleEditProfile(record)}
              disabled={checkingLoginStatus}
            />
          </Tooltip>

          <Tooltip title="Delete Profile">
            <Popconfirm
              title="Are you sure you want to delete this profile?"
              onConfirm={() => handleDeleteProfile(record.id)}
              okText="Yes"
              cancelText="No"
              disabled={checkingLoginStatus}
            >
              <Button
                icon={<DeleteOutlined />}
                size="small"
                danger
                disabled={checkingLoginStatus}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      )
    }
  ];

  // Debug logs for render
  console.log('🎨 [ProfileManager] Rendering component...');
  console.log('🎨 [ProfileManager] Current state:', {
    profiles: profiles.length,
    checkingLoginStatus,
    loginStatusResults: Object.keys(loginStatusResults).length
  });

  return (
    <Spin
      spinning={checkingLoginStatus}
      tip="Checking login status for all profiles... Please wait."
      size="large"
    >
      <div>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <h1>Profile Manager</h1>
          </Col>
          <Col>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={loadProfiles}
                loading={loading}
                disabled={checkingLoginStatus}
              >
                Refresh
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleCreateProfile}
                disabled={checkingLoginStatus}
              >
                Create Profile
              </Button>
            </Space>
          </Col>
        </Row>

      <Alert
        message="Facebook Login Process"
        description={
          <div>
            <p><strong>Follow these steps to set up social media login for your profiles:</strong></p>
            <ol>
              <li><strong>Launch Browser:</strong> Click the rocket icon to open Camoufox browser and automatically navigate to the login page (Facebook or Instagram based on profile type)</li>
              <li><strong>Manual Login:</strong> Complete the login process manually in the browser</li>
              <li><strong>Complete Login:</strong> Click the checkmark icon to save cookies</li>
              <li><strong>Future Use:</strong> Next time you launch the browser, you'll be automatically logged in</li>
            </ol>
            <p><strong>Note:</strong> Facebook profiles navigate to facebook.com/login, Instagram profiles navigate to instagram.com/accounts/login/</p>
          </div>
        }
        type="info"
        showIcon
        closable
        style={{ marginBottom: 16 }}
      />

      <Card>
        <Table
          columns={columns}
          dataSource={profiles}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} profiles`
          }}
        />
      </Card>

      {/* Create/Edit Profile Modal */}
      <Modal
        title={editingProfile ? 'Edit Profile' : 'Create Profile'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="Profile Name"
            rules={[{ required: true, message: 'Please enter profile name' }]}
          >
            <Input placeholder="Enter profile name" />
          </Form.Item>

          <Form.Item
            name="type"
            label="Profile Type"
            initialValue="facebook"
            rules={[{ required: true, message: 'Please select profile type' }]}
          >
            <Select placeholder="Select profile type">
              <Option value="facebook">Facebook</Option>
              <Option value="instagram">Instagram</Option>
            </Select>
          </Form.Item>

          <Card title="Proxy Settings" size="small" style={{ marginBottom: 16 }}>
            <Form.Item
              name="proxy_type"
              label="Proxy Type"
              initialValue="no_proxy"
            >
              <Select>
                <Option value="no_proxy">No Proxy (Local Network)</Option>
                <Option value="http">HTTP</Option>
                <Option value="https">HTTPS</Option>
                <Option value="socks5">SOCKS5</Option>
                <Option value="ssh">SSH</Option>
              </Select>
            </Form.Item>

            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                prevValues.proxy_type !== currentValues.proxy_type
              }
            >
              {({ getFieldValue }) => {
                const proxyType = getFieldValue('proxy_type');
                if (proxyType === 'no_proxy') return null;

                return (
                  <>
                    <Row gutter={16}>
                      <Col span={16}>
                        <Form.Item
                          name="proxy_host"
                          label="Host"
                          rules={[{ required: true, message: 'Please enter proxy host' }]}
                        >
                          <Input placeholder="proxy.example.com" />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item
                          name="proxy_port"
                          label="Port"
                          rules={[{ required: true, message: 'Please enter proxy port' }]}
                        >
                          <InputNumber 
                            placeholder="8080" 
                            min={1} 
                            max={65535} 
                            style={{ width: '100%' }}
                          />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="proxy_username"
                          label="Username"
                        >
                          <Input placeholder="Optional" />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="proxy_password"
                          label="Password"
                        >
                          <Input.Password placeholder="Optional" />
                        </Form.Item>
                      </Col>
                    </Row>
                  </>
                );
              }}
            </Form.Item>
          </Card>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingProfile ? 'Update' : 'Create'} Profile
              </Button>
              <Button onClick={() => setModalVisible(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Facebook Login Modal */}
      <Modal
        title="Facebook Login"
        open={loginModalVisible}
        onCancel={() => setLoginModalVisible(false)}
        footer={null}
      >
        <Form
          form={loginForm}
          layout="vertical"
          onFinish={handleLoginSubmit}
        >
          <Form.Item
            name="username"
            label="Facebook Username/Email"
            rules={[{ required: true, message: 'Please enter Facebook username' }]}
          >
            <Input placeholder="Enter Facebook username or email" />
          </Form.Item>

          <Form.Item
            name="password"
            label="Facebook Password"
            rules={[{ required: true, message: 'Please enter Facebook password' }]}
          >
            <Input.Password placeholder="Enter Facebook password" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                Login
              </Button>
              <Button onClick={() => setLoginModalVisible(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Profile Data Manager Modal */}
      {profileDataManagerVisible && selectedProfileForData && (
        <ProfileDataManager
          profileId={selectedProfileForData.id}
          onClose={() => {
            setProfileDataManagerVisible(false);
            setSelectedProfileForData(null);
          }}
        />
      )}
      </div>
    </Spin>
  );
};

export default ProfileManager;
