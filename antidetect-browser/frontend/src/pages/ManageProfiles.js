/**
 * ManageProfiles Page - Admin-only profile management interface
 */

import React, { useState, useEffect } from 'react';
import { 
  Tabs, 
  Card, 
  message, 
  Spin, 
  Alert,
  Typography,
  Space,
  Breadcrumb
} from 'antd';
import {
  TeamOutlined,
  UserOutlined,
  SettingOutlined,
  HomeOutlined,
  InfoCircleOutlined,
  ExperimentOutlined
} from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';
import { apiService } from '../services/api';
import {
  ProfileGroupList,
  ProfileGroupForm,
  ProfileList,
  ProfileForm,
  UserAssignmentList,
  UserAssignmentForm,
  ProfileSharingInfo
} from '../components/ProfileManagement';

const { Title } = Typography;

const ManageProfiles = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('groups');
  const [loading, setLoading] = useState(false);

  // Helper function to safely extract array from API response
  const safeExtractArray = (response, context = 'data') => {
    if (!response || !response.data) {
      console.warn(`⚠️ [ManageProfiles] No response data for ${context}`);
      return [];
    }

    const data = response.data;

    if (Array.isArray(data)) {
      return data;
    } else if (data.items && Array.isArray(data.items)) {
      return data.items;
    } else if (data.data && Array.isArray(data.data)) {
      return data.data;
    } else {
      console.warn(`⚠️ [ManageProfiles] Unexpected response format for ${context}:`, data);
      return [];
    }
  };

  // Data states
  const [profileGroups, setProfileGroups] = useState([]);
  const [profiles, setProfiles] = useState([]);
  const [userAssignments, setUserAssignments] = useState([]);
  const [users, setUsers] = useState([]);
  const [accounts, setAccounts] = useState([]);

  // Modal states
  const [groupFormVisible, setGroupFormVisible] = useState(false);
  const [profileFormVisible, setProfileFormVisible] = useState(false);
  const [assignmentFormVisible, setAssignmentFormVisible] = useState(false);

  // Editing states
  const [editingGroup, setEditingGroup] = useState(null);
  const [editingProfile, setEditingProfile] = useState(null);
  const [editingAssignment, setEditingAssignment] = useState(null);

  // Check admin permission
  if (user?.role !== 'admin') {
    return (
      <Card>
        <Alert
          message="Access Denied"
          description="You don't have permission to access this page. Only administrators can manage profiles."
          type="error"
          showIcon
        />
      </Card>
    );
  }

  // Fetch data functions
  const fetchProfileGroups = async () => {
    try {
      const response = await apiService.getProfileGroups();
      const groupsData = safeExtractArray(response, 'profile groups');
      setProfileGroups(groupsData);
    } catch (error) {
      console.error('Fetch profile groups error:', error);
      message.error('Failed to load profile groups');
      setProfileGroups([]);
    }
  };

  const fetchProfiles = async () => {
    try {
      console.log('📋 [ManageProfiles] Loading profiles from NestJS backend...');
      const response = await apiService.getProfileItems();

      // Enhanced response validation
      console.log('📊 [ManageProfiles] Raw response:', response);
      console.log('📊 [ManageProfiles] Response data type:', typeof response.data);
      console.log('📊 [ManageProfiles] Response data:', response.data);

      const profilesData = safeExtractArray(response, 'profiles');

      console.log('✅ [ManageProfiles] Loaded profiles:', profilesData.length);
      console.log('📊 [ManageProfiles] Profile IDs:', profilesData.map(p => ({ id: p.id, name: p.name })));
      setProfiles(profilesData);
    } catch (error) {
      console.error('❌ [ManageProfiles] Fetch profiles error:', error);
      message.error('Failed to load profiles');
      // Set empty array to prevent map errors
      setProfiles([]);
    }
  };

  const handleDeleteProfile = async (profileId) => {
    try {
      console.log('🗑️ Deleting profile from NestJS backend:', profileId);
      const response = await apiService.deleteProfileItem(profileId);
      console.log('✅ Delete response:', response);
      return response;
    } catch (error) {
      console.error('❌ Delete profile error:', error);
      throw error;
    }
  };

  const fetchUserAssignments = async () => {
    try {
      const response = await apiService.getUserProfileGroupAccess();
      const assignmentsData = safeExtractArray(response, 'user assignments');
      setUserAssignments(assignmentsData);
    } catch (error) {
      console.error('Fetch user assignments error:', error);
      message.error('Failed to load user assignments');
      setUserAssignments([]);
    }
  };

  const fetchUsers = async () => {
    try {
      const response = await apiService.getUsers();
      const usersData = safeExtractArray(response, 'users');
      setUsers(usersData);
    } catch (error) {
      console.error('Fetch users error:', error);
      message.error('Failed to load users');
      setUsers([]);
    }
  };

  const fetchAccounts = async () => {
    try {
      const response = await apiService.getAccounts();
      const accountsData = safeExtractArray(response, 'accounts');
      setAccounts(accountsData);
    } catch (error) {
      console.error('Fetch accounts error:', error);
      message.error('Failed to load accounts');
      setAccounts([]);
    }
  };

  // Load all data
  const loadAllData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchProfileGroups(),
        fetchProfiles(),
        fetchUserAssignments(),
        fetchUsers(),
        fetchAccounts(),
      ]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAllData();
  }, []);

  // Event handlers
  const handleCreateGroup = () => {
    setEditingGroup(null);
    setGroupFormVisible(true);
  };

  const handleEditGroup = (group) => {
    setEditingGroup(group);
    setGroupFormVisible(true);
  };

  const handleGroupFormSuccess = () => {
    setGroupFormVisible(false);
    setEditingGroup(null);
    fetchProfileGroups();
  };

  const handleCreateProfile = () => {
    setEditingProfile(null);
    setProfileFormVisible(true);
  };

  const handleEditProfile = (profile) => {
    setEditingProfile(profile);
    setProfileFormVisible(true);
  };

  const handleProfileFormSuccess = () => {
    setProfileFormVisible(false);
    setEditingProfile(null);
    fetchProfiles();
  };

  const handleCreateAssignment = () => {
    setEditingAssignment(null);
    setAssignmentFormVisible(true);
  };

  const handleEditAssignment = (assignment) => {
    setEditingAssignment(assignment);
    setAssignmentFormVisible(true);
  };

  const handleAssignmentFormSuccess = () => {
    setAssignmentFormVisible(false);
    setEditingAssignment(null);
    fetchUserAssignments();
  };

  const tabItems = [
    {
      key: 'groups',
      label: (
        <Space>
          <TeamOutlined />
          Profile Groups
        </Space>
      ),
      children: (
        <ProfileGroupList
          profileGroups={profileGroups}
          loading={loading}
          onEdit={handleEditGroup}
          onCreate={handleCreateGroup}
          onRefresh={fetchProfileGroups}
        />
      ),
    },
    {
      key: 'profiles',
      label: (
        <Space>
          <UserOutlined />
          Profiles
        </Space>
      ),
      children: (
        <ProfileList
          profiles={profiles}
          loading={loading}
          onEdit={handleEditProfile}
          onCreate={handleCreateProfile}
          onRefresh={fetchProfiles}
          onDelete={handleDeleteProfile}
        />
      ),
    },
    {
      key: 'assignments',
      label: (
        <Space>
          <SettingOutlined />
          User Assignments
        </Space>
      ),
      children: (
        <UserAssignmentList
          assignments={userAssignments}
          loading={loading}
          onEdit={handleEditAssignment}
          onCreate={handleCreateAssignment}
          onRefresh={fetchUserAssignments}
        />
      ),
    },
    {
      key: 'info',
      label: (
        <Space>
          <InfoCircleOutlined />
          Profile Sharing Info
        </Space>
      ),
      children: <ProfileSharingInfo />,
    },
    {
      key: 'testing',
      label: (
        <Space>
          <ExperimentOutlined />
          Testing & Validation
        </Space>
      ),
      children: (
        <Card>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Alert
              message="Testing & Validation Tools"
              description="Use these tools to test browser data capture functionality and validate system behavior."
              type="info"
              showIcon
            />
          </Space>
        </Card>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      {/* Breadcrumb */}
      <Breadcrumb style={{ marginBottom: '16px' }}>
        <Breadcrumb.Item>
          <HomeOutlined />
        </Breadcrumb.Item>
        <Breadcrumb.Item>Admin</Breadcrumb.Item>
        <Breadcrumb.Item>Manage Profiles</Breadcrumb.Item>
      </Breadcrumb>

      {/* Page Header */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>Manage Profiles</Title>
        <p style={{ color: '#666', marginBottom: 0 }}>
          Manage profile groups, profiles, and user access permissions
        </p>
      </div>

      {/* Main Content */}
      <Spin spinning={loading}>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size="large"
        />
      </Spin>

      {/* Modals */}
      <ProfileGroupForm
        visible={groupFormVisible}
        onCancel={() => setGroupFormVisible(false)}
        onSuccess={handleGroupFormSuccess}
        editingGroup={editingGroup}
      />

      <ProfileForm
        visible={profileFormVisible}
        onCancel={() => setProfileFormVisible(false)}
        onSuccess={handleProfileFormSuccess}
        editingProfile={editingProfile}
        profileGroups={profileGroups}
        accounts={accounts}
      />

      <UserAssignmentForm
        visible={assignmentFormVisible}
        onCancel={() => setAssignmentFormVisible(false)}
        onSuccess={handleAssignmentFormSuccess}
        editingAssignment={editingAssignment}
        users={users}
        profileGroups={profileGroups}
      />
    </div>
  );
};

export default ManageProfiles;
