/**
 * Scraping Page Component - Complete Implementation
 */

import React, { useState, useEffect } from 'react';
import {
  Card, Form, Input, Select, Button, Space, Table, Progress,
  message, Row, Col, InputNumber, Checkbox, Tag, Modal, Tooltip,
  Alert, Statistic
} from 'antd';
import {
  SearchOutlined, PlayCircleOutlined, StopOutlined, EyeOutlined,
  DownloadOutlined, ReloadOutlined, UserOutlined, CheckCircleOutlined,
  CloseCircleOutlined, ExportOutlined
} from '@ant-design/icons';
import { apiService } from '../services/api';
import { useAuth } from '../contexts/AuthContext';

const { Option } = Select;

const Scraping = () => {
  const { isAuthenticated } = useAuth();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [tasks, setTasks] = useState([]);
  const [profiles, setProfiles] = useState([]);
  const [activeTask, setActiveTask] = useState(null);
  const [taskProgress, setTaskProgress] = useState({});
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [selectedTaskResults, setSelectedTaskResults] = useState(null);
  const [exportHistory, setExportHistory] = useState([]);

  // Load initial data when authenticated
  useEffect(() => {
    console.log(`🔄 [useEffect] Authentication status changed: ${isAuthenticated}`);
    if (isAuthenticated) {
      console.log(`🔄 [useEffect] Loading initial data...`);
      loadInitialData();
    }
  }, [isAuthenticated]);

  // Separate polling effect for active task
  useEffect(() => {
    console.log(`🔄 [useEffect] activeTask changed:`, activeTask);

    if (activeTask) {
      console.log(`⏰ [useEffect] Starting polling for task: ${activeTask}`);
      const interval = setInterval(() => {
        console.log(`⏰ [useEffect] Polling interval triggered for task: ${activeTask}`);
        pollTaskStatus(activeTask);
      }, 2000); // Poll every 2 seconds

      return () => {
        console.log(`🛑 [useEffect] Clearing polling interval for task: ${activeTask}`);
        clearInterval(interval);
      };
    } else {
      console.log(`🛑 [useEffect] No active task, polling stopped`);
    }
  }, [activeTask]);

  const loadInitialData = async () => {
    try {
      setLoading(true);

      // Load profiles, tasks, and export history with better error handling
      const [profilesResponse, tasksData, exportsData] = await Promise.all([
        apiService.getProfiles().catch((error) => {
          console.error('Failed to load profiles:', error);
          message.error('Failed to load profiles: ' + error.message);
          return [];
        }),
        apiService.get('/api/scraping/').catch((error) => {
          console.error('Failed to load scraping tasks:', error);
          message.error('Failed to load scraping tasks: ' + error.message);
          return [];
        }),
        apiService.get('/api/scraping/exports/history').catch((error) => {
          console.warn('Failed to load export history:', error);
          return { exports: [] };
        })
      ]);

      // Extract profiles data from response (handle different response structures)
      let profilesData = Array.isArray(profilesResponse.data.data) ? profilesResponse.data.data : [];

      // Filter profiles that are logged into Facebook
      const loggedInProfiles = profilesData.filter(p => p.facebook_logged_in && p.type === 'facebook');
      console.log('✅ Scraping: Loaded profiles:', profilesData);
      console.log('✅ Scraping: Logged in profiles:', loggedInProfiles.length);

      setProfiles(loggedInProfiles);

      // Ensure tasksData is an array (handle triple-wrapped response)
      let processedTasksData = [];
      if (tasksData && typeof tasksData === 'object') {
        // Check for triple-wrapped: axios → standardized → data
        if (tasksData.data && tasksData.data.data && Array.isArray(tasksData.data.data)) {
          console.log('📊 Scraping: Triple-wrapped tasks response detected');
          processedTasksData = tasksData.data.data;
        }
        // Check for double-wrapped: standardized → data
        else if (tasksData.data && Array.isArray(tasksData.data)) {
          console.log('📊 Scraping: Double-wrapped tasks response detected');
          processedTasksData = tasksData.data;
        }
        // Check for direct array
        else if (Array.isArray(tasksData)) {
          console.log('📊 Scraping: Direct array tasks response detected');
          processedTasksData = tasksData;
        }
        // Check for standardized response structure
        else if (tasksData.success !== undefined && tasksData.data !== undefined) {
          if (tasksData.success && Array.isArray(tasksData.data)) {
            console.log('📊 Scraping: Standardized tasks response detected');
            processedTasksData = tasksData.data;
          } else {
            console.warn('❌ Scraping: Tasks API call failed or data is not an array:', tasksData);
            processedTasksData = [];
          }
        }
        else {
          console.warn('❌ Scraping: tasksData structure not recognized:', tasksData);
          processedTasksData = [];
        }
      } else {
        console.warn('❌ Scraping: Invalid tasksData format:', tasksData);
        processedTasksData = [];
      }

      setTasks(processedTasksData);

      // Check for running tasks and set activeTask
      const runningTask = processedTasksData.find(task =>
        ['running', 'pending'].includes(task.status)
      );
      if (runningTask && !activeTask) {
        console.log('📊 Found running task, setting as active:', runningTask.task_id);
        setActiveTask(runningTask.task_id);
      }

      // Initialize taskProgress for all tasks to ensure proper display
      const initialProgress = {};
      processedTasksData.forEach(task => {
        initialProgress[task.task_id] = {
          progress: task.progress || 0,
          status: task.status,
          total_found: task.total_found || 0,
          total_scraped: task.total_scraped || 0,
          current_step: task.status === 'completed' ? 'Completed' : 'Processing...'
        };
      });
      setTaskProgress(prev => ({ ...prev, ...initialProgress }));

      // Ensure exportsData is properly structured (handle triple-wrapped response)
      let processedExportsData = [];
      if (exportsData && typeof exportsData === 'object') {
        // Check for triple-wrapped: axios → response handler → original response with exports key
        if (exportsData.data && exportsData.data.data && exportsData.data.data.exports && Array.isArray(exportsData.data.data.exports)) {
          console.log('📊 Scraping: Triple-wrapped exports (axios → handler → exports) response detected');
          processedExportsData = exportsData.data.data.exports;
        }
        // Check for triple-wrapped: axios → standardized → data
        else if (exportsData.data && exportsData.data.data && Array.isArray(exportsData.data.data)) {
          console.log('📊 Scraping: Triple-wrapped exports response detected');
          processedExportsData = exportsData.data.data;
        }
        // Check for triple-wrapped with exports wrapper: axios → standardized → exports
        else if (exportsData.data && exportsData.data.exports && Array.isArray(exportsData.data.exports)) {
          console.log('📊 Scraping: Triple-wrapped exports (with exports key) response detected');
          processedExportsData = exportsData.data.exports;
        }
        // Check for double-wrapped with exports wrapper
        else if (exportsData.exports && Array.isArray(exportsData.exports)) {
          console.log('📊 Scraping: Exports wrapper response detected');
          processedExportsData = exportsData.exports;
        }
        // Check for double-wrapped: standardized → data
        else if (exportsData.data && Array.isArray(exportsData.data)) {
          console.log('📊 Scraping: Double-wrapped exports response detected');
          processedExportsData = exportsData.data;
        }
        // Check for direct array
        else if (Array.isArray(exportsData)) {
          console.log('📊 Scraping: Direct array exports response detected');
          processedExportsData = exportsData;
        }
        // Check for standardized response structure
        else if (exportsData.success !== undefined && exportsData.data !== undefined) {
          if (exportsData.success && Array.isArray(exportsData.data)) {
            console.log('📊 Scraping: Standardized exports response detected');
            processedExportsData = exportsData.data;
          } else {
            console.warn('❌ Scraping: Exports API call failed or data is not an array:', exportsData);
            processedExportsData = [];
          }
        }
        else {
          console.warn('❌ Scraping: exportsData structure not recognized:', exportsData);
          console.warn('📊 Scraping: exportsData keys:', Object.keys(exportsData));
          if (exportsData.data) {
            console.warn('📊 Scraping: exportsData.data keys:', Object.keys(exportsData.data));
            if (exportsData.data.data) {
              console.warn('📊 Scraping: exportsData.data.data keys:', Object.keys(exportsData.data.data));
            }
          }
          processedExportsData = [];
        }
      } else {
        console.warn('❌ Scraping: Invalid exportsData format:', exportsData);
        processedExportsData = [];
      }

      setExportHistory(processedExportsData);

      // Show warning if no logged in profiles
      if (loggedInProfiles.length === 0 && profilesData.length > 0) {
        message.warning('No profiles are logged into Facebook. Please login to Facebook first.');
      } else if (profilesData.length === 0) {
        message.info('No profiles found. Please create a profile first.');
      }

    } catch (error) {
      console.error('Failed to load initial data:', error);
      message.error('Failed to load data: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleStartScraping = async (values) => {
    try {
      setLoading(true);

      const config = {
        target_url: values.target_url,
        scraping_types: values.scraping_types || ['all'],
        max_results: values.max_results || 1000,
        profile_id: values.profile_id
      };

      const result = await apiService.post('/api/scraping/start', { config });

      setActiveTask(result.task_id);
      message.success('Scraping task started successfully');

      // Reset form and reload tasks
      form.resetFields();
      loadInitialData();

    } catch (error) {
      message.error('Failed to start scraping: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const pollTaskStatus = async (taskId) => {
    try {
      console.log(`🔄 [Polling] Checking status for task: ${taskId}`);
      const response = await apiService.get(`/api/scraping/status/${taskId}`);
      console.log(`📊 [Polling] Raw response for task ${taskId}:`, response);

      // Extract status from response (handle different response structures)
      let status = response;
      console.log(`📊 [Polling] Initial status:`, status);
      console.log(`📊 [Polling] Status keys:`, Object.keys(status || {}));

      // Check for triple-wrapped: axios → standardized → actual data
      if (response.data && response.data.data) {
        console.log(`📊 [Polling] Found response.data.data, using it as status`);
        status = response.data.data;
      }
      // Check for double-wrapped: axios → actual data
      else if (response.data) {
        console.log(`📊 [Polling] Found response.data, using it as status`);
        status = response.data;
      }

      console.log(`📊 [Polling] Final status object:`, status);
      console.log(`📊 [Polling] Final status keys:`, Object.keys(status || {}));
      console.log(`📊 [Polling] Task status field:`, status.status);
      console.log(`📊 [Polling] Task progress field:`, status.progress);

      // Update progress - ensure completed tasks show 100%
      const updatedStatus = { ...status };
      if (status.status === 'completed') {
        console.log(`✅ [Polling] Task ${taskId} completed, setting progress to 100%`);
        updatedStatus.progress = 100;
      }

      console.log(`📈 [Polling] Updating taskProgress for ${taskId}:`, updatedStatus);
      setTaskProgress(prev => {
        const newProgress = { ...prev, [taskId]: updatedStatus };
        console.log(`📈 [Polling] New taskProgress state:`, newProgress);
        return newProgress;
      });

      // Stop polling if task is completed - check status field directly
      if (['completed', 'failed', 'cancelled'].includes(status.status)) {
        console.log(`🛑 [Polling] Task ${taskId} finished with status: ${status.status}, stopping polling`);
        setActiveTask(prevActiveTask => {
          console.log(`🛑 [Polling] Clearing activeTask (was: ${prevActiveTask})`);
          return null;
        });

        // Update tasks list to reflect completion
        console.log(`📝 [Polling] Updating tasks list for completed task ${taskId}`);
        setTasks(prevTasks => {
          const updatedTasks = prevTasks.map(task =>
            task.task_id === taskId
              ? {
                  ...task,
                  status: status.status,
                  progress: status.status === 'completed' ? 100 : (status.progress || task.progress),
                  total_found: status.total_found || task.total_found,
                  total_scraped: status.total_scraped || task.total_scraped
                }
              : task
          );
          console.log(`📝 [Polling] Updated tasks list:`, updatedTasks);
          return updatedTasks;
        });

        // Also reload data to get latest info
        console.log(`🔄 [Polling] Reloading initial data after task completion`);
        loadInitialData();
      } else {
        console.log(`⏳ [Polling] Task ${taskId} still ${status.status}, continuing polling`);
      }

    } catch (error) {
      console.error(`❌ [Polling] Failed to poll task status for ${taskId}:`, error);
    }
  };

  const handleStopTask = async (taskId) => {
    try {
      await apiService.post(`/api/scraping/stop/${taskId}`);
      message.success('Task stopped successfully');
      setActiveTask(null);
      loadInitialData();
    } catch (error) {
      message.error('Failed to stop task: ' + error.message);
    }
  };

  const handleViewResults = async (taskId) => {
    try {
      const results = await apiService.get(`/api/scraping/results/${taskId}`);
      setSelectedTaskResults(results);
      setPreviewModalVisible(true);
    } catch (error) {
      message.error('Failed to load results: ' + error.message);
    }
  };

  const handleExportResults = async (taskId) => {
    try {
      console.log(`📤 [Export] Starting export for task: ${taskId}`);
      setLoading(true);
      message.loading('Exporting to Excel...', 0);

      console.log(`📤 [Export] Calling API: /api/scraping/export-excel/${taskId}`);
      const response = await apiService.post(`/api/scraping/export-excel/${taskId}`);

      message.destroy(); // Clear loading message

      console.log('📊 [Export] Full response object:', response);
      console.log('📊 [Export] Response type:', typeof response);
      console.log('📊 [Export] Response keys:', Object.keys(response || {}));

      // Handle different response structures
      let result = response;
      console.log('📊 [Export] Initial result:', result);

      if (response.data) {
        console.log('📊 [Export] Found response.data, checking structure...');

        // Check if response.data.data contains the actual export data
        if (response.data.data && typeof response.data.data === 'object') {
          console.log('📊 [Export] Found response.data.data (double-wrapped), using it as result');
          result = response.data.data;
        } else {
          console.log('📊 [Export] Using response.data as result');
          result = response.data;
        }
        console.log('📊 [Export] Updated result:', result);
      }

      // Check if export was successful
      const isSuccess = result.message || result.filename || result.download_url;
      console.log('📊 [Export] Success check:', {
        message: result.message,
        filename: result.filename,
        download_url: result.download_url,
        file_size: result.file_size,
        unique_users: result.unique_users,
        isSuccess
      });

      if (isSuccess) {
        console.log('✅ [Export] Export appears successful, processing download...');
        const exportMessage = result.message || 'Excel export completed successfully';
        const filename = result.filename || 'export.xlsx';
        const uniqueUsers = result.unique_users || 0;
        let downloadUrl = result.download_url;

        // Ensure download URL is properly formatted and handle authentication
        if (downloadUrl && !downloadUrl.startsWith('http')) {
          // If it's a relative URL, make it absolute
          if (downloadUrl.startsWith('/')) {
            downloadUrl = `${window.location.origin}${downloadUrl}`;
          } else {
            downloadUrl = `${window.location.origin}/api/scraping/download/${downloadUrl}`;
          }
        }

        // Create authenticated download function
        const authenticatedDownload = async (url) => {
          try {
            console.log('🔗 Starting authenticated download:', url);

            // Extract filename from URL or use the provided filename
            const downloadFilename = filename || url.split('/').pop() || 'export.xlsx';
            console.log('📁 Download filename:', downloadFilename);

            // Use apiService to handle authentication - try different approaches
            let response;

            // Use direct axios call with proper authentication
            console.log('🔄 Making authenticated download request');

            // Get token from API client headers (more reliable than localStorage)
            const authHeader = apiService.client.defaults.headers.common['Authorization'];
            const token = authHeader ? authHeader.replace('Bearer ', '') : localStorage.getItem('token');
            console.log('🔑 Token from API client:', !!authHeader);
            console.log('🔑 Token from localStorage:', !!localStorage.getItem('token'));
            console.log('🔑 Final token available:', !!token);

            // Ensure URL is properly formatted
            let downloadUrl = url;
            if (url.startsWith(window.location.origin)) {
              downloadUrl = url.replace(window.location.origin, '');
            }
            if (!downloadUrl.startsWith('/')) {
              downloadUrl = '/' + downloadUrl;
            }

            console.log('🔗 Final download URL:', downloadUrl);

            // Use axios with proper blob handling
            console.log('🔄 Using axios for blob download');
            const axiosResponse = await apiService.client.get(downloadUrl, {
              responseType: 'arraybuffer', // Use arraybuffer instead of blob
              headers: {
                'Authorization': `Bearer ${token}`,
                'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
              }
            });

            console.log('✅ Axios request successful');
            console.log('📊 Axios response status:', axiosResponse.status);
            console.log('📊 Axios response data type:', typeof axiosResponse.data);
            console.log('📊 Axios response data instanceof ArrayBuffer:', axiosResponse.data instanceof ArrayBuffer);
            console.log('📊 Axios response data constructor:', axiosResponse.data.constructor.name);

            // Check if data has byteLength property
            if (axiosResponse.data.byteLength !== undefined) {
              console.log('📊 Axios response data size:', axiosResponse.data.byteLength, 'bytes');
            } else {
              console.log('📊 Axios response data size: unknown (no byteLength property)');
              console.log('📊 Axios response data keys:', Object.keys(axiosResponse.data || {}));
            }

            // Extract actual ArrayBuffer from response
            let arrayBufferData = axiosResponse.data;

            // Check if response is wrapped by response handler
            if (axiosResponse.data.data && axiosResponse.data.success) {
              console.log('📦 Extracting ArrayBuffer from wrapped response');
              arrayBufferData = axiosResponse.data.data;
            }

            console.log('📦 Final ArrayBuffer data type:', typeof arrayBufferData);
            console.log('📦 Final ArrayBuffer instanceof ArrayBuffer:', arrayBufferData instanceof ArrayBuffer);

            // Create blob from arraybuffer
            const blob = new Blob([arrayBufferData], {
              type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            });
            console.log('📦 Created blob from arraybuffer:', blob.size, 'bytes');
            console.log('📦 Blob type:', blob.type);

            console.log('📦 Blob size:', blob.size, 'bytes');
            console.log('📦 Blob type:', blob.type);

            // Validate blob size
            if (blob.size < 100) {
              console.error('❌ Blob size too small, likely an error response');
              throw new Error(`File size too small (${blob.size} bytes). This may indicate an error response.`);
            }

            const blobUrl = window.URL.createObjectURL(blob);
            console.log('🔗 Blob URL created:', blobUrl);

            // Create temporary link and click it
            const link = document.createElement('a');
            link.href = blobUrl;
            link.download = downloadFilename;
            link.style.display = 'none';

            document.body.appendChild(link);
            console.log('🖱️ Triggering download click');
            link.click();
            document.body.removeChild(link);

            // Clean up blob URL after a delay
            setTimeout(() => {
              window.URL.revokeObjectURL(blobUrl);
              console.log('🧹 Blob URL cleaned up');
            }, 1000);

            console.log('✅ File download initiated successfully');
            message.success(`File "${downloadFilename}" download started`);

          } catch (downloadError) {
            console.error('❌ Download failed:', downloadError);
            console.error('❌ Error details:', {
              message: downloadError.message,
              status: downloadError.response?.status,
              statusText: downloadError.response?.statusText,
              data: downloadError.response?.data
            });

            const errorMessage = downloadError.response?.data?.message ||
                               downloadError.response?.statusText ||
                               downloadError.message ||
                               'Unknown download error';
            message.error('Download failed: ' + errorMessage);
          }
        };

        console.log('📊 Download URL:', downloadUrl);

        message.success(
          <div>
            <div>{exportMessage}</div>
            <div>File: {filename}</div>
            {uniqueUsers > 0 && <div>Unique users: {uniqueUsers}</div>}
            {downloadUrl && (
              <Button
                type="link"
                size="small"
                onClick={() => authenticatedDownload(downloadUrl)}
              >
                Download Now
              </Button>
            )}
          </div>,
          10 // Show for 10 seconds
        );

        // Auto download if URL is available
        if (downloadUrl) {
          console.log('🔗 [Export] Auto-downloading:', downloadUrl);

          // Use blob download method
          setTimeout(() => {
            console.log('🔗 [Export] Triggering blob download after delay');
            authenticatedDownload(downloadUrl);
          }, 1000);
        } else {
          console.warn('⚠️ [Export] No download URL available for auto-download');
        }

        loadInitialData(); // Reload to update export history
      } else {
        console.error('❌ Export failed - no success indicators in response:', result);
        message.error('Export failed: No download URL received');
      }
    } catch (error) {
      message.destroy();
      console.error('❌ Export error:', error);
      message.error('Failed to export results: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  const getStatusTag = (status) => {
    const statusConfig = {
      pending: { color: 'default', text: 'Pending' },
      running: { color: 'processing', text: 'Running' },
      completed: { color: 'success', text: 'Completed' },
      failed: { color: 'error', text: 'Failed' },
      cancelled: { color: 'default', text: 'Cancelled' }
    };

    const config = statusConfig[status] || statusConfig.pending;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const taskColumns = [
    {
      title: 'Target URL',
      dataIndex: 'target_url',
      key: 'target_url',
      render: (url) => (
        <Tooltip title={url}>
          <a href={url} target="_blank" rel="noopener noreferrer">
            {url.length > 50 ? url.substring(0, 50) + '...' : url}
          </a>
        </Tooltip>
      )
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => getStatusTag(status)
    },
    {
      title: 'Progress',
      key: 'progress',
      render: (_, record) => {
        const progress = taskProgress[record.task_id];
        if (progress) {
          return (
            <div>
              <Progress
                percent={progress.progress}
                size="small"
                status={progress.status === 'failed' ? 'exception' : 'active'}
              />
              <div style={{ fontSize: '12px', color: '#666' }}>
                {progress.current_step}
              </div>
            </div>
          );
        }
        return <Progress percent={record.progress || 0} size="small" />;
      }
    },
    {
      title: 'Found/Scraped',
      key: 'results',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <span>Found: {record.total_found || 0}</span>
          <span>Scraped: {record.total_scraped || 0}</span>
        </Space>
      )
    },
    {
      title: 'Created',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => new Date(date).toLocaleString()
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space size="small">
          {record.status === 'running' && (
            <Tooltip title="Stop Task">
              <Button
                icon={<StopOutlined />}
                size="small"
                danger
                onClick={() => handleStopTask(record.task_id)}
              />
            </Tooltip>
          )}

          <Tooltip title="View Results">
            <Button
              icon={<EyeOutlined />}
              size="small"
              onClick={() => handleViewResults(record.task_id)}
              disabled={!record.total_scraped}
            />
          </Tooltip>

          {record.status === 'completed' && record.total_scraped > 0 && (
            <Tooltip title="Export to Excel">
              <Button
                icon={<ExportOutlined />}
                size="small"
                type="primary"
                onClick={() => handleExportResults(record.task_id)}
              />
            </Tooltip>
          )}
        </Space>
      )
    }
  ];

  return (
    <div>
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <h1>Facebook Scraping</h1>
        </Col>
        <Col>
          <Button
            icon={<ReloadOutlined />}
            onClick={loadInitialData}
            loading={loading}
          >
            Refresh
          </Button>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* Create New Task */}
        <Col xs={24} lg={12}>
          <Card title="Create Scraping Task">
            <Form
              form={form}
              layout="vertical"
              onFinish={handleStartScraping}
            >
              <Form.Item
                name="target_url"
                label="Facebook Post URL"
                rules={[
                  { required: true, message: 'Please enter Facebook post URL' },
                  { type: 'url', message: 'Please enter a valid URL' }
                ]}
              >
                <Input placeholder="https://www.facebook.com/..." />
              </Form.Item>

              <Form.Item
                name="profile_id"
                label="Profile to Use"
                rules={[{ required: true, message: 'Please select a profile' }]}
              >
                <Select
                  placeholder={profiles.length === 0 ? "No logged-in profiles available" : "Select a logged-in profile"}
                  disabled={profiles.length === 0}
                  notFoundContent={profiles.length === 0 ? "No profiles logged into Facebook" : "No data"}
                >
                  {profiles.map(profile => (
                    <Option key={profile.id} value={profile.id}>
                      {profile.name} {profile.facebook_username ? `(${profile.facebook_username})` : '(No username)'}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              {profiles.length === 0 && (
                <Alert
                  message="No Facebook Profiles Available"
                  description="You need to create a profile and login to Facebook first before you can start scraping."
                  type="warning"
                  showIcon
                  style={{ marginBottom: 16 }}
                />
              )}

              <Form.Item
                name="scraping_types"
                label="What to Scrape"
                initialValue={['all']}
              >
                <Checkbox.Group>
                  <Row>
                    <Col span={24}>
                      <Checkbox value="all">All (Comments + Likes + Shares)</Checkbox>
                    </Col>
                    <Col span={8}>
                      <Checkbox value="comments">Comments</Checkbox>
                    </Col>
                    <Col span={8}>
                      <Checkbox value="likes">Likes</Checkbox>
                    </Col>
                    <Col span={8}>
                      <Checkbox value="shares">Shares</Checkbox>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>

              <Form.Item
                name="max_results"
                label="Maximum Results"
                initialValue={1000}
              >
                <InputNumber
                  min={1}
                  max={10000}
                  style={{ width: '100%' }}
                  placeholder="Maximum number of users to scrape"
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SearchOutlined />}
                  loading={loading}
                  disabled={profiles.length === 0}
                  block
                >
                  {profiles.length === 0 ? 'No Profiles Available' : 'Start Scraping'}
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* Active Task Status */}
        <Col xs={24} lg={12}>
          {activeTask && taskProgress[activeTask] && (
            <Card title="Active Task Status">
              <Space direction="vertical" style={{ width: '100%' }}>
                <Progress
                  percent={taskProgress[activeTask].progress}
                  status={taskProgress[activeTask].status === 'failed' ? 'exception' : 'active'}
                />

                <div>
                  <strong>Status:</strong> {getStatusTag(taskProgress[activeTask].status)}
                </div>

                <div>
                  <strong>Current Step:</strong> {taskProgress[activeTask].current_step}
                </div>

                <Row gutter={16}>
                  <Col span={12}>
                    <Statistic
                      title="Total Found"
                      value={taskProgress[activeTask].total_found}
                      prefix={<UserOutlined />}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="Total Scraped"
                      value={taskProgress[activeTask].total_scraped}
                      prefix={<CheckCircleOutlined />}
                      valueStyle={{ color: '#3f8600' }}
                    />
                  </Col>
                </Row>

                <Button
                  danger
                  icon={<StopOutlined />}
                  onClick={() => handleStopTask(activeTask)}
                  block
                >
                  Stop Task
                </Button>
              </Space>
            </Card>
          )}
        </Col>
      </Row>

      {/* Tasks History */}
      <Card title="Scraping Tasks" style={{ marginTop: 24 }}>
        <Table
          columns={taskColumns}
          dataSource={tasks}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} tasks`
          }}
        />
      </Card>

      {/* Results Modal */}
      <Modal
        title="Scraping Results"
        open={previewModalVisible}
        onCancel={() => setPreviewModalVisible(false)}
        width={1000}
        footer={[
          <Button key="close" onClick={() => setPreviewModalVisible(false)}>
            Close
          </Button>
        ]}
      >
        {selectedTaskResults && (
          <div>
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={6}>
                <Statistic title="Total Users" value={selectedTaskResults.total_users} />
              </Col>
              <Col span={6}>
                <Statistic title="Comments" value={selectedTaskResults.users_by_type?.comment || 0} />
              </Col>
              <Col span={6}>
                <Statistic title="Likes" value={selectedTaskResults.users_by_type?.like || 0} />
              </Col>
              <Col span={6}>
                <Statistic title="Shares" value={selectedTaskResults.users_by_type?.share || 0} />
              </Col>
            </Row>

            <Table
              size="small"
              columns={[
                {
                  title: 'UID',
                  dataIndex: 'facebook_uid',
                  key: 'facebook_uid',
                  width: 120
                },
                {
                  title: 'Name',
                  dataIndex: 'full_name',
                  key: 'full_name'
                },
                {
                  title: 'Type',
                  dataIndex: 'interaction_type',
                  key: 'interaction_type',
                  render: (type) => <Tag>{type}</Tag>
                },
                {
                  title: 'Content',
                  dataIndex: 'interaction_content',
                  key: 'interaction_content',
                  render: (content) => content && content.length > 50 ? content.substring(0, 50) + '...' : content || '-'
                },
                {
                  title: 'Scraped At',
                  dataIndex: 'scraped_at',
                  key: 'scraped_at',
                  render: (date) => new Date(date).toLocaleString()
                }
              ]}
              dataSource={selectedTaskResults.users}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Scraping;
