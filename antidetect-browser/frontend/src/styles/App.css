/* Facebook Automation Desktop - Main App Styles */

/* Global Styles */
#root {
  overflow: auto;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubunt<PERSON>', '<PERSON><PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #f5f5f5;
}

/* App Loading Screen */
.app-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-logo {
  font-size: 80px;
  margin-bottom: 20px;
  animation: pulse 2s infinite;
}

.loading-content h2 {
  color: white;
  margin-bottom: 30px;
  font-size: 28px;
  font-weight: 300;
}

.loading-content p {
  color: rgba(255, 255, 255, 0.8);
  margin-top: 20px;
  font-size: 16px;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* Sidebar Logo */
.app-logo {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  margin: 16px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  font-size: 32px;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

.logo-icon-collapsed {
  font-size: 28px;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

.logo-text {
  color: white;
  line-height: 1.2;
}

.logo-title {
  font-size: 16px;
  font-weight: bold;
  margin: 0;
}

.logo-subtitle {
  font-size: 12px;
  opacity: 0.8;
  margin: 0;
}

/* Custom Menu Styles */
.custom-menu .ant-menu-item {
  margin: 4px 8px !important;
  border-radius: 8px !important;
  height: 48px !important;
  line-height: 48px !important;
  transition: all 0.3s ease !important;
}

.custom-menu .ant-menu-item:hover {
  background: rgba(255, 255, 255, 0.15) !important;
  transform: translateX(4px);
}

.custom-menu .ant-menu-item-selected {
  background: rgba(255, 255, 255, 0.2) !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
}

.custom-menu .ant-menu-item-selected::after {
  display: none !important;
}

.custom-menu .ant-menu-item .ant-menu-item-icon {
  font-size: 18px;
}

/* Header Styles */
.app-header {
  border-bottom: 1px solid #f0f0f0;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95) !important;
}

.trigger {
  color: #666;
  border-radius: 4px;
}

.trigger:hover {
  color: #1890ff;
  background: #f0f0f0;
}

/* Content Wrapper */
.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Card Styles */
.custom-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  border: none;
  overflow: hidden;
  transition: all 0.3s ease;
}

.custom-card:hover {
  box-shadow: 0 8px 30px rgba(0,0,0,0.12);
  transform: translateY(-2px);
}

.custom-card .ant-card-head {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: none;
}

.custom-card .ant-card-head-title {
  color: white;
  font-weight: 600;
}

/* Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.status-online {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-offline {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.status-connecting {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

/* Progress Bars */
.custom-progress .ant-progress-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Buttons */
.gradient-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  transition: all 0.3s ease;
}

.gradient-button:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* Tables */
.custom-table .ant-table-thead > tr > th {
  background: #fafafa;
  border-bottom: 2px solid #f0f0f0;
  font-weight: 600;
  color: #262626;
}

.custom-table .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

/* Forms */
.custom-form .ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.custom-form .ant-input,
.custom-form .ant-select-selector {
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s ease;
}

.custom-form .ant-input:focus,
.custom-form .ant-select-focused .ant-select-selector {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* Notifications */
.ant-notification {
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Responsive Design */
@media (max-width: 768px) {
  .content-wrapper {
    padding: 0 8px;
  }
  
  .custom-card {
    margin-bottom: 16px;
  }
  
  .app-header h1 {
    font-size: 16px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  body {
    background: #141414;
    color: #fff;
  }
  
  .custom-card {
    background: #1f1f1f;
    border: 1px solid #303030;
  }
  
  .custom-table .ant-table-thead > tr > th {
    background: #262626;
    color: #fff;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

.slide-up {
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

.bounce-in {
  animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
  0% { opacity: 0; transform: scale(0.3); }
  50% { opacity: 1; transform: scale(1.05); }
  70% { transform: scale(0.9); }
  100% { opacity: 1; transform: scale(1); }
}
