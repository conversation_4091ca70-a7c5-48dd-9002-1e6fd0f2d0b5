import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Table,
  Button,
  Modal,
  Form,
  Select,
  DatePicker,
  Alert,
  Tag,
  Space,
  Tooltip,
  Badge,
  Descriptions,
  Timeline
} from 'antd';
import {
  ShieldOutlined,
  LockOutlined,
  AuditOutlined,
  ExportOutlined,
  DeleteOutlined,
  EyeOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  UserOutlined
} from '@ant-design/icons';
import moment from 'moment';
import { apiService } from '../../services/api';

const { RangePicker } = DatePicker;
const { Option } = Select;

const SecurityDashboard = () => {
  const [loading, setLoading] = useState(false);
  const [securityStatus, setSecurityStatus] = useState({});
  const [auditLogs, setAuditLogs] = useState([]);
  const [dataSummary, setDataSummary] = useState({});
  const [auditModalVisible, setAuditModalVisible] = useState(false);
  const [exportModalVisible, setExportModalVisible] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [auditFilters, setAuditFilters] = useState({});

  useEffect(() => {
    loadSecurityData();
  }, []);

  const loadSecurityData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        loadSecurityStatus(),
        loadDataSummary(),
        loadRecentAuditLogs()
      ]);
    } catch (error) {
      console.error('Failed to load security data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadSecurityStatus = async () => {
    try {
      const response = await apiService.get('/profile-security/security/status');
      if (response.success) {
        setSecurityStatus(response.data);
      }
    } catch (error) {
      console.error('Failed to load security status:', error);
    }
  };

  const loadDataSummary = async () => {
    try {
      const response = await apiService.get('/profile-security/privacy/data-summary');
      if (response.success) {
        setDataSummary(response.data);
      }
    } catch (error) {
      console.error('Failed to load data summary:', error);
    }
  };

  const loadRecentAuditLogs = async () => {
    try {
      const response = await apiService.get('/profile-security/audit-logs', {
        params: { limit: 10 }
      });
      if (response.success) {
        setAuditLogs(response.data.logs || []);
      }
    } catch (error) {
      console.error('Failed to load audit logs:', error);
    }
  };

  const handleExportData = async (values) => {
    try {
      const response = await apiService.post('/profile-security/privacy/export-data', {
        include_shared_profiles: values.include_shared,
        include_audit_logs: values.include_logs,
        format: values.format
      });

      if (response.success) {
        message.success('Data export initiated. You will be notified when complete.');
        setExportModalVisible(false);
      } else {
        message.error('Failed to initiate data export');
      }
    } catch (error) {
      message.error('Failed to export data');
      console.error('Export error:', error);
    }
  };

  const handleDeleteData = async () => {
    try {
      const response = await apiService.post('/profile-security/privacy/delete-data', {
        delete_shared_profiles: false,
        confirm_deletion: true
      });

      if (response.success) {
        message.success('Data deletion initiated. This process may take some time.');
        setDeleteModalVisible(false);
      } else {
        message.error('Failed to initiate data deletion');
      }
    } catch (error) {
      message.error('Failed to delete data');
      console.error('Delete error:', error);
    }
  };

  const getSecurityScoreColor = (score) => {
    if (score >= 90) return '#52c41a';
    if (score >= 70) return '#faad14';
    return '#ff4d4f';
  };

  const getActionIcon = (action) => {
    const icons = {
      'launch': <PlayCircleOutlined />,
      'close': <StopOutlined />,
      'sync': <SyncOutlined />,
      'export': <ExportOutlined />,
      'share': <ShareAltOutlined />,
      'view': <EyeOutlined />,
      'modify': <EditOutlined />,
      'delete': <DeleteOutlined />
    };
    return icons[action] || <InfoCircleOutlined />;
  };

  const auditColumns = [
    {
      title: 'Time',
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (time) => moment(time).format('MM-DD HH:mm:ss'),
      width: 120
    },
    {
      title: 'User',
      dataIndex: 'user_id',
      key: 'user_id',
      render: (userId) => (
        <Space>
          <UserOutlined />
          User {userId}
        </Space>
      ),
      width: 100
    },
    {
      title: 'Action',
      dataIndex: 'action',
      key: 'action',
      render: (action) => (
        <Space>
          {getActionIcon(action)}
          <Tag color="blue">{action.toUpperCase()}</Tag>
        </Space>
      ),
      width: 120
    },
    {
      title: 'Profile',
      dataIndex: 'profile_id',
      key: 'profile_id',
      render: (profileId) => `Profile ${profileId}`,
      width: 100
    },
    {
      title: 'Status',
      dataIndex: 'success',
      key: 'success',
      render: (success) => (
        <Tag color={success ? 'success' : 'error'}>
          {success ? 'SUCCESS' : 'FAILED'}
        </Tag>
      ),
      width: 80
    },
    {
      title: 'IP Address',
      dataIndex: 'ip_address',
      key: 'ip_address',
      width: 120
    },
    {
      title: 'Details',
      key: 'details',
      render: (_, record) => (
        <Button
          type="link"
          icon={<EyeOutlined />}
          onClick={() => showAuditDetails(record)}
          size="small"
        >
          View
        </Button>
      ),
      width: 80
    }
  ];

  const showAuditDetails = (record) => {
    Modal.info({
      title: 'Audit Log Details',
      width: 600,
      content: (
        <Descriptions bordered column={1} size="small">
          <Descriptions.Item label="Timestamp">
            {moment(record.timestamp).format('YYYY-MM-DD HH:mm:ss')}
          </Descriptions.Item>
          <Descriptions.Item label="User ID">{record.user_id}</Descriptions.Item>
          <Descriptions.Item label="Profile ID">{record.profile_id}</Descriptions.Item>
          <Descriptions.Item label="Action">{record.action}</Descriptions.Item>
          <Descriptions.Item label="Success">{record.success ? 'Yes' : 'No'}</Descriptions.Item>
          <Descriptions.Item label="IP Address">{record.ip_address}</Descriptions.Item>
          <Descriptions.Item label="User Agent">{record.user_agent}</Descriptions.Item>
          {record.details && (
            <Descriptions.Item label="Details">
              <pre style={{ fontSize: '12px', maxHeight: '200px', overflow: 'auto' }}>
                {JSON.stringify(record.details, null, 2)}
              </pre>
            </Descriptions.Item>
          )}
        </Descriptions>
      )
    });
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* Security Overview */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Security Score"
              value={securityStatus.security_score || 0}
              suffix="/100"
              valueStyle={{ color: getSecurityScoreColor(securityStatus.security_score || 0) }}
              prefix={<ShieldOutlined />}
            />
            <Progress
              percent={securityStatus.security_score || 0}
              strokeColor={getSecurityScoreColor(securityStatus.security_score || 0)}
              size="small"
              style={{ marginTop: 8 }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Encrypted Profiles"
              value={securityStatus.encryption?.encrypted_profiles || 0}
              prefix={<LockOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
            <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
              {securityStatus.encryption?.algorithm || 'AES-256'}
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Audit Logs"
              value={securityStatus.audit_logging?.total_logs || 0}
              prefix={<AuditOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
            <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
              {securityStatus.audit_logging?.retention_days || 365} days retention
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Active Sessions"
              value={securityStatus.access_control?.active_sessions || 0}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
            <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
              Rate limiting: {securityStatus.access_control?.rate_limiting ? 'ON' : 'OFF'}
            </div>
          </Card>
        </Col>
      </Row>

      {/* Privacy Compliance */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={12}>
          <Card
            title="Privacy Compliance"
            extra={
              <Space>
                <Button
                  type="primary"
                  icon={<ExportOutlined />}
                  onClick={() => setExportModalVisible(true)}
                >
                  Export My Data
                </Button>
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => setDeleteModalVisible(true)}
                >
                  Delete My Data
                </Button>
              </Space>
            }
          >
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="My Profiles"
                  value={dataSummary.data_summary?.accessible_profiles || 0}
                  prefix={<UserOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="Audit Entries"
                  value={dataSummary.data_summary?.audit_log_entries || 0}
                  prefix={<AuditOutlined />}
                />
              </Col>
            </Row>
            <div style={{ marginTop: 16 }}>
              <Alert
                message="GDPR Compliance"
                description="You have the right to export, modify, or delete your personal data at any time."
                type="info"
                showIcon
                style={{ fontSize: '12px' }}
              />
            </div>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="Security Features">
            <Timeline size="small">
              <Timeline.Item
                dot={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                color="green"
              >
                <strong>Data Encryption</strong>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  All sensitive data encrypted with AES-256
                </div>
              </Timeline.Item>
              <Timeline.Item
                dot={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                color="green"
              >
                <strong>Audit Logging</strong>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  All profile operations are logged and monitored
                </div>
              </Timeline.Item>
              <Timeline.Item
                dot={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                color="green"
              >
                <strong>Access Control</strong>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  Role-based permissions and data isolation
                </div>
              </Timeline.Item>
              <Timeline.Item
                dot={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                color="green"
              >
                <strong>Privacy Compliance</strong>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  GDPR-ready data export and deletion
                </div>
              </Timeline.Item>
            </Timeline>
          </Card>
        </Col>
      </Row>

      {/* Recent Audit Logs */}
      <Card
        title="Recent Audit Logs"
        extra={
          <Button
            icon={<AuditOutlined />}
            onClick={() => setAuditModalVisible(true)}
          >
            View All Logs
          </Button>
        }
      >
        <Table
          columns={auditColumns}
          dataSource={auditLogs}
          loading={loading}
          rowKey="id"
          pagination={false}
          size="small"
        />
      </Card>

      {/* Export Data Modal */}
      <Modal
        title="Export My Data"
        open={exportModalVisible}
        onCancel={() => setExportModalVisible(false)}
        footer={null}
        width={500}
      >
        <Alert
          message="Data Export"
          description="Export all your personal data for backup or transfer purposes. This process may take a few minutes."
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
        
        <Form onFinish={handleExportData} layout="vertical">
          <Form.Item name="format" label="Export Format" initialValue="json">
            <Select>
              <Option value="json">JSON</Option>
              <Option value="csv">CSV</Option>
            </Select>
          </Form.Item>
          
          <Form.Item name="include_shared" valuePropName="checked" initialValue={true}>
            <Checkbox>Include shared profiles</Checkbox>
          </Form.Item>
          
          <Form.Item name="include_logs" valuePropName="checked" initialValue={false}>
            <Checkbox>Include audit logs</Checkbox>
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                Export Data
              </Button>
              <Button onClick={() => setExportModalVisible(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Delete Data Modal */}
      <Modal
        title="Delete My Data"
        open={deleteModalVisible}
        onCancel={() => setDeleteModalVisible(false)}
        footer={null}
        width={500}
      >
        <Alert
          message="Warning: Data Deletion"
          description="This action will permanently delete all your personal data. This cannot be undone."
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
        
        <div style={{ marginBottom: 16 }}>
          <p>This will delete:</p>
          <ul>
            <li>Your profile access records</li>
            <li>Your audit log entries</li>
            <li>Your sharing history</li>
            <li>Any personal preferences</li>
          </ul>
        </div>
        
        <Space>
          <Button
            danger
            type="primary"
            onClick={handleDeleteData}
            icon={<DeleteOutlined />}
          >
            Confirm Deletion
          </Button>
          <Button onClick={() => setDeleteModalVisible(false)}>
            Cancel
          </Button>
        </Space>
      </Modal>
    </div>
  );
};

export default SecurityDashboard;
