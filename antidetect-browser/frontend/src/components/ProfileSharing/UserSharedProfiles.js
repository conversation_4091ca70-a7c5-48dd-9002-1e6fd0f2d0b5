import React, { useState, useEffect } from 'react';
import {
  Card,
  List,
  Button,
  Tag,
  Space,
  Avatar,
  Tooltip,
  message,
  Modal,
  Descriptions,
  Progress,
  Alert,
  Row,
  Col,
  Statistic,
  Badge
} from 'antd';
import {
  UserOutlined,
  ShareAltOutlined,
  PlayCircleOutlined,
  SyncOutlined,
  ExportOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import moment from 'moment';
import { apiService } from '../../services/api';

const UserSharedProfiles = () => {
  const [loading, setLoading] = useState(false);
  const [profiles, setProfiles] = useState([]);
  const [accessSummary, setAccessSummary] = useState({});
  const [selectedProfile, setSelectedProfile] = useState(null);
  const [profileModalVisible, setProfileModalVisible] = useState(false);
  const [syncStatus, setSyncStatus] = useState({});

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        loadAccessibleProfiles(),
        loadAccessSummary()
      ]);
    } catch (error) {
      message.error('Failed to load profile data');
    } finally {
      setLoading(false);
    }
  };

  const loadAccessibleProfiles = async () => {
    try {
      const response = await apiService.get('/profiles/my-profiles');
      if (response && Array.isArray(response)) {
        setProfiles(response);
      }
    } catch (error) {
      console.error('Failed to load accessible profiles:', error);
    }
  };

  const loadAccessSummary = async () => {
    try {
      const response = await apiService.get('/profiles/access-summary');
      if (response.success) {
        setAccessSummary(response.data);
      }
    } catch (error) {
      console.error('Failed to load access summary:', error);
    }
  };

  const handleLaunchProfile = async (profileId) => {
    try {
      const response = await apiService.post(`/profiles/${profileId}/launch-with-access`);
      
      if (response.success) {
        message.success('Profile launched successfully');
        // You might want to update UI to show the profile is active
      } else {
        message.error(response.message || 'Failed to launch profile');
      }
    } catch (error) {
      message.error('Failed to launch profile');
      console.error('Launch error:', error);
    }
  };

  const handleSyncProfile = async (profileId) => {
    try {
      setSyncStatus(prev => ({ ...prev, [profileId]: 'syncing' }));
      
      const response = await apiService.post(`/profile-sharing/sync/${profileId}`, {
        sync_type: 'full',
        sync_direction: 'bidirectional'
      });
      
      if (response.success || response.sync_id) {
        message.success('Profile synchronized successfully');
        setSyncStatus(prev => ({ ...prev, [profileId]: 'completed' }));
        
        // Reload profile data to get updated sync info
        setTimeout(() => {
          loadAccessibleProfiles();
          setSyncStatus(prev => ({ ...prev, [profileId]: null }));
        }, 2000);
      } else {
        message.error('Failed to sync profile');
        setSyncStatus(prev => ({ ...prev, [profileId]: 'failed' }));
      }
    } catch (error) {
      message.error('Failed to sync profile');
      setSyncStatus(prev => ({ ...prev, [profileId]: 'failed' }));
      console.error('Sync error:', error);
    }
  };

  const handleExportProfile = async (profileId) => {
    try {
      const response = await apiService.post(`/profile-sharing/export/${profileId}`);
      
      if (response.success) {
        message.success('Profile data exported successfully');
        // You might want to trigger a download or show export details
      } else {
        message.error(response.message || 'Failed to export profile');
      }
    } catch (error) {
      message.error('Failed to export profile');
      console.error('Export error:', error);
    }
  };

  const showProfileDetails = (profile) => {
    setSelectedProfile(profile);
    setProfileModalVisible(true);
  };

  const getAccessTypeColor = (accessType) => {
    switch (accessType) {
      case 'package': return 'blue';
      case 'shared': return 'green';
      default: return 'default';
    }
  };

  const getAccessTypeIcon = (accessType) => {
    switch (accessType) {
      case 'package': return <UserOutlined />;
      case 'shared': return <ShareAltOutlined />;
      default: return <InfoCircleOutlined />;
    }
  };

  const getSyncStatusIcon = (profileId) => {
    const status = syncStatus[profileId];
    switch (status) {
      case 'syncing': return <SyncOutlined spin />;
      case 'completed': return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'failed': return <WarningOutlined style={{ color: '#ff4d4f' }} />;
      default: return <SyncOutlined />;
    }
  };

  const renderProfileActions = (profile) => {
    const permissions = profile.access_info?.permissions || {};
    const accessType = profile.access_info?.accessType;
    
    return (
      <Space>
        <Button
          type="primary"
          icon={<PlayCircleOutlined />}
          onClick={() => handleLaunchProfile(profile.profile.id)}
          size="small"
        >
          Launch
        </Button>
        
        {permissions.can_modify_profile && (
          <Button
            icon={getSyncStatusIcon(profile.profile.id)}
            onClick={() => handleSyncProfile(profile.profile.id)}
            loading={syncStatus[profile.profile.id] === 'syncing'}
            size="small"
          >
            Sync
          </Button>
        )}
        
        {permissions.can_export_data && (
          <Button
            icon={<ExportOutlined />}
            onClick={() => handleExportProfile(profile.profile.id)}
            size="small"
          >
            Export
          </Button>
        )}
        
        <Button
          icon={<InfoCircleOutlined />}
          onClick={() => showProfileDetails(profile)}
          size="small"
        >
          Details
        </Button>
      </Space>
    );
  };

  const renderExpirationWarning = (profile) => {
    const expiresAt = profile.expires_at;
    if (!expiresAt || profile.access_info?.accessType !== 'shared') {
      return null;
    }

    const expirationDate = moment(expiresAt);
    const now = moment();
    const daysUntilExpiration = expirationDate.diff(now, 'days');

    if (daysUntilExpiration <= 7 && daysUntilExpiration > 0) {
      return (
        <Alert
          message={`Access expires in ${daysUntilExpiration} days`}
          type="warning"
          showIcon
          size="small"
          style={{ marginTop: 8 }}
        />
      );
    } else if (daysUntilExpiration <= 0) {
      return (
        <Alert
          message="Access has expired"
          type="error"
          showIcon
          size="small"
          style={{ marginTop: 8 }}
        />
      );
    }

    return null;
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* Access Summary */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Accessible"
              value={accessSummary.total_accessible || 0}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Package Based"
              value={accessSummary.package_based || 0}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Shared"
              value={accessSummary.shared || 0}
              prefix={<ShareAltOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Expiring Soon"
              value={accessSummary.expiring_soon || 0}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Expiring Shares Alert */}
      {accessSummary.expiring_soon > 0 && (
        <Alert
          message="Profile Access Expiring Soon"
          description={`You have ${accessSummary.expiring_soon} profile(s) with access expiring within 7 days. Please contact your administrator if you need extended access.`}
          type="warning"
          showIcon
          closable
          style={{ marginBottom: '24px' }}
        />
      )}

      {/* Profiles List */}
      <Card
        title="My Accessible Profiles"
        extra={
          <Button
            icon={<SyncOutlined />}
            onClick={loadData}
            loading={loading}
          >
            Refresh
          </Button>
        }
      >
        <List
          loading={loading}
          dataSource={profiles}
          renderItem={(profile) => (
            <List.Item
              key={profile.profile.id}
              actions={[renderProfileActions(profile)]}
            >
              <List.Item.Meta
                avatar={
                  <Badge
                    status={profile.profile.facebook_logged_in ? 'success' : 'default'}
                    dot
                  >
                    <Avatar icon={<UserOutlined />} />
                  </Badge>
                }
                title={
                  <Space>
                    {profile.profile.name}
                    <Tag 
                      color={getAccessTypeColor(profile.access_info?.accessType)}
                      icon={getAccessTypeIcon(profile.access_info?.accessType)}
                    >
                      {profile.access_info?.accessType?.toUpperCase() || 'UNKNOWN'}
                    </Tag>
                    {profile.access_info?.permissions?.access_level && (
                      <Tag color="purple">
                        {profile.access_info.permissions.access_level.toUpperCase()}
                      </Tag>
                    )}
                  </Space>
                }
                description={
                  <div>
                    <div>
                      {profile.account_info?.website_url && (
                        <span>Website: {profile.account_info.website_url}</span>
                      )}
                      {profile.profile.facebook_logged_in && (
                        <Tag color="green" size="small" style={{ marginLeft: 8 }}>
                          Facebook Logged In
                        </Tag>
                      )}
                    </div>
                    
                    {/* Permissions */}
                    <div style={{ marginTop: 4 }}>
                      <Space size="small">
                        {profile.access_info?.permissions?.can_modify_profile && (
                          <Tag size="small" color="blue">Can Modify</Tag>
                        )}
                        {profile.access_info?.permissions?.can_view_history && (
                          <Tag size="small" color="cyan">Can View History</Tag>
                        )}
                        {profile.access_info?.permissions?.can_export_data && (
                          <Tag size="small" color="geekblue">Can Export</Tag>
                        )}
                      </Space>
                    </div>
                    
                    {renderExpirationWarning(profile)}
                  </div>
                }
              />
            </List.Item>
          )}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} profiles`
          }}
        />
      </Card>

      {/* Profile Details Modal */}
      <Modal
        title="Profile Details"
        open={profileModalVisible}
        onCancel={() => setProfileModalVisible(false)}
        footer={null}
        width={700}
      >
        {selectedProfile && (
          <div>
            <Descriptions bordered column={2}>
              <Descriptions.Item label="Profile Name" span={2}>
                {selectedProfile.profile.name}
              </Descriptions.Item>
              <Descriptions.Item label="Access Type">
                <Tag 
                  color={getAccessTypeColor(selectedProfile.access_info?.accessType)}
                  icon={getAccessTypeIcon(selectedProfile.access_info?.accessType)}
                >
                  {selectedProfile.access_info?.accessType?.toUpperCase() || 'UNKNOWN'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Access Level">
                <Tag color="purple">
                  {selectedProfile.access_info?.permissions?.access_level?.toUpperCase() || 'UNKNOWN'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Facebook Status">
                <Tag color={selectedProfile.profile.facebook_logged_in ? 'green' : 'default'}>
                  {selectedProfile.profile.facebook_logged_in ? 'Logged In' : 'Not Logged In'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Last Used">
                {selectedProfile.profile.last_used ? 
                  moment(selectedProfile.profile.last_used).format('YYYY-MM-DD HH:mm:ss') : 
                  'Never'
                }
              </Descriptions.Item>
              {selectedProfile.expires_at && (
                <Descriptions.Item label="Expires At" span={2}>
                  <Space>
                    <ClockCircleOutlined />
                    {moment(selectedProfile.expires_at).format('YYYY-MM-DD HH:mm:ss')}
                    <Tag color="orange">
                      {moment(selectedProfile.expires_at).fromNow()}
                    </Tag>
                  </Space>
                </Descriptions.Item>
              )}
            </Descriptions>

            <Card title="Permissions" style={{ marginTop: 16 }}>
              <Row gutter={16}>
                <Col span={8}>
                  <div style={{ textAlign: 'center' }}>
                    <CheckCircleOutlined 
                      style={{ 
                        fontSize: 24, 
                        color: selectedProfile.access_info?.permissions?.can_modify_profile ? '#52c41a' : '#d9d9d9' 
                      }} 
                    />
                    <div>Can Modify Profile</div>
                  </div>
                </Col>
                <Col span={8}>
                  <div style={{ textAlign: 'center' }}>
                    <CheckCircleOutlined 
                      style={{ 
                        fontSize: 24, 
                        color: selectedProfile.access_info?.permissions?.can_view_history ? '#52c41a' : '#d9d9d9' 
                      }} 
                    />
                    <div>Can View History</div>
                  </div>
                </Col>
                <Col span={8}>
                  <div style={{ textAlign: 'center' }}>
                    <CheckCircleOutlined 
                      style={{ 
                        fontSize: 24, 
                        color: selectedProfile.access_info?.permissions?.can_export_data ? '#52c41a' : '#d9d9d9' 
                      }} 
                    />
                    <div>Can Export Data</div>
                  </div>
                </Col>
              </Row>
            </Card>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default UserSharedProfiles;
