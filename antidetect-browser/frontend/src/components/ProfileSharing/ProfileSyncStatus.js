import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Table,
  Tag,
  Progress,
  Button,
  Space,
  Tooltip,
  Modal,
  Descriptions,
  Timeline,
  Alert,
  Statistic,
  Row,
  Col,
  Select,
  DatePicker,
  message
} from 'antd';
import {
  SyncOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  ReloadOutlined,
  EyeOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined
} from '@ant-design/icons';
import moment from 'moment';
import { apiService } from '../../services/api';

const { Option } = Select;
const { RangePicker } = DatePicker;

const ProfileSyncStatus = () => {
  const [loading, setLoading] = useState(false);
  const [syncLogs, setSyncLogs] = useState([]);
  const [activeSessions, setActiveSessions] = useState([]);
  const [selectedLog, setSelectedLog] = useState(null);
  const [logModalVisible, setLogModalVisible] = useState(false);
  const [realTimeEnabled, setRealTimeEnabled] = useState(false);
  const [filters, setFilters] = useState({
    status: 'all',
    syncType: 'all',
    dateRange: null
  });
  const wsRef = useRef(null);

  useEffect(() => {
    loadData();
    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);

  useEffect(() => {
    if (realTimeEnabled) {
      connectWebSocket();
    } else {
      disconnectWebSocket();
    }
  }, [realTimeEnabled]);

  const loadData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        loadSyncLogs(),
        loadActiveSessions()
      ]);
    } catch (error) {
      message.error('Failed to load sync data');
    } finally {
      setLoading(false);
    }
  };

  const loadSyncLogs = async () => {
    try {
      // This would need to be implemented in the backend
      const response = await apiService.get('/profile-sharing/sync-logs', {
        params: filters
      });
      if (response.success) {
        setSyncLogs(response.data || []);
      }
    } catch (error) {
      console.error('Failed to load sync logs:', error);
    }
  };

  const loadActiveSessions = async () => {
    try {
      const response = await apiService.get('/profile-sharing/sync/active-sessions');
      if (response.success) {
        setActiveSessions(response.data.active_sessions || []);
      }
    } catch (error) {
      console.error('Failed to load active sessions:', error);
    }
  };

  const connectWebSocket = () => {
    try {
      const wsUrl = `ws://localhost:8000/api/profile-ws/ws/${getCurrentUserId()}`;
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('WebSocket connected for sync status');
        message.success('Real-time sync monitoring enabled');
      };

      wsRef.current.onmessage = (event) => {
        const data = JSON.parse(event.data);
        handleWebSocketMessage(data);
      };

      wsRef.current.onclose = () => {
        console.log('WebSocket disconnected');
        if (realTimeEnabled) {
          // Attempt to reconnect after 5 seconds
          setTimeout(connectWebSocket, 5000);
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error);
        message.error('Real-time connection failed');
      };
    } catch (error) {
      console.error('Failed to connect WebSocket:', error);
    }
  };

  const disconnectWebSocket = () => {
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
  };

  const handleWebSocketMessage = (data) => {
    switch (data.type) {
      case 'sync_started':
        message.info(`Sync started for profile ${data.profile_id}`);
        loadActiveSessions();
        break;
      case 'sync_completed':
        message.success(`Sync completed for profile ${data.profile_id}`);
        loadData();
        break;
      case 'sync_failed':
        message.error(`Sync failed for profile ${data.profile_id}`);
        loadData();
        break;
      case 'sync_conflict':
        message.warning(`Sync conflict detected for profile ${data.profile_id}`);
        break;
      default:
        console.log('Unknown WebSocket message:', data);
    }
  };

  const getCurrentUserId = () => {
    // This should get the current user ID from your auth context
    return 1; // Placeholder
  };

  const handleForceSync = async (profileId) => {
    try {
      const response = await apiService.post(`/profile-sharing/sync/${profileId}/force-sync`);
      if (response.success || response.sync_id) {
        message.success('Force sync initiated');
        loadData();
      } else {
        message.error('Failed to initiate force sync');
      }
    } catch (error) {
      message.error('Failed to initiate force sync');
      console.error('Force sync error:', error);
    }
  };

  const handleResolveConflicts = async (profileId) => {
    try {
      const response = await apiService.post(`/profile-sharing/sync/${profileId}/resolve-conflicts`);
      if (response.success) {
        message.success('Conflicts resolved');
        loadData();
      } else {
        message.error('Failed to resolve conflicts');
      }
    } catch (error) {
      message.error('Failed to resolve conflicts');
      console.error('Resolve conflicts error:', error);
    }
  };

  const showLogDetails = (log) => {
    setSelectedLog(log);
    setLogModalVisible(true);
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'failed':
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'in_progress':
        return <SyncOutlined spin style={{ color: '#1890ff' }} />;
      case 'pending':
        return <ClockCircleOutlined style={{ color: '#faad14' }} />;
      default:
        return <WarningOutlined style={{ color: '#d9d9d9' }} />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'success';
      case 'failed': return 'error';
      case 'in_progress': return 'processing';
      case 'pending': return 'warning';
      default: return 'default';
    }
  };

  const formatDataSize = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const syncLogColumns = [
    {
      title: 'Profile',
      dataIndex: 'profile_id',
      key: 'profile_id',
      render: (profileId) => `Profile ${profileId}`
    },
    {
      title: 'Type',
      dataIndex: 'sync_type',
      key: 'sync_type',
      render: (type) => (
        <Tag color={type === 'full' ? 'blue' : 'green'}>
          {type?.toUpperCase()}
        </Tag>
      )
    },
    {
      title: 'Direction',
      dataIndex: 'sync_direction',
      key: 'sync_direction',
      render: (direction) => (
        <Tag color="purple">
          {direction?.toUpperCase()}
        </Tag>
      )
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Space>
          {getStatusIcon(status)}
          <Tag color={getStatusColor(status)}>
            {status?.toUpperCase()}
          </Tag>
        </Space>
      )
    },
    {
      title: 'Progress',
      key: 'progress',
      render: (_, record) => {
        if (record.status === 'in_progress') {
          const progress = record.items_synced && record.total_items ? 
            (record.items_synced / record.total_items) * 100 : 0;
          return <Progress percent={Math.round(progress)} size="small" />;
        }
        return record.status === 'completed' ? 
          <Progress percent={100} size="small" status="success" /> :
          <Progress percent={0} size="small" status="exception" />;
      }
    },
    {
      title: 'Items',
      dataIndex: 'items_synced',
      key: 'items_synced',
      render: (items) => items || 0
    },
    {
      title: 'Data Size',
      dataIndex: 'data_size_bytes',
      key: 'data_size_bytes',
      render: (size) => formatDataSize(size || 0)
    },
    {
      title: 'Started',
      dataIndex: 'started_at',
      key: 'started_at',
      render: (time) => moment(time).format('MM-DD HH:mm:ss')
    },
    {
      title: 'Duration',
      key: 'duration',
      render: (_, record) => {
        if (record.completed_at && record.started_at) {
          const duration = moment(record.completed_at).diff(moment(record.started_at), 'seconds');
          return `${duration}s`;
        }
        return record.status === 'in_progress' ? 
          `${moment().diff(moment(record.started_at), 'seconds')}s` : '-';
      }
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => showLogDetails(record)}
            size="small"
          >
            Details
          </Button>
          {record.status === 'failed' && (
            <Button
              type="link"
              icon={<ReloadOutlined />}
              onClick={() => handleForceSync(record.profile_id)}
              size="small"
            >
              Retry
            </Button>
          )}
        </Space>
      )
    }
  ];

  const activeSessionColumns = [
    {
      title: 'Profile',
      dataIndex: 'profile_id',
      key: 'profile_id',
      render: (profileId) => `Profile ${profileId}`
    },
    {
      title: 'Type',
      dataIndex: 'sync_type',
      key: 'sync_type',
      render: (type) => <Tag color="blue">{type?.toUpperCase()}</Tag>
    },
    {
      title: 'Progress',
      key: 'progress',
      render: (_, record) => {
        const progress = record.items_synced && record.total_items ? 
          (record.items_synced / record.total_items) * 100 : 0;
        return (
          <div>
            <Progress percent={Math.round(progress)} size="small" />
            <small>{record.items_synced || 0} items synced</small>
          </div>
        );
      }
    },
    {
      title: 'Data Size',
      dataIndex: 'data_size_bytes',
      key: 'data_size_bytes',
      render: (size) => formatDataSize(size || 0)
    },
    {
      title: 'Running Time',
      dataIndex: 'started_at',
      key: 'running_time',
      render: (startTime) => {
        const duration = moment().diff(moment(startTime), 'seconds');
        return `${duration}s`;
      }
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      {/* Statistics */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Active Sessions"
              value={activeSessions.length}
              prefix={<SyncOutlined spin />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Completed Today"
              value={syncLogs.filter(log => 
                log.status === 'completed' && 
                moment(log.started_at).isAfter(moment().startOf('day'))
              ).length}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Failed Today"
              value={syncLogs.filter(log => 
                log.status === 'failed' && 
                moment(log.started_at).isAfter(moment().startOf('day'))
              ).length}
              prefix={<CloseCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Real-time Monitoring"
              value={realTimeEnabled ? 'ON' : 'OFF'}
              prefix={
                <Button
                  type="link"
                  icon={realTimeEnabled ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                  onClick={() => setRealTimeEnabled(!realTimeEnabled)}
                  style={{ padding: 0 }}
                />
              }
              valueStyle={{ color: realTimeEnabled ? '#52c41a' : '#d9d9d9' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Active Sessions */}
      {activeSessions.length > 0 && (
        <Card
          title="Active Sync Sessions"
          style={{ marginBottom: '24px' }}
        >
          <Table
            columns={activeSessionColumns}
            dataSource={activeSessions}
            rowKey="sync_id"
            pagination={false}
            size="small"
          />
        </Card>
      )}

      {/* Sync Logs */}
      <Card
        title="Sync History"
        extra={
          <Space>
            <Select
              value={filters.status}
              onChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
              style={{ width: 120 }}
            >
              <Option value="all">All Status</Option>
              <Option value="completed">Completed</Option>
              <Option value="failed">Failed</Option>
              <Option value="in_progress">In Progress</Option>
            </Select>
            <Select
              value={filters.syncType}
              onChange={(value) => setFilters(prev => ({ ...prev, syncType: value }))}
              style={{ width: 120 }}
            >
              <Option value="all">All Types</Option>
              <Option value="full">Full Sync</Option>
              <Option value="incremental">Incremental</Option>
            </Select>
            <Button
              icon={<ReloadOutlined />}
              onClick={loadData}
              loading={loading}
            >
              Refresh
            </Button>
          </Space>
        }
      >
        <Table
          columns={syncLogColumns}
          dataSource={syncLogs}
          loading={loading}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} sync logs`
          }}
        />
      </Card>

      {/* Log Details Modal */}
      <Modal
        title="Sync Log Details"
        open={logModalVisible}
        onCancel={() => setLogModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedLog && (
          <div>
            <Descriptions bordered column={2}>
              <Descriptions.Item label="Profile ID">
                {selectedLog.profile_id}
              </Descriptions.Item>
              <Descriptions.Item label="Sync Type">
                <Tag color="blue">{selectedLog.sync_type?.toUpperCase()}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Direction">
                <Tag color="purple">{selectedLog.sync_direction?.toUpperCase()}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Status">
                <Space>
                  {getStatusIcon(selectedLog.status)}
                  <Tag color={getStatusColor(selectedLog.status)}>
                    {selectedLog.status?.toUpperCase()}
                  </Tag>
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="Items Synced">
                {selectedLog.items_synced || 0}
              </Descriptions.Item>
              <Descriptions.Item label="Data Size">
                {formatDataSize(selectedLog.data_size_bytes || 0)}
              </Descriptions.Item>
              <Descriptions.Item label="Conflicts Resolved">
                {selectedLog.conflicts_resolved || 0}
              </Descriptions.Item>
              <Descriptions.Item label="Started At">
                {moment(selectedLog.started_at).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
              <Descriptions.Item label="Completed At">
                {selectedLog.completed_at ? 
                  moment(selectedLog.completed_at).format('YYYY-MM-DD HH:mm:ss') : 
                  'Not completed'
                }
              </Descriptions.Item>
              <Descriptions.Item label="Duration">
                {selectedLog.completed_at && selectedLog.started_at ? 
                  `${moment(selectedLog.completed_at).diff(moment(selectedLog.started_at), 'seconds')}s` : 
                  'N/A'
                }
              </Descriptions.Item>
            </Descriptions>

            {selectedLog.error_message && (
              <Alert
                message="Error Details"
                description={selectedLog.error_message}
                type="error"
                showIcon
                style={{ marginTop: 16 }}
              />
            )}

            {selectedLog.error_details && (
              <Card title="Technical Details" style={{ marginTop: 16 }}>
                <pre style={{ background: '#f5f5f5', padding: 12, borderRadius: 4 }}>
                  {JSON.stringify(selectedLog.error_details, null, 2)}
                </pre>
              </Card>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ProfileSyncStatus;
