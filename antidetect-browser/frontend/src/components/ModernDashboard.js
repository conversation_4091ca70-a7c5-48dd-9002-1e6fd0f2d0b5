/**
 * Modern Dashboard Component - Desktop Design
 */

import React, { useState, useEffect } from 'react';
import {
  Card, Row, Col, Statistic, Progress, List, Avatar, Tag, 
  Button, Space, Timeline, Alert, Spin, Empty, Typography,
  Divider, Badge, Tooltip, notification
} from 'antd';
import {
  UserOutlined, SearchOutlined, MessageOutlined, SettingOutlined,
  TrophyOutlined, ClockCircleOutlined, CheckCircleOutlined,
  ExclamationCircleOutlined, ReloadOutlined, PlayCircleOutlined,
  RocketOutlined, DatabaseOutlined, CloudServerOutlined,
  LineChartOutlined, SafetyOutlined, ThunderboltOutlined,
  FireOutlined, StarOutlined
} from '@ant-design/icons';
import { apiService } from '../services/api';
import { useAuth } from '../contexts/AuthContext';

const { Title, Text } = Typography;

const ModernDashboard = () => {
  const { isAuthenticated } = useAuth();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    profiles: { total: 0, active: 0, logged_in: 0 },
    scraping: { total_tasks: 0, completed: 0, running: 0 },
    messaging: { total_tasks: 0, messages_sent: 0, success_rate: 0 },
    system: { uptime: 0, memory_usage: 0, cpu_usage: 0 }
  });
  const [recentActivities, setRecentActivities] = useState([]);
  const [systemHealth, setSystemHealth] = useState('healthy');
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    // Only load data if user is authenticated
    if (isAuthenticated) {
      loadDashboardData();
    } else {
      setLoading(false);
    }
    
    // Set up auto-refresh every 30 seconds
    const interval = setInterval(loadDashboardData, 30000);
    
    // Update time every second
    const timeInterval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    
    return () => {
      clearInterval(interval);
      clearInterval(timeInterval);
    };
  }, [isAuthenticated]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // Load all dashboard data
      const [profilesData, scrapingData, messagingData, systemData] = await Promise.all([
        apiService.getProfiles().catch(() => []),
        apiService.get('/api/scraping/').catch(() => []),
        apiService.get('/api/messaging/').catch(() => []),
        apiService.get('/api/system/stats').catch(() => ({ performance: {} }))
      ]);
      
      // Ensure data is arrays
      const safeProfilesData = Array.isArray(profilesData) ? profilesData : [];
      const safeScrapingData = Array.isArray(scrapingData) ? scrapingData : [];
      const safeMessagingData = Array.isArray(messagingData) ? messagingData : [];

      // Process profiles stats
      const profileStats = {
        total: safeProfilesData.length,
        active: safeProfilesData.filter(p => p.status === 'active').length,
        logged_in: safeProfilesData.filter(p => p.facebook_logged_in).length
      };

      // Process scraping stats
      const scrapingStats = {
        total_tasks: safeScrapingData.length,
        completed: safeScrapingData.filter(t => t.status === 'completed').length,
        running: safeScrapingData.filter(t => t.status === 'running').length
      };

      // Process messaging stats
      const messagingStats = {
        total_tasks: safeMessagingData.length,
        messages_sent: safeMessagingData.reduce((sum, t) => sum + (t.messages_sent || 0), 0),
        success_rate: safeMessagingData.length > 0 ?
          (safeMessagingData.reduce((sum, t) => sum + (t.messages_sent || 0), 0) /
           safeMessagingData.reduce((sum, t) => sum + (t.total_recipients || 1), 0) * 100) : 0
      };
      
      setStats({
        profiles: profileStats,
        scraping: scrapingStats,
        messaging: messagingStats,
        system: systemData.performance || {}
      });
      
      // Generate recent activities
      const activities = [
        ...safeScrapingData.slice(0, 3).map(task => ({
          type: 'scraping',
          title: `Scraping task completed`,
          description: `Found ${task.total_found || 0} users from Facebook post`,
          time: task.completed_at || task.created_at,
          status: task.status
        })),
        ...safeMessagingData.slice(0, 3).map(task => ({
          type: 'messaging',
          title: `Messaging campaign finished`,
          description: `Sent ${task.messages_sent || 0} messages to recipients`,
          time: task.completed_at || task.created_at,
          status: task.status
        }))
      ].sort((a, b) => new Date(b.time) - new Date(a.time)).slice(0, 5);
      
      setRecentActivities(activities);
      setSystemHealth('healthy');
      
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      setSystemHealth('error');
      notification.error({
        message: 'Dashboard Error',
        description: 'Failed to load dashboard data. Please check your connection.',
        duration: 4
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'success';
      case 'running': return 'processing';
      case 'failed': return 'error';
      default: return 'default';
    }
  };

  const getActivityIcon = (type) => {
    switch (type) {
      case 'scraping': return <SearchOutlined style={{ color: '#1890ff' }} />;
      case 'messaging': return <MessageOutlined style={{ color: '#52c41a' }} />;
      default: return <ClockCircleOutlined />;
    }
  };

  if (loading) {
    return (
      <div className="loading-container" style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '60vh',
        flexDirection: 'column'
      }}>
        <Spin size="large" />
        <Text style={{ marginTop: '16px', fontSize: '16px' }}>Loading dashboard...</Text>
      </div>
    );
  }

  return (
    <div className="modern-dashboard fade-in">
      {/* Hero Header Section */}
      <div className="dashboard-hero" style={{ 
        marginBottom: '32px', 
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        borderRadius: '20px',
        padding: '32px',
        color: 'white',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* Background Pattern */}
        <div style={{
          position: 'absolute',
          top: 0,
          right: 0,
          width: '200px',
          height: '200px',
          background: 'rgba(255,255,255,0.1)',
          borderRadius: '50%',
          transform: 'translate(50%, -50%)'
        }} />
        
        <Row justify="space-between" align="middle">
          <Col>
            <Space direction="vertical" size="small">
              <Title level={1} style={{ color: 'white', margin: 0, fontSize: '36px' }}>
                🚀 Facebook Automation
              </Title>
              <Text style={{ color: 'rgba(255,255,255,0.9)', fontSize: '18px' }}>
                Complete automation solution for Facebook marketing
              </Text>
              <div style={{ marginTop: '12px', fontSize: '14px', opacity: 0.8 }}>
                <ClockCircleOutlined style={{ marginRight: '8px' }} />
                {currentTime.toLocaleString()}
              </div>
            </Space>
          </Col>
          <Col>
            <Space direction="vertical" align="end">
              <Badge status="processing" text="System Online" style={{ color: 'white', fontSize: '16px' }} />
              <Button 
                icon={<ReloadOutlined />} 
                onClick={loadDashboardData}
                loading={loading}
                size="large"
                style={{ 
                  background: 'rgba(255,255,255,0.2)', 
                  border: 'none', 
                  color: 'white',
                  borderRadius: '12px',
                  backdropFilter: 'blur(10px)'
                }}
              >
                Refresh Data
              </Button>
            </Space>
          </Col>
        </Row>
      </div>

      {/* System Health Alert */}
      {systemHealth !== 'healthy' && (
        <Alert
          message="System Status Warning"
          description="Some services may be unavailable. Please check your backend connection."
          type="warning"
          showIcon
          style={{ marginBottom: '24px', borderRadius: '12px' }}
          action={
            <Button size="small" onClick={loadDashboardData}>
              Retry Connection
            </Button>
          }
        />
      )}

      {/* Modern Statistics Cards */}
      <Row gutter={[24, 24]} style={{ marginBottom: '32px' }}>
        <Col xs={24} sm={12} lg={6}>
          <Card className="custom-card bounce-in" style={{ 
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            border: 'none',
            color: 'white',
            borderRadius: '16px',
            overflow: 'hidden'
          }}>
            <div style={{ position: 'relative' }}>
              <UserOutlined style={{ 
                position: 'absolute', 
                top: '-10px', 
                right: '-10px', 
                fontSize: '60px', 
                opacity: 0.2 
              }} />
              <Statistic
                title={<span style={{ color: 'rgba(255,255,255,0.9)', fontSize: '14px' }}>Browser Profiles</span>}
                value={stats.profiles.total}
                valueStyle={{ color: 'white', fontSize: '32px', fontWeight: 'bold' }}
              />
              <div style={{ marginTop: '12px', fontSize: '14px', opacity: 0.9 }}>
                <CheckCircleOutlined style={{ marginRight: '6px' }} />
                {stats.profiles.logged_in} logged into Facebook
              </div>
              <Progress 
                percent={stats.profiles.total > 0 ? (stats.profiles.logged_in / stats.profiles.total * 100) : 0} 
                size="small" 
                showInfo={false}
                strokeColor="rgba(255,255,255,0.8)"
                trailColor="rgba(255,255,255,0.2)"
                style={{ marginTop: '12px' }}
              />
            </div>
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card className="custom-card bounce-in" style={{ 
            background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
            border: 'none',
            color: 'white',
            borderRadius: '16px',
            overflow: 'hidden'
          }}>
            <div style={{ position: 'relative' }}>
              <SearchOutlined style={{ 
                position: 'absolute', 
                top: '-10px', 
                right: '-10px', 
                fontSize: '60px', 
                opacity: 0.2 
              }} />
              <Statistic
                title={<span style={{ color: 'rgba(255,255,255,0.9)', fontSize: '14px' }}>Scraping Tasks</span>}
                value={stats.scraping.total_tasks}
                valueStyle={{ color: 'white', fontSize: '32px', fontWeight: 'bold' }}
              />
              <div style={{ marginTop: '12px', fontSize: '14px', opacity: 0.9 }}>
                <TrophyOutlined style={{ marginRight: '6px' }} />
                {stats.scraping.completed} completed successfully
              </div>
              <Progress 
                percent={stats.scraping.total_tasks > 0 ? (stats.scraping.completed / stats.scraping.total_tasks * 100) : 0} 
                size="small" 
                showInfo={false}
                strokeColor="rgba(255,255,255,0.8)"
                trailColor="rgba(255,255,255,0.2)"
                style={{ marginTop: '12px' }}
              />
            </div>
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card className="custom-card bounce-in" style={{ 
            background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
            border: 'none',
            color: 'white',
            borderRadius: '16px',
            overflow: 'hidden'
          }}>
            <div style={{ position: 'relative' }}>
              <MessageOutlined style={{ 
                position: 'absolute', 
                top: '-10px', 
                right: '-10px', 
                fontSize: '60px', 
                opacity: 0.2 
              }} />
              <Statistic
                title={<span style={{ color: 'rgba(255,255,255,0.9)', fontSize: '14px' }}>Messages Sent</span>}
                value={stats.messaging.messages_sent}
                valueStyle={{ color: 'white', fontSize: '32px', fontWeight: 'bold' }}
              />
              <div style={{ marginTop: '12px', fontSize: '14px', opacity: 0.9 }}>
                <FireOutlined style={{ marginRight: '6px' }} />
                {stats.messaging.total_tasks} campaigns completed
              </div>
              <Progress 
                percent={Math.min(stats.messaging.success_rate, 100)} 
                size="small" 
                showInfo={false}
                strokeColor="rgba(255,255,255,0.8)"
                trailColor="rgba(255,255,255,0.2)"
                style={{ marginTop: '12px' }}
              />
            </div>
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card className="custom-card bounce-in" style={{ 
            background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
            border: 'none',
            color: 'white',
            borderRadius: '16px',
            overflow: 'hidden'
          }}>
            <div style={{ position: 'relative' }}>
              <StarOutlined style={{ 
                position: 'absolute', 
                top: '-10px', 
                right: '-10px', 
                fontSize: '60px', 
                opacity: 0.2 
              }} />
              <Statistic
                title={<span style={{ color: 'rgba(255,255,255,0.9)', fontSize: '14px' }}>Success Rate</span>}
                value={stats.messaging.success_rate}
                precision={1}
                suffix="%"
                valueStyle={{ color: 'white', fontSize: '32px', fontWeight: 'bold' }}
              />
              <div style={{ marginTop: '12px', fontSize: '14px', opacity: 0.9 }}>
                <ThunderboltOutlined style={{ marginRight: '6px' }} />
                Average campaign performance
              </div>
              <Progress 
                percent={Math.min(stats.messaging.success_rate, 100)} 
                size="small" 
                showInfo={false}
                strokeColor="rgba(255,255,255,0.8)"
                trailColor="rgba(255,255,255,0.2)"
                style={{ marginTop: '12px' }}
              />
            </div>
          </Card>
        </Col>
      </Row>

      <Row gutter={[24, 24]}>
        {/* Quick Actions Panel */}
        <Col xs={24} lg={8}>
          <Card 
            title={
              <Space>
                <RocketOutlined style={{ color: '#667eea' }} />
                <span>Quick Actions</span>
              </Space>
            }
            className="custom-card slide-up"
            style={{ marginBottom: '24px', borderRadius: '16px' }}
          >
            <Space direction="vertical" style={{ width: '100%' }} size="middle">
              <Button 
                type="primary" 
                block 
                size="large"
                icon={<UserOutlined />}
                onClick={() => window.location.hash = '/profiles'}
                className="gradient-button"
                style={{ height: '50px', borderRadius: '12px' }}
              >
                Manage Profiles
              </Button>
              <Button 
                type="primary" 
                block 
                size="large"
                icon={<SearchOutlined />}
                onClick={() => window.location.hash = '/scraping'}
                className="gradient-button"
                style={{ height: '50px', borderRadius: '12px' }}
              >
                Start Scraping
              </Button>
              <Button 
                type="primary" 
                block 
                size="large"
                icon={<MessageOutlined />}
                onClick={() => window.location.hash = '/messaging'}
                className="gradient-button"
                style={{ height: '50px', borderRadius: '12px' }}
              >
                Send Messages
              </Button>
              <Button 
                type="default" 
                block 
                size="large"
                icon={<SettingOutlined />}
                onClick={() => window.location.hash = '/settings'}
                style={{ height: '50px', borderRadius: '12px' }}
              >
                Settings
              </Button>
            </Space>
          </Card>

          {/* System Status */}
          <Card 
            title={
              <Space>
                <DatabaseOutlined style={{ color: '#52c41a' }} />
                <span>System Status</span>
              </Space>
            }
            className="custom-card slide-up"
            style={{ borderRadius: '16px' }}
          >
            <Timeline
              items={[
                {
                  dot: <CheckCircleOutlined style={{ color: '#52c41a', fontSize: '16px' }} />,
                  children: (
                    <div>
                      <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>Backend Service</div>
                      <div style={{ color: '#52c41a', fontSize: '14px' }}>Connected and running</div>
                    </div>
                  ),
                },
                {
                  dot: <CheckCircleOutlined style={{ color: '#52c41a', fontSize: '16px' }} />,
                  children: (
                    <div>
                      <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>Database</div>
                      <div style={{ color: '#52c41a', fontSize: '14px' }}>Operational</div>
                    </div>
                  ),
                },
                {
                  dot: <CheckCircleOutlined style={{ color: '#52c41a', fontSize: '16px' }} />,
                  children: (
                    <div>
                      <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>Browser Engine</div>
                      <div style={{ color: '#52c41a', fontSize: '14px' }}>Ready for automation</div>
                    </div>
                  ),
                },
              ]}
            />
          </Card>
        </Col>

        {/* Recent Activities */}
        <Col xs={24} lg={16}>
          <Card 
            title={
              <Space>
                <LineChartOutlined style={{ color: '#1890ff' }} />
                <span>Recent Activities</span>
              </Space>
            }
            className="custom-card slide-up"
            style={{ borderRadius: '16px' }}
            extra={
              <Button 
                type="link" 
                icon={<PlayCircleOutlined />}
                onClick={() => window.location.hash = '/scraping'}
              >
                Start New Task
              </Button>
            }
          >
            {recentActivities.length > 0 ? (
              <List
                itemLayout="horizontal"
                dataSource={recentActivities}
                renderItem={(item) => (
                  <List.Item style={{ padding: '16px 0', borderBottom: '1px solid #f0f0f0' }}>
                    <List.Item.Meta
                      avatar={
                        <Avatar 
                          icon={getActivityIcon(item.type)} 
                          style={{ 
                            background: item.type === 'scraping' ? '#e6f7ff' : '#f6ffed',
                            border: `2px solid ${item.type === 'scraping' ? '#1890ff' : '#52c41a'}`
                          }}
                        />
                      }
                      title={
                        <Space>
                          <span style={{ fontWeight: 'bold' }}>{item.title}</span>
                          <Tag color={getStatusColor(item.status)}>{item.status}</Tag>
                        </Space>
                      }
                      description={
                        <div>
                          <div style={{ marginBottom: '4px' }}>{item.description}</div>
                          <div style={{ fontSize: '12px', color: '#999' }}>
                            <ClockCircleOutlined style={{ marginRight: '4px' }} />
                            {new Date(item.time).toLocaleString()}
                          </div>
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            ) : (
              <Empty 
                description="No recent activities"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                style={{ padding: '40px 0' }}
              />
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default ModernDashboard;
