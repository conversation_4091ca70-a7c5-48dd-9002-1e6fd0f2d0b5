import React, { useState, useEffect } from 'react';
import api from '../services/api';
import { ProfileAPI } from '../utils/unifiedProfileManager';
import './ProfileDataManager.css';

const ProfileDataManager = ({ profileId, onClose }) => {
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState(''); // 'success' or 'error'
  const [profileData, setProfileData] = useState(null);
  const [dataSize, setDataSize] = useState(null);

  // Sample browser data for testing
  const [testData, setTestData] = useState({
    localStorage: {
      facebook_token: 'sample_token_' + Date.now(),
      user_preferences: {
        theme: 'dark',
        language: 'vi',
        notifications: true
      },
      last_login: new Date().toISOString()
    },
    cookies: [
      {
        name: 'session_id',
        value: 'sess_' + Math.random().toString(36).substr(2, 9),
        domain: 'facebook.com',
        path: '/',
        secure: true,
        httpOnly: true
      },
      {
        name: 'csrf_token',
        value: 'csrf_' + Math.random().toString(36).substr(2, 9),
        domain: 'facebook.com',
        path: '/',
        secure: true
      }
    ],
    history: [
      {
        url: 'https://facebook.com',
        title: 'Facebook - Đăng nhập hoặc đăng ký',
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        visitCount: 1
      },
      {
        url: 'https://facebook.com/profile',
        title: 'Trang cá nhân',
        timestamp: new Date(Date.now() - 1800000).toISOString(),
        visitCount: 2
      },
      {
        url: 'https://facebook.com/messages',
        title: 'Tin nhắn',
        timestamp: new Date().toISOString(),
        visitCount: 1
      }
    ],
    fingerprint: {
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
      screen: {
        width: 1920,
        height: 1080,
        colorDepth: 24
      },
      timezone: 'Asia/Ho_Chi_Minh',
      language: 'vi-VN',
      platform: 'MacIntel'
    },
    metadata: {
      saved_by: 'user',
      browser_version: 'Camoufox 1.0',
      profile_version: '1.0',
      notes: 'Test profile data for development'
    }
  });

  const showMessage = (text, type = 'info') => {
    setMessage(text);
    setMessageType(type);
    setTimeout(() => {
      setMessage('');
      setMessageType('');
    }, 5000);
  };

  const handleSaveData = async () => {
    setLoading(true);
    try {
      console.log('🔍 [ProfileDataManager] Starting save data for profile:', profileId);

      // Create a mock profile object for the shared utility
      const mockProfile = {
        id: profileId,
        account_id: profileId, // Assuming profileId is the account_id
        name: `Profile ${profileId}`
      };

      // Use unified profile manager for complete flow
      const result = await ProfileAPI.captureAndSave(mockProfile, {
        showLoadingMessage: false // We'll handle messages manually
      });

      if (!result.success) {
        console.error('❌ [ProfileDataManager] Unified capture and save failed:', result.message);
        showMessage(`Lỗi thực hiện: ${result.message}`, 'error');
        return;
      }

      console.log('✅ [ProfileDataManager] Save completed successfully');

      // Show success message with details
      const localStorageCount = Object.keys(captureResult.data.localStorage?.items || {}).length;
      const cookiesCount = (captureResult.data.cookies || []).length;
      showMessage(`Đã lưu thành công! Captured ${localStorageCount} localStorage items, ${cookiesCount} cookies`, 'success');

      setDataSize(saveResult.data?.data_size || 'Unknown');

    } catch (error) {
      console.error('❌ [ProfileDataManager] Save data error:', error);
      showMessage(`Lỗi lưu data: ${error.message}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleLoadData = async () => {
    setLoading(true);
    try {
      const response = await api.loadBrowserData(profileId);
      
      if (response.success) {
        setProfileData(response.data);
        showMessage('Đã tải data thành công!', 'success');
      } else {
        showMessage(`Lỗi tải data: ${response.message}`, 'error');
        setProfileData(null);
      }
    } catch (error) {
      console.error('Load data error:', error);
      showMessage(`Lỗi tải data: ${error.message}`, 'error');
      setProfileData(null);
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateNewTestData = () => {
    setTestData({
      ...testData,
      localStorage: {
        ...testData.localStorage,
        facebook_token: 'sample_token_' + Date.now(),
        last_login: new Date().toISOString()
      },
      cookies: testData.cookies.map(cookie => ({
        ...cookie,
        value: cookie.name + '_' + Math.random().toString(36).substr(2, 9)
      })),
      history: [
        ...testData.history,
        {
          url: 'https://facebook.com/feed',
          title: 'Bảng tin',
          timestamp: new Date().toISOString(),
          visitCount: 1
        }
      ]
    });
    showMessage('Đã tạo test data mới!', 'success');
  };

  const formatDataSize = (size) => {
    if (!size) return 'N/A';
    const total = Object.values(size).reduce((sum, val) => sum + val, 0);
    return `${total} bytes`;
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="profile-data-manager">
      <div className="modal-overlay" onClick={onClose}>
        <div className="modal-content" onClick={e => e.stopPropagation()}>
          <div className="modal-header">
            <h2>Profile Data Manager - Profile {profileId}</h2>
            <button className="close-btn" onClick={onClose}>×</button>
          </div>

          <div className="modal-body">
            {message && (
              <div className={`message ${messageType}`}>
                {message}
              </div>
            )}

            <div className="section">
              <h3>Test Data Operations</h3>
              <div className="button-group">
                <button 
                  onClick={handleGenerateNewTestData}
                  disabled={loading}
                  className="btn btn-secondary"
                >
                  🔄 Tạo Test Data Mới
                </button>
                <button 
                  onClick={handleSaveData}
                  disabled={loading}
                  className="btn btn-primary"
                >
                  {loading ? '⏳ Đang lưu...' : '💾 Lưu Browser Data'}
                </button>
                <button 
                  onClick={handleLoadData}
                  disabled={loading}
                  className="btn btn-success"
                >
                  {loading ? '⏳ Đang tải...' : '📂 Tải Browser Data'}
                </button>
              </div>
            </div>

            {dataSize && (
              <div className="section">
                <h3>Data Size Information</h3>
                <div className="data-size-info">
                  <div>localStorage: {formatDataSize({localStorage: dataSize.localStorage})}</div>
                  <div>IndexedDB: {formatDataSize({indexedDB: dataSize.indexedDB})}</div>
                  <div>Cookies: {formatDataSize({cookies: dataSize.cookies})}</div>
                  <div>History: {formatDataSize({history: dataSize.history})}</div>
                  <div><strong>Total: {formatDataSize(dataSize)}</strong></div>
                </div>
              </div>
            )}

            <div className="section">
              <h3>Current Test Data Preview</h3>
              <div className="data-preview">
                <div className="data-item">
                  <strong>localStorage:</strong> {Object.keys(testData.localStorage).length} items
                </div>
                <div className="data-item">
                  <strong>Cookies:</strong> {testData.cookies.length} items
                </div>
                <div className="data-item">
                  <strong>History:</strong> {testData.history.length} items
                </div>
                <div className="data-item">
                  <strong>Fingerprint:</strong> {Object.keys(testData.fingerprint).length} properties
                </div>
              </div>
            </div>

            {profileData && (
              <div className="section">
                <h3>Loaded Profile Data</h3>
                <div className="loaded-data">
                  {profileData.localStorage && (
                    <div className="data-section">
                      <h4>localStorage ({Object.keys(profileData.localStorage).length} items)</h4>
                      <pre>{JSON.stringify(profileData.localStorage, null, 2)}</pre>
                    </div>
                  )}
                  {profileData.cookies && (
                    <div className="data-section">
                      <h4>Cookies ({profileData.cookies.length} items)</h4>
                      <pre>{JSON.stringify(profileData.cookies, null, 2)}</pre>
                    </div>
                  )}
                  {profileData.history && (
                    <div className="data-section">
                      <h4>History ({profileData.history.length} items)</h4>
                      <pre>{JSON.stringify(profileData.history, null, 2)}</pre>
                    </div>
                  )}
                  {profileData.profile_info && (
                    <div className="data-section">
                      <h4>Profile Info</h4>
                      <pre>{JSON.stringify(profileData.profile_info, null, 2)}</pre>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          <div className="modal-footer">
            <button onClick={onClose} className="btn btn-secondary">
              Đóng
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileDataManager;
