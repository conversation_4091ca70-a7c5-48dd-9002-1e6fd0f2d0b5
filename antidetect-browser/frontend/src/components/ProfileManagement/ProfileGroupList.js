/**
 * ProfileGroupList Component - Display and manage profile groups in a table
 */

import React, { useState } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  Popconfirm,
  message,
  Tooltip,
  Typography,
  Card,
  Badge
} from 'antd';
import {
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  EyeOutlined,
  TeamOutlined,
  UserOutlined
} from '@ant-design/icons';
import { apiService } from '../../services/api';
import dayjs from 'dayjs';

const { Text } = Typography;

const ProfileGroupList = ({ 
  profileGroups = [], 
  loading = false, 
  onEdit, 
  onCreate, 
  onRefresh,
  onViewProfiles,
  onViewUsers
}) => {
  const [deleting, setDeleting] = useState(null);

  const handleDelete = async (group) => {
    setDeleting(group.id);
    try {
      await apiService.deleteProfileGroup(group.id);
      message.success('Profile group deleted successfully!');
      onRefresh();
    } catch (error) {
      console.error('Delete profile group error:', error);
      message.error(error.message || 'Delete failed');
    } finally {
      setDeleting(null);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'green';
      case 'inactive': return 'orange';
      case 'archived': return 'red';
      default: return 'default';
    }
  };

  const columns = [
    {
      title: 'Group Name',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space direction="vertical" size={0}>
          <Text strong>{text}</Text>
          {record.description && (
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.description}
            </Text>
          )}
        </Space>
      ),
    },
    {
      title: 'Profiles',
      dataIndex: 'profiles',
      key: 'profiles',
      render: (profiles) => (
        <Badge 
          count={profiles?.length || 0} 
          showZero 
          color="blue"
          style={{ backgroundColor: '#1890ff' }}
        />
      ),
    },
    {
      title: 'Users',
      dataIndex: 'userAccesses',
      key: 'userAccesses',
      render: (userAccesses) => {
        const activeAccesses = userAccesses?.filter(access => access.status === 'active') || [];
        return (
          <Badge 
            count={activeAccesses.length} 
            showZero 
            color="green"
            style={{ backgroundColor: '#52c41a' }}
          />
        );
      },
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {status?.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Created',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => (
        <Space direction="vertical" size={0}>
          <Text>{dayjs(date).format('MMM DD, YYYY')}</Text>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {dayjs(date).format('HH:mm')}
          </Text>
        </Space>
      ),
    },
    {
      title: 'Created By',
      dataIndex: ['createdByAdmin', 'email'],
      key: 'createdBy',
      render: (email) => (
        <Text type="secondary">{email}</Text>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="View Profiles">
            <Button 
              type="text" 
              icon={<TeamOutlined />} 
              size="small"
              onClick={() => onViewProfiles && onViewProfiles(record)}
            />
          </Tooltip>

          <Tooltip title="View Users">
            <Button 
              type="text" 
              icon={<UserOutlined />} 
              size="small"
              onClick={() => onViewUsers && onViewUsers(record)}
            />
          </Tooltip>

          <Tooltip title="View Details">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              size="small"
              onClick={() => {
                // TODO: Implement view details modal
                message.info('View details feature coming soon');
              }}
            />
          </Tooltip>

          <Tooltip title="Edit Group">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => onEdit(record)}
            />
          </Tooltip>

          <Popconfirm
            title="Delete Profile Group"
            description={
              <div>
                <p>Are you sure you want to delete this profile group?</p>
                {record.profiles?.length > 0 && (
                  <p style={{ color: 'red', margin: 0 }}>
                    This group contains {record.profiles.length} profile(s).
                  </p>
                )}
                {record.userAccesses?.length > 0 && (
                  <p style={{ color: 'red', margin: 0 }}>
                    This group has {record.userAccesses.length} user access(es).
                  </p>
                )}
              </div>
            }
            onConfirm={() => handleDelete(record)}
            okText="Yes"
            cancelText="No"
            disabled={record.profiles?.length > 0 || record.userAccesses?.length > 0}
          >
            <Tooltip 
              title={
                record.profiles?.length > 0 || record.userAccesses?.length > 0
                  ? "Cannot delete group with profiles or user accesses"
                  : "Delete Group"
              }
            >
              <Button 
                type="text" 
                danger 
                icon={<DeleteOutlined />} 
                size="small"
                loading={deleting === record.id}
                disabled={record.profiles?.length > 0 || record.userAccesses?.length > 0}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Card
      title="Profile Groups"
      extra={
        <Button 
          type="primary" 
          icon={<PlusOutlined />}
          onClick={onCreate}
        >
          Create Group
        </Button>
      }
    >
      <Table
        columns={columns}
        dataSource={profileGroups}
        loading={loading}
        rowKey="id"
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => 
            `${range[0]}-${range[1]} of ${total} groups`,
        }}
        scroll={{ x: 1200 }}
      />
    </Card>
  );
};

export default ProfileGroupList;
