/**
 * Facebook Automation Desktop - Main React App Component
 */

import React from 'react';
import { ConfigProvider } from 'antd';

// Import the modern layout
import ModernLayout from './components/ModernLayout';
import ProtectedRoute from './components/ProtectedRoute';
import { AuthProvider } from './contexts/AuthContext';
import { apiService } from './services/api';

// Import global styles
import './styles/App.css';

// Expose apiService globally before App component renders
window.apiService = apiService;

// Ant Design theme configuration
const themeConfig = {
  token: {
    colorPrimary: '#667eea',
    colorSuccess: '#52c41a',
    colorWarning: '#faad14',
    colorError: '#ff4d4f',
    colorInfo: '#1890ff',
    borderRadius: 8,
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
  },
  components: {
    Button: {
      borderRadius: 8,
      controlHeight: 40,
    },
    Input: {
      borderRadius: 8,
      controlHeight: 40,
    },
    Card: {
      borderRadius: 16,
    },
    Menu: {
      borderRadius: 8,
    },
  },
};

// Expose apiService globally before App component renders
window.apiService = apiService;

function App() {
  return (
    <ConfigProvider theme={themeConfig}>
      <AuthProvider>
        <div className="App">
          <ProtectedRoute requirePackageAccess={true}>
            <ModernLayout />
          </ProtectedRoute>
        </div>
      </AuthProvider>
    </ConfigProvider>
  );
}

export default App;
