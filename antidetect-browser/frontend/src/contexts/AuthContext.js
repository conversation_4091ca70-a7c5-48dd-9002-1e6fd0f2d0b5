/**
 * Authentication Context - Manages user authentication state
 * Provides centralized authentication management for the entire application
 * Ensures all components have access to authentication state and methods
 */

import React, { createContext, useContext, useState, useEffect } from 'react';
import { notification } from 'antd';

const AuthContext = createContext();

// Auto-login service URL (for Google OAuth)
const AUTO_LOGIN_SERVICE_URL = 'http://localhost:3000';

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [hasValidPackage, setHasValidPackage] = useState(false);
  const [authError, setAuthError] = useState(null);

  // Check authentication status on app load and set up periodic checks
  useEffect(() => {
    // Check if user is already authenticated from localStorage first
    const storedUser = localStorage.getItem('user');
    const storedAuth = localStorage.getItem('isAuthenticated');
    const storedToken = localStorage.getItem('access_token');

    if (storedUser && storedAuth === 'true') {
      console.log('🔍 [AUTH] Found stored authentication, restoring user session...');
      try {
        const userData = JSON.parse(storedUser);
        setUser(userData);
        setIsAuthenticated(true);
        // Admin users always have package access, others assume valid for OAuth users
        setHasValidPackage(userData.role === 'admin' || true);

        // Restore token in API service if available
        if (storedToken) {
          if (window.apiService) {
            window.apiService.setAuthToken(storedToken).then(() => {
              console.log('🔄 [AUTH] Restored auth token in API client:', storedToken.substring(0, 20) + '...');
            });
          } else {
            console.warn('⚠️ [AUTH] API service not ready, will retry token restoration...');
            // Retry token restoration after a short delay
            setTimeout(async () => {
              if (window.apiService) {
                await window.apiService.setAuthToken(storedToken);
                console.log('🔄 [AUTH] Restored auth token in API client (delayed):', storedToken.substring(0, 20) + '...');
              } else {
                console.error('❌ [AUTH] API service still not available after delay');
              }
            }, 1000);
          }
        }

        setLoading(false);
        return; // Skip server check if we have stored auth
      } catch (error) {
        console.error('❌ [AUTH] Failed to parse stored user data:', error);
      }
    }

    // Only check server if no stored auth found
    checkAuthStatus();

    // Set up periodic session check every 5 minutes
    const sessionCheckInterval = setInterval(() => {
      if (isAuthenticated) {
        console.log('🔍 [AUTH] Performing periodic session check...');
        checkAuthStatus();
      }
    }, 5 * 60 * 1000); // 5 minutes

    return () => {
      clearInterval(sessionCheckInterval);
    };
  }, [isAuthenticated]);

  const checkAuthStatus = async () => {
    try {
      setLoading(true);
      setAuthError(null);

      console.log('🔍 [AUTH] Checking Bearer token authentication status...');

      // Check if we have a stored access token
      const storedToken = localStorage.getItem('access_token');
      if (!storedToken) {
        console.log('❌ [AUTH] No stored access token found');
        clearAuthData();
        return;
      }

      console.log('🔍 [AUTH] Found stored token, verifying...');

      // Verify token with auto-login service using new endpoint
      const response = await fetch(`${AUTO_LOGIN_SERVICE_URL}/auth/token/verify`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ access_token: storedToken }),
      });

      if (response.ok) {
        const tokenData = await response.json();
        const userData = tokenData.user;
        console.log('✅ [AUTH] Token verified, user authenticated:', userData);

        // Set token in API service
        if (window.apiService) {
          window.apiService.setAuthToken(storedToken);
          console.log('✅ [AUTH] Token set in API service');
        }

        setUser(userData);
        setIsAuthenticated(true);
        localStorage.setItem('user', JSON.stringify(userData));
        localStorage.setItem('isAuthenticated', 'true');

        // Check package access
        await checkPackageAccess(userData.id);
      } else {
        console.log('❌ [AUTH] Token verification failed');
        clearAuthData();
      }
    } catch (error) {
      console.error('❌ [AUTH] Authentication check failed:', error);
      setAuthError('Failed to verify authentication status');
      clearAuthData();
    } finally {
      setLoading(false);
    }
  };

  const checkPackageAccess = async (userId) => {
    try {
      // Skip package check for admin users
      if (user && user.role === 'admin') {
        console.log('✅ [AUTH] Admin user detected, skipping package check');
        setHasValidPackage(true);
        return;
      }

      const storedToken = localStorage.getItem('access_token');
      if (!storedToken) {
        console.log('❌ [AUTH] No access token found for package access check');
        setHasValidPackage(false);
        return;
      }

      const response = await fetch(`${AUTO_LOGIN_SERVICE_URL}/users/${userId}/package-access`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${storedToken}`,
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ [AUTH] Package access check successful:', data);
        setHasValidPackage(data.has_bot_insta_access);
      } else {
        console.log('❌ [AUTH] Package access check failed:', response.status, response.statusText);
        setHasValidPackage(false);
      }
    } catch (error) {
      console.error('Package access check failed:', error);
      setHasValidPackage(false);
    }
  };

  const login = async (userData, accessToken = null) => {
    if (userData) {
      // Email/password login - set user data directly
      console.log('🔍 [AUTH] Setting user data from email login:', userData);
      setUser(userData);
      setIsAuthenticated(true);

      // Admin users always have package access, others check has_bot_insta_access
      const hasPackageAccess = userData.role === 'admin' || userData.has_bot_insta_access || true;
      setHasValidPackage(hasPackageAccess);

      // Store in localStorage
      localStorage.setItem('user', JSON.stringify(userData));
      localStorage.setItem('isAuthenticated', 'true');

      // Store access token if provided
      if (accessToken) {
        localStorage.setItem('access_token', accessToken);
        console.log('💾 [AUTH] Access token stored in localStorage:', accessToken.substring(0, 20) + '...');
        // Set token in API service
        if (window.apiService) {
          try {
            await window.apiService.setAuthToken(accessToken);
            console.log('✅ [AUTH] Token set in API service for email login');
          } catch (error) {
            console.error('❌ [AUTH] Failed to set token in API service:', error);
          }
        } else {
          console.error('❌ [AUTH] window.apiService not available when setting token');
        }
      } else {
        console.warn('⚠️ [AUTH] No access token provided to login function');
      }
    } else {
      // Google OAuth login
      console.log('🔍 [AUTH] Initiating Google OAuth login with Bearer tokens...');
      window.location.href = `${AUTO_LOGIN_SERVICE_URL}/auth/token/google`;
    }
  };

  const logout = async () => {
    try {
      console.log('🔍 [AUTH] Logging out user...');

      // Call logout endpoint to clear server-side session
      await fetch(`${AUTO_LOGIN_SERVICE_URL}/auth/logout`, {
        method: 'POST',
        credentials: 'include',
      });

      console.log('✅ [AUTH] Server logout successful');
    } catch (error) {
      console.error('❌ [AUTH] Logout request failed:', error);
    } finally {
      clearAuthData();

      // Show logout notification
      notification.info({
        message: 'Logged Out',
        description: 'You have been successfully logged out.',
        duration: 3,
        placement: 'topRight'
      });
    }
  };

  const clearAuthData = () => {
    console.log('🔍 [AUTH] Clearing authentication data');
    setUser(null);
    setIsAuthenticated(false);
    setHasValidPackage(false);
    setAuthError(null);
    localStorage.removeItem('user');
    localStorage.removeItem('isAuthenticated');
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');

    // Clear token from API service
    if (window.apiService) {
      window.apiService.setAuthToken(null);
    }

    // Clear any cached Google OAuth session
    console.log('🔍 [AUTH] Clearing Google OAuth cache...');
  };

  const handleOAuthCallback = async (accessToken, refreshToken) => {
    try {
      console.log('🔍 [AUTH] Processing OAuth callback with tokens...');

      if (!accessToken) {
        throw new Error('No access token provided');
      }

      // Store tokens
      localStorage.setItem('access_token', accessToken);
      if (refreshToken) {
        localStorage.setItem('refresh_token', refreshToken);
      }

      // Set token in API service
      if (window.apiService) {
        window.apiService.setAuthToken(accessToken);
        console.log('✅ [AUTH] Token set in API service');
      }

      // Verify token and get user data
      const response = await fetch(`${AUTO_LOGIN_SERVICE_URL}/auth/token/verify`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ access_token: accessToken }),
      });

      if (response.ok) {
        const tokenData = await response.json();
        const userData = tokenData.user;
        console.log('✅ [AUTH] OAuth callback successful, user authenticated:', userData);

        setUser(userData);
        setIsAuthenticated(true);
        localStorage.setItem('user', JSON.stringify(userData));
        localStorage.setItem('isAuthenticated', 'true');

        // Check package access
        await checkPackageAccess(userData.id);

        return true;
      } else {
        throw new Error('Token verification failed');
      }
    } catch (error) {
      console.error('❌ [AUTH] OAuth callback failed:', error);
      clearAuthData();
      return false;
    }
  };

  const pollForTokens = async () => {
    try {
      console.log('🔍 [AUTH] Polling for OAuth tokens...');

      const response = await fetch(`${AUTO_LOGIN_SERVICE_URL}/auth/token/retrieve`, {
        method: 'GET',
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.access_token) {
          console.log('✅ [AUTH] Found OAuth tokens from polling');
          return await handleOAuthCallback(result.access_token, result.refresh_token);
        }
      }

      return false;
    } catch (error) {
      console.error('❌ [AUTH] Token polling failed:', error);
      return false;
    }
  };

  const value = {
    user,
    isAuthenticated,
    loading,
    hasValidPackage,
    authError,
    login,
    logout,
    checkAuthStatus,
    checkPackageAccess: () => user ? checkPackageAccess(user.id) : Promise.resolve(),
    handleOAuthCallback,
    pollForTokens,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
