/**
 * Comprehensive Logging and Monitoring System
 * Provides structured logging, performance monitoring, and debugging capabilities
 */

/**
 * Log levels
 */
export const LOG_LEVELS = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
  CRITICAL: 4
};

/**
 * Log categories for better organization
 */
export const LOG_CATEGORIES = {
  API: 'api',
  BROWSER: 'browser',
  PROFILE: 'profile',
  DATA_CAPTURE: 'data_capture',
  VALIDATION: 'validation',
  ERROR: 'error',
  PERFORMANCE: 'performance',
  USER_ACTION: 'user_action',
  SYSTEM: 'system'
};

/**
 * Logger configuration
 */
const CONFIG = {
  minLevel: process.env.NODE_ENV === 'production' ? LOG_LEVELS.WARN : LOG_LEVELS.DEBUG,
  enableConsole: true,
  enableRemoteLogging: false, // Can be enabled for production
  maxLogEntries: 1000, // Maximum number of logs to keep in memory
  enablePerformanceMonitoring: true
};

/**
 * In-memory log storage for debugging
 */
let logHistory = [];

/**
 * Performance monitoring data
 */
let performanceData = {
  apiCalls: [],
  captureOperations: [],
  userActions: []
};

/**
 * Core Logger class
 */
export class Logger {
  constructor(context = '') {
    this.context = context;
  }

  /**
   * Create a structured log entry
   */
  createLogEntry(level, category, message, data = {}) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level: Object.keys(LOG_LEVELS)[level],
      category,
      context: this.context,
      message,
      data,
      sessionId: this.getSessionId(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    // Add to history
    this.addToHistory(logEntry);

    return logEntry;
  }

  /**
   * Add log entry to history with size management
   */
  addToHistory(logEntry) {
    logHistory.push(logEntry);
    
    // Keep only the most recent entries
    if (logHistory.length > CONFIG.maxLogEntries) {
      logHistory = logHistory.slice(-CONFIG.maxLogEntries);
    }
  }

  /**
   * Get or create session ID
   */
  getSessionId() {
    if (!window.sessionStorage.getItem('logger_session_id')) {
      window.sessionStorage.setItem('logger_session_id', 
        'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
      );
    }
    return window.sessionStorage.getItem('logger_session_id');
  }

  /**
   * Debug level logging
   */
  debug(category, message, data = {}) {
    this.log(LOG_LEVELS.DEBUG, category, message, data);
  }

  /**
   * Info level logging
   */
  info(category, message, data = {}) {
    this.log(LOG_LEVELS.INFO, category, message, data);
  }

  /**
   * Warning level logging
   */
  warn(category, message, data = {}) {
    this.log(LOG_LEVELS.WARN, category, message, data);
  }

  /**
   * Error level logging
   */
  error(category, message, data = {}) {
    this.log(LOG_LEVELS.ERROR, category, message, data);
  }

  /**
   * Critical level logging
   */
  critical(category, message, data = {}) {
    this.log(LOG_LEVELS.CRITICAL, category, message, data);
  }

  /**
   * Main logging method
   */
  log(level, category, message, data = {}) {
    if (level < CONFIG.minLevel) {
      return; // Skip if below minimum level
    }

    const logEntry = this.createLogEntry(level, category, message, data);

    // Console output
    if (CONFIG.enableConsole) {
      this.outputToConsole(logEntry);
    }

    // Remote logging (if enabled)
    if (CONFIG.enableRemoteLogging) {
      this.sendToRemote(logEntry);
    }

    return logEntry;
  }

  /**
   * Output log to console with formatting
   */
  outputToConsole(logEntry) {
    const { level, category, context, message, data, timestamp } = logEntry;
    const contextStr = context ? `[${context}]` : '';
    const categoryStr = `[${category.toUpperCase()}]`;
    const timeStr = new Date(timestamp).toLocaleTimeString();
    
    const logMessage = `${timeStr} ${contextStr}${categoryStr} ${message}`;

    switch (logEntry.level) {
      case 'DEBUG':
        console.debug(`🔍 ${logMessage}`, data);
        break;
      case 'INFO':
        console.info(`ℹ️ ${logMessage}`, data);
        break;
      case 'WARN':
        console.warn(`⚠️ ${logMessage}`, data);
        break;
      case 'ERROR':
        console.error(`❌ ${logMessage}`, data);
        break;
      case 'CRITICAL':
        console.error(`🚨 ${logMessage}`, data);
        break;
      default:
        console.log(`📝 ${logMessage}`, data);
    }
  }

  /**
   * Send log to remote service (placeholder)
   */
  sendToRemote(logEntry) {
    // In production, this would send logs to a service like LogRocket, Sentry, etc.
    // For now, we'll just store it locally
    try {
      const logs = JSON.parse(localStorage.getItem('app_logs') || '[]');
      logs.push(logEntry);
      
      // Keep only recent logs in localStorage
      const recentLogs = logs.slice(-100);
      localStorage.setItem('app_logs', JSON.stringify(recentLogs));
    } catch (error) {
      console.warn('Failed to store log in localStorage:', error);
    }
  }
}

/**
 * Performance Monitor
 */
export class PerformanceMonitor {
  static startTimer(operation, context = {}) {
    const timerId = `${operation}_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
    const startTime = performance.now();
    
    return {
      timerId,
      operation,
      context,
      startTime,
      end: () => this.endTimer(timerId, operation, context, startTime)
    };
  }

  static endTimer(timerId, operation, context, startTime) {
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    const performanceEntry = {
      timerId,
      operation,
      context,
      startTime,
      endTime,
      duration,
      timestamp: new Date().toISOString()
    };

    // Store performance data
    this.storePerformanceData(operation, performanceEntry);

    // Log if duration is significant
    if (duration > 1000) { // More than 1 second
      logger.warn(LOG_CATEGORIES.PERFORMANCE, 
        `Slow operation detected: ${operation} took ${duration.toFixed(2)}ms`, 
        performanceEntry
      );
    } else {
      logger.debug(LOG_CATEGORIES.PERFORMANCE, 
        `Operation completed: ${operation} took ${duration.toFixed(2)}ms`, 
        performanceEntry
      );
    }

    return performanceEntry;
  }

  static storePerformanceData(operation, entry) {
    if (!CONFIG.enablePerformanceMonitoring) return;

    switch (operation) {
      case 'api_call':
        performanceData.apiCalls.push(entry);
        break;
      case 'capture_browser_data':
      case 'save_captured_data':
        performanceData.captureOperations.push(entry);
        break;
      case 'user_action':
        performanceData.userActions.push(entry);
        break;
    }

    // Keep only recent entries
    Object.keys(performanceData).forEach(key => {
      if (performanceData[key].length > 100) {
        performanceData[key] = performanceData[key].slice(-100);
      }
    });
  }

  static getPerformanceStats() {
    const stats = {};
    
    Object.keys(performanceData).forEach(key => {
      const entries = performanceData[key];
      if (entries.length > 0) {
        const durations = entries.map(e => e.duration);
        stats[key] = {
          count: entries.length,
          avgDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
          minDuration: Math.min(...durations),
          maxDuration: Math.max(...durations),
          recentEntries: entries.slice(-10)
        };
      }
    });
    
    return stats;
  }
}

/**
 * Default logger instance
 */
export const logger = new Logger('App');

/**
 * Create logger with specific context
 */
export const createLogger = (context) => new Logger(context);

/**
 * Utility functions for common logging scenarios
 */
export const LogUtils = {
  /**
   * Log API call with timing
   */
  logApiCall: (method, url, data = {}) => {
    const timer = PerformanceMonitor.startTimer('api_call', { method, url });
    logger.info(LOG_CATEGORIES.API, `${method} ${url}`, { data });
    return timer;
  },

  /**
   * Log user action
   */
  logUserAction: (action, context = {}) => {
    const timer = PerformanceMonitor.startTimer('user_action', { action });
    logger.info(LOG_CATEGORIES.USER_ACTION, action, context);
    return timer;
  },

  /**
   * Log profile operation
   */
  logProfileOperation: (operation, profileId, data = {}) => {
    logger.info(LOG_CATEGORIES.PROFILE, `Profile ${operation}: ${profileId}`, data);
  },

  /**
   * Log browser operation
   */
  logBrowserOperation: (operation, data = {}) => {
    logger.info(LOG_CATEGORIES.BROWSER, `Browser ${operation}`, data);
  },

  /**
   * Log data capture operation
   */
  logDataCapture: (operation, profileId, data = {}) => {
    const timer = PerformanceMonitor.startTimer('capture_browser_data', { operation, profileId });
    logger.info(LOG_CATEGORIES.DATA_CAPTURE, `Data capture ${operation} for profile ${profileId}`, data);
    return timer;
  }
};

/**
 * Debug utilities
 */
export const DebugUtils = {
  /**
   * Get recent logs
   */
  getRecentLogs: (count = 50) => {
    return logHistory.slice(-count);
  },

  /**
   * Get logs by category
   */
  getLogsByCategory: (category, count = 50) => {
    return logHistory
      .filter(log => log.category === category)
      .slice(-count);
  },

  /**
   * Get error logs
   */
  getErrorLogs: (count = 20) => {
    return logHistory
      .filter(log => log.level === 'ERROR' || log.level === 'CRITICAL')
      .slice(-count);
  },

  /**
   * Export logs for debugging
   */
  exportLogs: () => {
    const exportData = {
      logs: logHistory,
      performance: performanceData,
      config: CONFIG,
      timestamp: new Date().toISOString(),
      sessionId: logger.getSessionId()
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `app-logs-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  },

  /**
   * Clear logs
   */
  clearLogs: () => {
    logHistory = [];
    performanceData = {
      apiCalls: [],
      captureOperations: [],
      userActions: []
    };
    localStorage.removeItem('app_logs');
    logger.info(LOG_CATEGORIES.SYSTEM, 'Logs cleared');
  }
};

// Make debug utilities available globally in development
if (process.env.NODE_ENV === 'development') {
  window.AppLogger = {
    logger,
    PerformanceMonitor,
    LogUtils,
    DebugUtils,
    LOG_CATEGORIES,
    LOG_LEVELS
  };
}
