/**
 * Profile Helper Utilities
 * Helper functions for working with profiles across different backends
 */

import { message } from 'antd';
import { apiService } from '../services/api';
import { logger, LogUtils, LOG_CATEGORIES } from './logger';
import { getAvailableProfiles } from './browserDataCapture';

/**
 * Get a working profile for testing
 */
export const getWorkingProfile = async () => {
  const timer = LogUtils.logUserAction('get_working_profile');
  
  try {
    logger.info(LOG_CATEGORIES.PROFILE, 'Finding a working profile for testing');
    
    // First, get available profiles from FastAPI (capture backend)
    const availableProfileIds = await getAvailableProfiles();
    
    if (availableProfileIds.length === 0) {
      throw new Error('No profiles available in capture backend');
    }
    
    // Use the first available profile ID
    const workingProfileId = availableProfileIds[0];
    
    // Create a mock profile object with the working ID
    const workingProfile = {
      id: workingProfileId,
      name: `Test Profile ${workingProfileId}`,
      account_id: workingProfileId, // Assuming account_id matches profile id
      status: 'active',
      created_at: new Date().toISOString()
    };
    
    logger.info(LOG_CATEGORIES.PROFILE, `Found working profile for testing: ${workingProfileId}`, {
      profile: workingProfile,
      availableIds: availableProfileIds
    });
    
    timer.end();
    return workingProfile;
    
  } catch (error) {
    logger.error(LOG_CATEGORIES.PROFILE, 'Failed to find working profile', {
      error: error.message
    });
    
    timer.end();
    throw error;
  }
};

/**
 * Test capture with working profile
 */
export const testCaptureWithWorkingProfile = async () => {
  const timer = LogUtils.logUserAction('test_capture_working_profile');
  
  try {
    logger.info(LOG_CATEGORIES.SYSTEM, 'Starting capture test with working profile');
    
    // Get a working profile
    const workingProfile = await getWorkingProfile();
    
    // Import capture function dynamically to avoid circular dependency
    const { captureBrowserData } = await import('./browserDataCapture');
    
    // Test capture
    const captureResult = await captureBrowserData(workingProfile, {
      showLoadingMessage: true,
      validateSync: false, // Skip sync validation
      retryOnError: true
    });
    
    if (captureResult.success) {
      logger.info(LOG_CATEGORIES.SYSTEM, 'Capture test successful with working profile', {
        profileId: workingProfile.id,
        dataSize: JSON.stringify(captureResult.data).length
      });
      
      message.success(`✅ Capture test successful with profile ${workingProfile.id}!`);
      return captureResult;
    } else {
      logger.error(LOG_CATEGORIES.SYSTEM, 'Capture test failed with working profile', {
        profileId: workingProfile.id,
        error: captureResult.message
      });
      
      message.error(`❌ Capture test failed: ${captureResult.message}`);
      return captureResult;
    }
    
  } catch (error) {
    logger.error(LOG_CATEGORIES.SYSTEM, 'Capture test with working profile failed', {
      error: error.message,
      stack: error.stack
    });
    
    message.error(`❌ Test failed: ${error.message}`);
    throw error;
  } finally {
    timer.end();
  }
};

/**
 * Test save with working profile
 */
export const testSaveWithWorkingProfile = async () => {
  const timer = LogUtils.logUserAction('test_save_working_profile');
  
  try {
    logger.info(LOG_CATEGORIES.SYSTEM, 'Starting save test with working profile');
    
    // Get a working profile
    const workingProfile = await getWorkingProfile();
    
    // Create mock data
    const mockData = {
      localStorage: {
        items: {
          'test_key_1': 'test_value_1',
          'test_key_2': 'test_value_2'
        },
        rawItems: [],
        length: 2,
        keys: ['test_key_1', 'test_key_2'],
        totalSize: 32
      },
      cookies: [
        {
          name: 'test_cookie',
          value: 'test_value',
          domain: 'test.com',
          path: '/'
        }
      ],
      indexedDB: {
        databases: [],
        data: {},
        totalSize: 0
      },
      history: {
        navigation: {},
        performance: {},
        referrer: '',
        visitedDomains: ['test.com'],
        sessionHistory: {}
      },
      metadata: {
        captured_at: new Date().toISOString(),
        browser: 'test'
      }
    };
    
    // Import save function dynamically to avoid circular dependency
    const { saveCapturedData } = await import('./browserDataCapture');
    
    // Test save
    const saveResult = await saveCapturedData(workingProfile.account_id, mockData, {
      showLoadingMessage: true
    });
    
    if (saveResult.success) {
      logger.info(LOG_CATEGORIES.SYSTEM, 'Save test successful with working profile', {
        profileId: workingProfile.id,
        accountId: workingProfile.account_id
      });
      
      message.success(`✅ Save test successful with profile ${workingProfile.id}!`);
      return saveResult;
    } else {
      logger.error(LOG_CATEGORIES.SYSTEM, 'Save test failed with working profile', {
        profileId: workingProfile.id,
        error: saveResult.message
      });
      
      message.error(`❌ Save test failed: ${saveResult.message}`);
      return saveResult;
    }
    
  } catch (error) {
    logger.error(LOG_CATEGORIES.SYSTEM, 'Save test with working profile failed', {
      error: error.message,
      stack: error.stack
    });
    
    message.error(`❌ Test failed: ${error.message}`);
    throw error;
  } finally {
    timer.end();
  }
};

/**
 * Run complete test flow with working profile
 */
export const runCompleteTestWithWorkingProfile = async () => {
  const timer = LogUtils.logUserAction('complete_test_working_profile');
  
  try {
    logger.info(LOG_CATEGORIES.SYSTEM, 'Starting complete test flow with working profile');
    message.info('🧪 Starting complete test with working profile...');
    
    // Step 1: Get working profile
    const workingProfile = await getWorkingProfile();
    message.info(`📋 Using working profile: ${workingProfile.id}`);
    
    // Step 2: Test capture
    message.info('🔍 Testing browser data capture...');
    const captureResult = await testCaptureWithWorkingProfile();
    
    // Step 3: Test save (regardless of capture result)
    message.info('💾 Testing data save...');
    const saveResult = await testSaveWithWorkingProfile();
    
    // Summary
    const results = {
      workingProfile,
      captureResult,
      saveResult,
      summary: {
        captureSuccess: captureResult.success,
        saveSuccess: saveResult.success,
        overallSuccess: captureResult.success && saveResult.success
      }
    };
    
    if (results.summary.overallSuccess) {
      logger.info(LOG_CATEGORIES.SYSTEM, 'Complete test flow successful', results.summary);
      message.success('✅ Complete test flow successful!');
    } else {
      logger.warn(LOG_CATEGORIES.SYSTEM, 'Complete test flow partially successful', results.summary);
      message.warning('⚠️ Test flow completed with some issues. Check logs for details.');
    }
    
    return results;
    
  } catch (error) {
    logger.error(LOG_CATEGORIES.SYSTEM, 'Complete test flow failed', {
      error: error.message,
      stack: error.stack
    });
    
    message.error(`❌ Complete test flow failed: ${error.message}`);
    throw error;
  } finally {
    timer.end();
  }
};

// Make helper functions available globally in development
if (process.env.NODE_ENV === 'development') {
  window.ProfileHelper = {
    getWorkingProfile,
    testCaptureWithWorkingProfile,
    testSaveWithWorkingProfile,
    runCompleteTestWithWorkingProfile
  };
}
