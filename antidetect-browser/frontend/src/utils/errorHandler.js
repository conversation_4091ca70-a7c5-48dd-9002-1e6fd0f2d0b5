/**
 * Centralized Error Handling and Validation Utilities
 * Provides consistent error handling, validation, and user feedback across the application
 */

import { message } from 'antd';

/**
 * Error types for categorization
 */
export const ERROR_TYPES = {
  VALIDATION: 'validation',
  NETWORK: 'network',
  AUTHENTICATION: 'authentication',
  AUTHORIZATION: 'authorization',
  NOT_FOUND: 'not_found',
  SERVER: 'server',
  BROWSER: 'browser',
  PROFILE_SYNC: 'profile_sync',
  DATA_INTEGRITY: 'data_integrity',
  UNKNOWN: 'unknown'
};

/**
 * Error severity levels
 */
export const ERROR_SEVERITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

/**
 * Enhanced error class with additional context
 */
export class AppError extends Error {
  constructor(message, type = ERROR_TYPES.UNKNOWN, severity = ERROR_SEVERITY.MEDIUM, context = {}) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.severity = severity;
    this.context = context;
    this.timestamp = new Date().toISOString();
  }
}

/**
 * Profile validation utilities
 */
export const ProfileValidator = {
  /**
   * Validate profile object for capture operations
   */
  validateForCapture: (profile) => {
    const errors = [];
    
    if (!profile) {
      errors.push('Profile object is required');
      return { isValid: false, errors };
    }
    
    if (!profile.id) {
      errors.push('Profile ID is required');
    }
    
    if (!profile.account_id) {
      errors.push('Profile must have an associated account');
    }
    
    if (profile.status && profile.status !== 'active') {
      errors.push(`Profile status is '${profile.status}', expected 'active'`);
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  },

  /**
   * Validate profile sync between backends
   */
  validateSync: (nestjsProfile, fastApiProfile) => {
    const issues = [];
    
    if (!nestjsProfile && !fastApiProfile) {
      issues.push('Profile not found in either backend');
      return { isSynced: false, issues };
    }
    
    if (!nestjsProfile) {
      issues.push('Profile missing from NestJS backend');
    }
    
    if (!fastApiProfile) {
      issues.push('Profile missing from FastAPI backend');
    }
    
    if (nestjsProfile && fastApiProfile) {
      if (nestjsProfile.id !== fastApiProfile.id) {
        issues.push('Profile IDs do not match between backends');
      }
      
      if (nestjsProfile.name !== fastApiProfile.name) {
        issues.push('Profile names do not match between backends');
      }
    }
    
    return {
      isSynced: issues.length === 0,
      issues
    };
  }
};

/**
 * Browser state validation utilities
 */
export const BrowserValidator = {
  /**
   * Validate browser state before capture
   */
  validateBrowserState: async (profileId) => {
    const issues = [];
    
    try {
      // This would need to be implemented based on your browser management system
      // For now, we'll do basic checks
      
      if (!profileId) {
        issues.push('Profile ID is required for browser validation');
        return { isValid: false, issues };
      }
      
      // Add more browser-specific validations here
      // e.g., check if browser is running, accessible, etc.
      
      return {
        isValid: issues.length === 0,
        issues
      };
    } catch (error) {
      issues.push(`Browser validation failed: ${error.message}`);
      return { isValid: false, issues };
    }
  }
};

/**
 * Data integrity validation utilities
 */
export const DataValidator = {
  /**
   * Validate captured browser data integrity
   */
  validateCapturedData: (data) => {
    const issues = [];
    
    if (!data) {
      issues.push('No data provided for validation');
      return { isValid: false, issues };
    }
    
    // Validate localStorage
    if (data.localStorage) {
      if (typeof data.localStorage !== 'object') {
        issues.push('localStorage must be an object');
      } else if (data.localStorage.items && typeof data.localStorage.items !== 'object') {
        issues.push('localStorage.items must be an object');
      }
    }
    
    // Validate cookies
    if (data.cookies) {
      if (!Array.isArray(data.cookies)) {
        issues.push('cookies must be an array');
      } else {
        data.cookies.forEach((cookie, index) => {
          if (!cookie.name || !cookie.value) {
            issues.push(`Cookie at index ${index} missing name or value`);
          }
        });
      }
    }
    
    // Validate indexedDB
    if (data.indexedDB) {
      if (typeof data.indexedDB !== 'object') {
        issues.push('indexedDB must be an object');
      } else if (data.indexedDB.databases && !Array.isArray(data.indexedDB.databases)) {
        issues.push('indexedDB.databases must be an array');
      }
    }
    
    // Validate history
    if (data.history) {
      if (typeof data.history !== 'object') {
        issues.push('history must be an object');
      }
    }
    
    return {
      isValid: issues.length === 0,
      issues,
      dataSize: JSON.stringify(data).length
    };
  }
};

/**
 * Centralized error handler
 */
export class ErrorHandler {
  static logError(error, context = {}) {
    const errorInfo = {
      message: error.message,
      type: error.type || ERROR_TYPES.UNKNOWN,
      severity: error.severity || ERROR_SEVERITY.MEDIUM,
      context,
      timestamp: new Date().toISOString(),
      stack: error.stack
    };
    
    console.error('🚨 Application Error:', errorInfo);
    
    // In production, you might want to send this to an error tracking service
    // e.g., Sentry, LogRocket, etc.
    
    return errorInfo;
  }
  
  static handleError(error, options = {}) {
    const {
      showUserMessage = true,
      logError = true,
      context = {}
    } = options;
    
    if (logError) {
      this.logError(error, context);
    }
    
    if (showUserMessage) {
      this.showUserMessage(error);
    }
    
    return error;
  }
  
  static showUserMessage(error) {
    const userMessage = this.getUserFriendlyMessage(error);
    
    switch (error.severity) {
      case ERROR_SEVERITY.CRITICAL:
        message.error(userMessage, 10); // Show for 10 seconds
        break;
      case ERROR_SEVERITY.HIGH:
        message.error(userMessage, 5);
        break;
      case ERROR_SEVERITY.MEDIUM:
        message.warning(userMessage, 3);
        break;
      case ERROR_SEVERITY.LOW:
        message.info(userMessage, 2);
        break;
      default:
        message.error(userMessage);
    }
  }
  
  static getUserFriendlyMessage(error) {
    switch (error.type) {
      case ERROR_TYPES.VALIDATION:
        return `Validation Error: ${error.message}`;
      case ERROR_TYPES.NETWORK:
        return 'Network connection issue. Please check your internet connection.';
      case ERROR_TYPES.AUTHENTICATION:
        return 'Authentication failed. Please log in again.';
      case ERROR_TYPES.AUTHORIZATION:
        return 'You do not have permission to perform this action.';
      case ERROR_TYPES.NOT_FOUND:
        return 'The requested resource was not found.';
      case ERROR_TYPES.SERVER:
        return 'Server error occurred. Please try again later.';
      case ERROR_TYPES.BROWSER:
        return 'Browser operation failed. Please ensure the browser is running.';
      case ERROR_TYPES.PROFILE_SYNC:
        return 'Profile synchronization issue detected. Please contact support.';
      case ERROR_TYPES.DATA_INTEGRITY:
        return 'Data integrity check failed. The captured data may be corrupted.';
      default:
        return error.message || 'An unexpected error occurred.';
    }
  }
  
  static createError(message, type, severity, context) {
    return new AppError(message, type, severity, context);
  }
}

/**
 * Validation helper functions
 */
export const validateAndHandle = (validator, data, errorType = ERROR_TYPES.VALIDATION) => {
  const result = validator(data);
  
  if (!result.isValid) {
    const error = ErrorHandler.createError(
      result.errors.join(', '),
      errorType,
      ERROR_SEVERITY.MEDIUM,
      { validationResult: result }
    );
    
    throw error;
  }
  
  return result;
};

/**
 * Async operation wrapper with error handling
 */
export const withErrorHandling = async (operation, context = {}) => {
  try {
    return await operation();
  } catch (error) {
    if (!(error instanceof AppError)) {
      // Convert regular errors to AppError
      const appError = new AppError(
        error.message,
        ERROR_TYPES.UNKNOWN,
        ERROR_SEVERITY.MEDIUM,
        { originalError: error, ...context }
      );
      
      ErrorHandler.handleError(appError, { context });
      throw appError;
    }
    
    ErrorHandler.handleError(error, { context });
    throw error;
  }
};
