/**
 * API Response Handler Utilities
 * Standardizes API response format and error handling across the application
 */

/**
 * Standard API Response Format
 * @typedef {Object} StandardResponse
 * @property {boolean} success - Whether the operation was successful
 * @property {*} data - The response data (null if error)
 * @property {string} message - Human-readable message
 * @property {string} error - Error details (null if success)
 * @property {string} timestamp - ISO timestamp of the response
 * @property {number} statusCode - HTTP status code
 */

/**
 * Create a standardized API response
 * @param {boolean} success - Success status
 * @param {*} data - Response data
 * @param {string} message - Response message
 * @param {string|Error} error - Error details
 * @param {number} statusCode - HTTP status code
 * @returns {StandardResponse}
 */
export const createStandardResponse = (success, data = null, message = '', error = null, statusCode = 200) => ({
  success,
  data,
  message,
  error: error ? (error instanceof Error ? error.message : error) : null,
  timestamp: new Date().toISOString(),
  statusCode
});

/**
 * Normalize various API response formats to standard format
 * @param {*} response - Raw API response
 * @param {number} statusCode - HTTP status code
 * @returns {StandardResponse}
 */
export const normalizeApiResponse = (response, statusCode = 200) => {
  // Handle null/undefined responses
  if (!response) {
    return createStandardResponse(false, null, 'Empty response', 'No data received', statusCode);
  }

  // If response is already in standard format
  if (typeof response === 'object' && 'success' in response && 'timestamp' in response) {
    return response;
  }

  // Handle axios response wrapper
  if (response.data !== undefined) {
    return normalizeApiResponse(response.data, response.status || statusCode);
  }

  // Handle different response patterns
  if (typeof response === 'object') {
    // Pattern 1: { success: boolean, data: any, message?: string }
    if ('success' in response) {
      return createStandardResponse(
        response.success,
        response.data || response.result || null,
        response.message || (response.success ? 'Operation successful' : 'Operation failed'),
        response.error || (!response.success ? response.message : null),
        statusCode
      );
    }

    // Pattern 2: { data: any, message?: string } (assume success if no error)
    if ('data' in response) {
      return createStandardResponse(
        true,
        response.data,
        response.message || 'Data retrieved successfully',
        null,
        statusCode
      );
    }

    // Pattern 3: { error: string } or { message: string } (error response)
    if ('error' in response || ('message' in response && statusCode >= 400)) {
      return createStandardResponse(
        false,
        null,
        response.message || 'Operation failed',
        response.error || response.message,
        statusCode
      );
    }

    // Pattern 4: Direct data object (assume success)
    return createStandardResponse(
      true,
      response,
      'Data retrieved successfully',
      null,
      statusCode
    );
  }

  // Handle primitive responses
  return createStandardResponse(
    true,
    response,
    'Operation completed',
    null,
    statusCode
  );
};

/**
 * Handle API errors and normalize them to standard format
 * @param {Error} error - Error object from API call
 * @returns {StandardResponse}
 */
export const handleApiError = (error) => {
  console.error('API Error:', error);

  // Handle axios errors
  if (error.response) {
    // Server responded with error status
    const { status, data } = error.response;
    
    // Check for "success wrapped in error" pattern (legacy issue)
    if (status === 200 && data && (data.success === true || data.success === undefined)) {
      console.warn('Success response wrapped in error - normalizing...');
      return normalizeApiResponse(data, 200);
    }

    // Handle error response data
    if (data) {
      return createStandardResponse(
        false,
        null,
        data.message || data.detail || data.error || `HTTP ${status} Error`,
        data.error || data.detail || data.message,
        status
      );
    }

    return createStandardResponse(
      false,
      null,
      `HTTP ${status} Error`,
      `Server responded with status ${status}`,
      status
    );
  }

  if (error.request) {
    // Request was made but no response received
    return createStandardResponse(
      false,
      null,
      'Network Error',
      'No response from server. Please check your connection.',
      0
    );
  }

  // Something else happened
  return createStandardResponse(
    false,
    null,
    'Unknown Error',
    error.message || 'An unexpected error occurred',
    500
  );
};

/**
 * Wrapper for API calls that automatically handles response normalization
 * @param {Function} apiCall - Function that returns a Promise with API response
 * @returns {Promise<StandardResponse>}
 */
export const callApiWithStandardResponse = async (apiCall) => {
  try {
    const response = await apiCall();
    return normalizeApiResponse(response);
  } catch (error) {
    return handleApiError(error);
  }
};

/**
 * Validate that a response is in standard format
 * @param {*} response - Response to validate
 * @returns {boolean}
 */
export const isStandardResponse = (response) => {
  return (
    response &&
    typeof response === 'object' &&
    'success' in response &&
    'data' in response &&
    'message' in response &&
    'error' in response &&
    'timestamp' in response &&
    'statusCode' in response
  );
};

/**
 * Extract data from standard response with error handling
 * @param {StandardResponse} response - Standard response object
 * @param {*} defaultValue - Default value if extraction fails
 * @returns {*}
 */
export const extractDataFromResponse = (response, defaultValue = null) => {
  if (!isStandardResponse(response)) {
    console.warn('Response is not in standard format:', response);
    return defaultValue;
  }

  if (!response.success) {
    console.warn('Response indicates failure:', response.error);
    return defaultValue;
  }

  return response.data !== null ? response.data : defaultValue;
};

/**
 * Check if response indicates success
 * @param {StandardResponse} response - Standard response object
 * @returns {boolean}
 */
export const isSuccessResponse = (response) => {
  return isStandardResponse(response) && response.success === true;
};

/**
 * Get error message from response
 * @param {StandardResponse} response - Standard response object
 * @param {string} defaultMessage - Default error message
 * @returns {string}
 */
export const getErrorMessage = (response, defaultMessage = 'Unknown error') => {
  if (!isStandardResponse(response)) {
    return defaultMessage;
  }

  return response.error || response.message || defaultMessage;
};
