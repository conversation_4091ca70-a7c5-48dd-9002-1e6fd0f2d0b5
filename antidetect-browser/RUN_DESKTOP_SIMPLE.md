# Simple Desktop App Runner

This document describes how to use the simplified desktop application runner scripts that work with your existing Docker setup.

## Overview

The simple runner scripts (`start-desktop-simple.sh` and `start-desktop-simple.bat`) provide an easy way to start the Facebook Automation desktop application using your existing PostgreSQL and Redis containers from the `auto-login/docker-compose.yml` setup.

## Prerequisites

1. **Docker** must be running
2. **PostgreSQL and Redis** containers from `auto-login/docker-compose.yml` should be available
3. **Node.js** and **npm** installed for the frontend
4. **Python 3.9+** installed for the backend

## Quick Start

### Linux/macOS

```bash
# Make sure you're in the project root
cd /path/to/antidetect-browser

# Run the simple desktop app
./scripts/start-desktop-simple.sh
```

### Windows

```cmd
# Make sure you're in the project root
cd C:\path\to\antidetect-browser

# Run the simple desktop app
scripts\start-desktop-simple.bat
```

## What the Script Does

1. **Checks Docker Services**: Verifies <PERSON><PERSON> is running and starts PostgreSQL/Redis if needed
2. **Quick Backend Setup**:
   - Creates Python virtual environment if needed
   - Installs minimal required packages (FastAPI, Uvicorn, etc.)
   - Creates a simple `.env` configuration
   - Uses `main_simple.py` for a lightweight backend server
3. **Quick Frontend Setup**:
   - Installs npm dependencies if needed
   - **Builds React frontend** for production (webpack build)
   - Uses simple authentication bypass for desktop app
4. **Starts Services**:
   - Launches the backend server on port 8000
   - Launches the Electron desktop application
5. **Shows Status**: Displays running services and useful URLs

## Features

- **Minimal Setup**: Only installs essential packages to get running quickly
- **Frontend Build**: Automatically builds React frontend for production
- **Simple Authentication**: Bypasses complex auth for desktop development
- **Error Handling**: Gracefully handles package installation failures
- **Service Detection**: Automatically detects if services are already running
- **Clean Shutdown**: Properly cleans up processes when stopped

## Service URLs

When running, you can access:

- **Desktop Application**: Electron window opens automatically
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health
- **PostgreSQL**: localhost:5432 (from Docker)
- **Redis**: localhost:6379 (from Docker)

## Logs

Backend logs are saved to:
- Linux/macOS: `backend/logs/backend.log`
- Windows: `backend\logs\backend.log`

## Troubleshooting

### Backend Won't Start

1. Check if port 8000 is already in use:
   ```bash
   # Linux/macOS
   lsof -i :8000
   
   # Windows
   netstat -an | findstr :8000
   ```

2. Check backend logs:
   ```bash
   # Linux/macOS
   tail -f backend/logs/backend.log
   
   # Windows
   type backend\logs\backend.log
   ```

### Database Connection Issues

1. Verify Docker containers are running:
   ```bash
   cd auto-login
   docker-compose ps
   ```

2. Test database connection:
   ```bash
   docker exec auto-login-postgres-1 psql -U postgres -d auto_login -c "SELECT 1;"
   ```

3. Test Redis connection:
   ```bash
   docker exec auto-login-redis-1 redis-cli -a redis_password ping
   ```

### Frontend Issues

1. Clear npm cache and reinstall:
   ```bash
   cd frontend
   rm -rf node_modules package-lock.json
   npm install
   ```

2. Check if Electron is properly installed:
   ```bash
   cd frontend
   npm list electron
   ```

## Differences from Full Setup

This simple version:

- ✅ **Faster startup**: Minimal package installation
- ✅ **Fewer dependencies**: Only essential packages
- ✅ **Simplified backend**: Uses `main_simple.py` without complex database initialization
- ✅ **Error resilient**: Continues even if some packages fail to install
- ❌ **Limited functionality**: Some advanced features may not work
- ❌ **No database migrations**: Assumes database is already set up
- ❌ **Basic error handling**: Less comprehensive error reporting

## Upgrading to Full Version

If you need full functionality, you can use the complete setup scripts:

```bash
# Linux/macOS
./scripts/run-desktop-app.sh

# Windows
scripts\run-desktop-app.bat
```

## Stopping the Application

To stop all services:

1. **In the terminal**: Press `Ctrl+C`
2. **Close Electron window**: Click the X button
3. **Manual cleanup** (if needed):
   ```bash
   # Kill backend process
   pkill -f "python main_simple.py"
   
   # Kill any remaining Python processes
   pkill python
   ```

## Configuration

The script creates a simple `.env` file in the backend directory with basic configuration. You can modify this file to customize:

- Database connection settings
- API configuration
- CORS origins
- Logging levels
- Profile storage paths

## Support

If you encounter issues:

1. Check the logs in `backend/logs/backend.log`
2. Verify Docker containers are healthy: `docker-compose ps`
3. Test individual components (database, Redis, backend API)
4. Try the full setup scripts if the simple version doesn't work
