#!/usr/bin/env python3
"""
Script to add type column to profiles table
"""

import sqlite3
import os

def add_type_column():
    """Add type column to profiles table"""
    
    # Try different possible database paths
    possible_paths = [
        'facebook_automation.db',
        'backend/facebook_automation.db',
        'backend/data/facebook_automation.db',
        'data/facebook_automation.db',
        '../backend/data/facebook_automation.db',
        './backend/data/facebook_automation.db'
    ]
    
    db_path = None
    for path in possible_paths:
        if os.path.exists(path):
            db_path = path
            break
    
    if not db_path:
        print("❌ Database not found in any of these locations:")
        for path in possible_paths:
            print(f"   - {path}")
        
        # List current directory contents
        print("\n📁 Current directory contents:")
        for item in os.listdir('.'):
            print(f"   - {item}")
        
        if os.path.exists('backend'):
            print("\n📁 Backend directory contents:")
            for item in os.listdir('backend'):
                print(f"   - backend/{item}")
                
            if os.path.exists('backend/data'):
                print("\n📁 Backend/data directory contents:")
                for item in os.listdir('backend/data'):
                    print(f"   - backend/data/{item}")
        
        return False
    
    print(f"📊 Found database at: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if type column exists
        cursor.execute('PRAGMA table_info(profiles)')
        columns = [column[1] for column in cursor.fetchall()]
        print(f"📋 Current columns: {columns}")
        
        if 'type' not in columns:
            print('🔧 Adding type column to profiles table...')
            cursor.execute('ALTER TABLE profiles ADD COLUMN type VARCHAR(50) DEFAULT "facebook" NOT NULL')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_profiles_type ON profiles (type)')
            conn.commit()
            print('✅ Type column added successfully')
            
            # Verify the column was added
            cursor.execute('PRAGMA table_info(profiles)')
            new_columns = [column[1] for column in cursor.fetchall()]
            print(f"📋 New columns: {new_columns}")
            
            # Check existing profiles
            cursor.execute('SELECT id, name, type FROM profiles LIMIT 5')
            profiles = cursor.fetchall()
            if profiles:
                print(f"📊 Sample profiles with type:")
                for profile in profiles:
                    print(f"   - ID: {profile[0]}, Name: {profile[1]}, Type: {profile[2]}")
            else:
                print("📊 No existing profiles found")
                
        else:
            print('✅ Type column already exists')
            
            # Show sample data
            cursor.execute('SELECT id, name, type FROM profiles LIMIT 5')
            profiles = cursor.fetchall()
            if profiles:
                print(f"📊 Sample profiles:")
                for profile in profiles:
                    print(f"   - ID: {profile[0]}, Name: {profile[1]}, Type: {profile[2]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Adding type column to profiles table...")
    success = add_type_column()
    if success:
        print("🎉 Operation completed successfully!")
    else:
        print("💥 Operation failed!")
