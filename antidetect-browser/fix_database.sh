#!/bin/bash

# Database fix script - restore data and test system

echo "🔧 Database Fix Script"
echo "====================="

# Check if backup exists
if [ ! -f "backend/database_dump.sql" ]; then
    echo "❌ No database dump found. Creating fresh database..."
else
    echo "📦 Found database dump. Restoring data..."
    
    # Stop backend if running
    echo "🛑 Stopping backend..."
    lsof -ti:8000 | xargs kill -9 2>/dev/null || true
    
    # Remove current database
    rm -f backend/facebook_automation.db
    
    # Create new database from dump
    echo "🗄️ Creating new database from dump..."
    sqlite3 backend/facebook_automation.db < backend/database_dump.sql
    
    if [ $? -eq 0 ]; then
        echo "✅ Database restored successfully"
    else
        echo "⚠️ Database restore had warnings (this is normal)"
    fi
fi

# Start backend
echo "🚀 Starting backend..."
cd backend
source venv/bin/activate
python main.py &
BACKEND_PID=$!

# Wait for backend to start
echo "⏳ Waiting for backend to start..."
sleep 5

# Test database
echo "🧪 Testing database..."
RESPONSE=$(curl -s "http://localhost:8000/api/profiles/test-db-fix")
echo "📊 Database test response:"
echo "$RESPONSE" | jq .

# Check if test was successful
SUCCESS=$(echo "$RESPONSE" | jq -r '.success // false')
if [ "$SUCCESS" = "true" ]; then
    echo "✅ Database test successful!"
    
    # Show profile count
    PROFILE_COUNT=$(echo "$RESPONSE" | jq -r '.profiles_count // 0')
    echo "📊 Found $PROFILE_COUNT profiles in database"
    
else
    echo "❌ Database test failed"
    echo "Response: $RESPONSE"
fi

echo ""
echo "🎉 Database fix completed!"
echo "Backend is running on http://localhost:8000"
echo "Backend PID: $BACKEND_PID"
echo ""
echo "To stop backend: kill $BACKEND_PID"
