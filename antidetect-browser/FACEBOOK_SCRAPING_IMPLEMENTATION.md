# Facebook Comment Scraping Implementation

## Overview
Đã triển khai hệ thống scraping Facebook comments với human-like behaviors và anti-detection measures theo yêu cầu.

## Features Implemented

### 1. Human-like Scrolling Engine

#### **Natural Scroll Patterns:**
- **Variable scroll distance**: 200-800px mỗi lần, không đều
- **Scroll velocity curves**: B<PERSON><PERSON> đ<PERSON> n<PERSON> → chậm dần → dừng
- **Reading pauses**: Dừng 2-5 gi<PERSON>y đ<PERSON> "đọc" comments
- **Micro-movements**: Scroll nhỏ lên xuống như điều chỉnh vị trí đọc
- **Random intervals**: 1-4 giây giữa các lần scroll

#### **Implementation Details:**
```python
async def _perform_natural_scroll(self, page, scroll_count: int):
    # Variable scroll distance (200-800px)
    scroll_distance = random.randint(200, 800)
    
    # Scroll velocity simulation: fast → medium → slow
    scroll_steps = random.randint(3, 6)
    step_distance = scroll_distance // scroll_steps
    
    for i in range(scroll_steps):
        # Velocity curve: start fast, end slow
        if i < scroll_steps // 2:
            delay = random.uniform(0.05, 0.1)  # Fast
        else:
            delay = random.uniform(0.15, 0.3)  # Slow
```

### 2. Advanced Human-like Behaviors

#### **Mouse Movements:**
- **Natural curve patterns**: Bezier curve simulation cho mouse movement
- **Reading behavior simulation**: Eye movement patterns khi đọc
- **Casual interactions**: Hover over buttons, links occasionally

#### **Viewport Adjustments:**
- **Scroll up/down patterns**: Thỉnh thoảng scroll lên rồi xuống tiếp
- **Window focus simulation**: Focus vào comment elements
- **Break patterns**: Sau 10-15 comments, dừng lâu hơn (5-10 giây)

### 3. Data Extraction System

#### **XPath Selectors Used:**
```javascript
// Comment container
[role="article"][aria-label*="Bình luận"]

// User name
.x193iq5w.xeuugli.x13faqbe.x1vvkbs.xlh3980.xvmahel.x1n0sxbx.x1lliihq.x1s928wv.xhkezso.x1gmr53x.x1cpjm7i.x1fgarty.x1943h6x.x4zkp8e.x676frb.x1nxh6w3.x1sibtaa.xo1l8bm.xzsf02u

// Profile link
a[href*="/groups/"][href*="/user/"], a[href*="/user/"], a[href*="/profile.php"]

// Comment text
.x193iq5w.xeuugli.x13faqbe.x1vvkbs[dir="auto"]

// Profile picture
image[xlink:href*="scontent"], img[src*="scontent"]
```

#### **UID Extraction:**
```python
def _extract_user_id_from_url(self, url: str) -> str:
    patterns = [
        r'/groups/\d+/user/(\d+)',  # Groups: /groups/591054007361950/user/100001277281956/
        r'/user/(\d+)',             # Direct user format
        r'profile\.php\?id=(\d+)',  # Profile.php format
        r'/people/[^/]+/(\d+)',     # People format
        r'/(\d+)/?$'                # Direct ID at end
    ]
```

### 4. Anti-Detection Measures

#### **Rate Limiting Detection:**
- Monitor for "Try again later", "Temporarily blocked" messages
- Detect unusually slow page loading
- Auto-cooldown với 1-3 phút pause khi detect rate limiting

#### **Advanced Anti-Detection:**
- **Mouse movement curves**: Natural Bezier curves thay vì straight lines
- **Reading simulation**: Eye movement patterns khi đọc comments
- **Interaction simulation**: Hover over reactions, buttons occasionally
- **Session breaks**: Random breaks 30-60s sau mỗi 50-100 comments
- **Final behaviors**: Random scroll, check other page parts trước khi kết thúc

### 5. Progressive Data Extraction

#### **Batch Processing:**
- Extract data sau mỗi scroll session
- Avoid duplicates bằng UID tracking
- Progressive saving để tránh mất dữ liệu

#### **Termination Conditions:**
- **Max results reached**: Đạt số lượng comments mong muốn
- **No new content**: Không có comment mới sau 3 lần scroll liên tiếp
- **Rate limiting detected**: Auto-pause và retry
- **Timeout**: Maximum time limit để tránh infinite loops

## Usage Example

```python
# Sử dụng enhanced scraping system
facebook_scraper = FacebookScraper()

# Scrape comments với human-like behavior
results = await facebook_scraper.scrape_facebook_post(
    crawler=crawler,
    post_url="https://facebook.com/groups/123/posts/456",
    scraping_types=[ScrapingType.COMMENTS],
    max_results=500
)

# Results sẽ chứa:
# - facebook_uid: 100001277281956
# - full_name: Tên user
# - profile_url: Link profile đầy đủ
# - interaction_content: Nội dung comment
# - profile_picture_url: Avatar URL
```

## Key Improvements

### **Compared to Previous System:**
1. **Human-like scrolling** thay vì simple "Load more" clicking
2. **Advanced mouse movements** với natural curves
3. **Rate limiting detection** và auto-recovery
4. **Progressive extraction** để handle large datasets
5. **Anti-detection measures** comprehensive
6. **Robust UID extraction** cho groups format

### **Performance Optimizations:**
- **Batch extraction** thay vì individual processing
- **Duplicate prevention** với UID tracking
- **Memory efficient** progressive processing
- **Error recovery** với fallback methods

## Configuration

### **Scroll Parameters:**
- Distance: 200-800px per scroll
- Velocity: 3-6 steps per scroll
- Reading pause: 2-5 seconds
- Break frequency: Every 10-15 scrolls
- Cooldown: 60-180 seconds when rate limited

### **Detection Thresholds:**
- No new content: 3 consecutive attempts
- Rate limit check: Every 5 scrolls
- Mouse interaction: 40% probability
- Viewport adjustment: 30% probability

## Testing

### **Manual Testing:**
1. Start backend với enhanced scraper
2. Create scraping task với Facebook post URL
3. Monitor logs cho human-like behaviors
4. Verify extracted data quality

### **Expected Behavior:**
- Natural scrolling patterns visible in browser
- Mouse movements over comments
- Appropriate pauses và breaks
- Successful UID extraction từ groups format
- No rate limiting triggers under normal usage

## Notes

- **Facebook structure changes**: XPath selectors có thể cần update khi Facebook thay đổi
- **Rate limiting**: System tự động detect và handle, nhưng nên monitor
- **Performance**: Slower than aggressive scraping nhưng safer và more reliable
- **Scalability**: Có thể scale với multiple profiles và proxy rotation
