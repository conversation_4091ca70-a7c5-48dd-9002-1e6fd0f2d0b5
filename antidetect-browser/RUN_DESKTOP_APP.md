# 🚀 Chạy Desktop App - Hướng dẫn nhanh

Script đơn giản để chạy ứng dụng desktop Facebook Automation sử dụng PostgreSQL và Redis đã có từ docker-compose.

## 📋 Yêu cầu

- **Docker** đang chạy
- **PostgreSQL và Redis** từ `auto-login/docker-compose.yml` đã được setup
- **Node.js** và **Python 3** đã cài đặt

## 🎯 Cách sử dụng

### Linux/macOS:
```bash
./scripts/run-desktop-app.sh
```

### Windows:
```cmd
scripts\run-desktop-app.bat
```

## ⚡ Script sẽ tự động:

1. **Kiểm tra Docker** đang chạy
2. **Khởi động PostgreSQL và Redis** từ docker-compose nếu chưa chạy
3. **Setup Python virtual environment** cho backend
4. **Cài đặt dependencies** (Python + Node.js)
5. **Tạo file cấu hình** (.env) với database connection đúng
6. **Khởi động backend FastAPI** server
7. **Mở ứng dụng Electron** desktop

## 🔧 Cấu hình tự động

Script sẽ tạo file `.env` cho backend với cấu hình:

```env
# Database (sử dụng docker-compose postgres)
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/auto_login

# Redis (sử dụng docker-compose redis)  
REDIS_URL=redis://:redis123@localhost:6379/0

# API
API_PORT=8000
```

## 🌐 Services sau khi chạy

- **PostgreSQL**: `localhost:5432` (từ docker-compose)
- **Redis**: `localhost:6379` (từ docker-compose)
- **Backend API**: `http://localhost:8000`
- **API Docs**: `http://localhost:8000/docs`
- **Desktop App**: Electron window

## 🛑 Dừng ứng dụng

Nhấn `Ctrl+C` trong terminal để dừng tất cả services.

## 📁 Logs

- **Backend**: `backend/logs/backend.log`
- **Docker**: `docker-compose logs postgres redis`

## 🐛 Troubleshooting

### Lỗi thường gặp:

1. **Docker không chạy**:
   ```bash
   # Khởi động Docker Desktop
   ```

2. **Port 8000 đã được sử dụng**:
   ```bash
   # Kiểm tra process đang dùng port
   lsof -i :8000
   # Kill process nếu cần
   kill -9 <PID>
   ```

3. **Database connection lỗi**:
   ```bash
   # Kiểm tra docker-compose
   cd auto-login
   docker-compose ps
   docker-compose logs postgres
   ```

4. **Python dependencies lỗi**:
   ```bash
   # Xóa và tạo lại virtual environment
   rm -rf backend/venv
   # Chạy lại script
   ```

## 💡 Lưu ý

- Script sử dụng database `auto_login` từ docker-compose hiện có
- Không cần setup database riêng
- Tự động tạo các thư mục cần thiết
- Tự động cleanup khi thoát

---

**Chỉ cần chạy 1 lệnh và ứng dụng desktop sẽ hoạt động!** 🎉
