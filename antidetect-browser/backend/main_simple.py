#!/usr/bin/env python3
"""
Simple FastAPI server for testing
"""

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# Create FastAPI app
app = FastAPI(
    title="Facebook Automation Backend - Simple",
    description="Simple version for testing",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:8080", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Facebook Automation Backend - Simple Version", "status": "running"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "message": "Backend is running"}

@app.get("/api/health")
async def api_health_check():
    """API health check endpoint"""
    return {"status": "healthy", "message": "API is running"}

# Mock API endpoints for frontend compatibility
@app.get("/api/profiles/")
async def get_profiles():
    """Get profiles - mock data"""
    return [
        {
            "id": 1,
            "name": "Demo Profile",
            "status": "active",
            "proxy": "127.0.0.1:8080",
            "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "created_at": "2024-01-01T00:00:00Z",
            "type": "camoufox",
            "browser_version": "120.0",
            "os": "Windows",
            "screen_resolution": "1920x1080",
            "timezone": "UTC+7",
            "language": "en-US",
            "cookies_count": 15,
            "last_used": "2024-01-01T00:00:00Z",
            "proxy_config": {
                "type": "http",
                "host": "127.0.0.1",
                "port": 8080,
                "username": "",
                "password": ""
            }
        }
    ]

@app.get("/profiles/my-profiles")
async def get_my_profiles():
    """Get my profiles - mock data (alternative endpoint)"""
    return [
        {
            "id": 1,
            "name": "Demo Profile",
            "status": "active",
            "proxy": "127.0.0.1:8080",
            "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "created_at": "2024-01-01T00:00:00Z",
            "type": "camoufox",
            "browser_version": "120.0",
            "os": "Windows",
            "screen_resolution": "1920x1080",
            "timezone": "UTC+7",
            "language": "en-US",
            "cookies_count": 15,
            "last_used": "2024-01-01T00:00:00Z",
            "proxy_config": {
                "type": "http",
                "host": "127.0.0.1",
                "port": 8080,
                "username": "",
                "password": ""
            }
        }
    ]

@app.get("/api/scraping/")
async def get_scraping_tasks():
    """Get scraping tasks - mock data"""
    return [
        {
            "id": 1,
            "name": "Demo Scraping Task",
            "status": "completed",
            "post_url": "https://facebook.com/demo/posts/123",
            "scraped_count": 50,
            "created_at": "2024-01-01T00:00:00Z"
        }
    ]

@app.get("/api/messaging/")
async def get_messaging_tasks():
    """Get messaging tasks - mock data"""
    return [
        {
            "id": 1,
            "name": "Demo Messaging Task",
            "status": "completed",
            "sent_count": 25,
            "total_count": 30,
            "success_rate": 83.3,
            "created_at": "2024-01-01T00:00:00Z"
        }
    ]

@app.get("/api/system/stats")
async def get_system_stats():
    """Get system statistics - mock data"""
    return {
        "stats": {
            "total_profiles": 1,
            "active_profiles": 1,
            "running_tasks": 0,
            "total_scraped_users": 50,
            "total_messages_sent": 25,
            "success_rate": 83.3
        },
        "recent_activities": [
            {
                "id": 1,
                "type": "scraping",
                "description": "Completed scraping task for demo post",
                "timestamp": "2024-01-01T00:00:00Z",
                "status": "success"
            },
            {
                "id": 2,
                "type": "messaging",
                "description": "Sent 25 messages successfully",
                "timestamp": "2024-01-01T00:00:00Z",
                "status": "success"
            }
        ]
    }

# Additional endpoints that might be called by frontend
@app.post("/api/profiles/")
async def create_profile():
    """Create profile - mock response"""
    return {"id": 2, "name": "New Profile", "status": "created"}

@app.put("/api/profiles/{profile_id}")
async def update_profile(profile_id: int):
    """Update profile - mock response"""
    return {"id": profile_id, "name": "Updated Profile", "status": "updated"}

@app.delete("/api/profiles/{profile_id}")
async def delete_profile(profile_id: int):
    """Delete profile - mock response"""
    return {"message": f"Profile {profile_id} deleted successfully"}

@app.post("/api/profiles/{profile_id}/login")
async def profile_login(profile_id: int):
    """Profile login - mock response"""
    return {"message": f"Profile {profile_id} login initiated", "status": "success"}

@app.post("/api/scraping/")
async def create_scraping_task():
    """Create scraping task - mock response"""
    return {"id": 2, "name": "New Scraping Task", "status": "created"}

@app.post("/api/messaging/")
async def create_messaging_task():
    """Create messaging task - mock response"""
    return {"id": 2, "name": "New Messaging Task", "status": "created"}

@app.get("/api/messaging/{task_id}/results")
async def get_messaging_results(task_id: int):
    """Get messaging results - mock response"""
    return {
        "results": [
            {
                "id": 1,
                "full_name": "John Doe",
                "profile_url": "https://facebook.com/john.doe",
                "status": "sent",
                "message": "Hello John!",
                "sent_at": "2024-01-01T00:00:00Z"
            }
        ],
        "total": 1
    }

@app.get("/api/scraping/exports/history")
async def get_scraping_exports_history():
    """Get scraping exports history - mock response"""
    return [
        {
            "id": 1,
            "filename": "facebook_users_2024-01-01.xlsx",
            "created_at": "2024-01-01T00:00:00Z",
            "file_size": "2.5 MB",
            "record_count": 150,
            "status": "completed"
        }
    ]

if __name__ == "__main__":
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )
