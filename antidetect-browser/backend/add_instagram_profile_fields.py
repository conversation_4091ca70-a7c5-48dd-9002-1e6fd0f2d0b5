#!/usr/bin/env python3
"""
Add Instagram fields to profiles table
"""

import sqlite3
from pathlib import Path

def add_instagram_fields():
    """Add Instagram login fields to profiles table"""
    
    # Get database path
    backend_dir = Path(__file__).parent
    db_path = backend_dir / "facebook_automation.db"
    
    print(f"📊 Using database: {db_path}")
    
    # Connect to database
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    try:
        print("🚀 Adding Instagram fields to profiles table...")
        
        # Check if columns already exist
        cursor.execute("PRAGMA table_info(profiles)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        # Add instagram_logged_in column if it doesn't exist
        if 'instagram_logged_in' not in column_names:
            cursor.execute("ALTER TABLE profiles ADD COLUMN instagram_logged_in BOOLEAN DEFAULT FALSE")
            print("✅ Added instagram_logged_in column")
        else:
            print("ℹ️ instagram_logged_in column already exists")
        
        # Add instagram_username column if it doesn't exist
        if 'instagram_username' not in column_names:
            cursor.execute("ALTER TABLE profiles ADD COLUMN instagram_username VARCHAR(255)")
            print("✅ Added instagram_username column")
        else:
            print("ℹ️ instagram_username column already exists")
        
        # Commit changes
        conn.commit()
        
        # Verify columns were added
        cursor.execute("PRAGMA table_info(profiles)")
        columns = cursor.fetchall()
        instagram_columns = [col for col in columns if 'instagram' in col[1]]
        
        print(f"📊 Instagram columns in profiles table: {[col[1] for col in instagram_columns]}")
        
        print("✅ Instagram profile fields migration completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        conn.rollback()
        return False
        
    finally:
        conn.close()

def main():
    """Main function"""
    print("=" * 60)
    print("Add Instagram Profile Fields Migration")
    print("=" * 60)
    
    # Check if database exists
    backend_dir = Path(__file__).parent
    db_path = backend_dir / "facebook_automation.db"
    
    if not db_path.exists():
        print(f"❌ Database not found: {db_path}")
        print("Please make sure the backend has been initialized first.")
        return 1
    
    # Run migration
    success = add_instagram_fields()
    
    if success:
        print("\n✅ Instagram profile fields migration completed successfully!")
        print("\nYou can now:")
        print("1. Restart the backend server")
        print("2. Create Instagram profiles with login status")
        return 0
    else:
        print("\n❌ Migration failed!")
        return 1

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
