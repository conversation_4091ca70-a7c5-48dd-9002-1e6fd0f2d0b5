-- Migration: Add Not Found Statistics Support
-- Date: 2025-08-09
-- Purpose: Add support for tracking "not found" users in Instagram DM statistics

-- Add not_found column to main statistics table
ALTER TABLE instagram_dm_statistics ADD COLUMN messages_not_found INTEGER DEFAULT 0;

-- Add not_found column to profile statistics table
ALTER TABLE instagram_dm_profile_statistics ADD COLUMN messages_not_found INTEGER DEFAULT 0;

-- Update existing records to have 0 for not_found (for backward compatibility)
UPDATE instagram_dm_statistics SET messages_not_found = 0 WHERE messages_not_found IS NULL;
UPDATE instagram_dm_profile_statistics SET messages_not_found = 0 WHERE messages_not_found IS NULL;

-- Verify the changes
SELECT name FROM sqlite_master WHERE type='table' AND name IN ('instagram_dm_statistics', 'instagram_dm_profile_statistics');

-- Show updated table schemas
.schema instagram_dm_statistics
.schema instagram_dm_profile_statistics
