-- Migration: Add Instagram Direct Message Statistics Table
-- Date: 2025-08-04
-- Purpose: Store completed Instagram DM task statistics for persistent display

-- Create instagram_dm_statistics table (similar to bulk_messaging_statistics)
CREATE TABLE IF NOT EXISTS instagram_dm_statistics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id VARCHAR(255) UNIQUE NOT NULL,
    task_name VARCHAR(255) NOT NULL,
    sender_profile_id INTEGER NOT NULL,
    status VARCHAR(50) NOT NULL,
    total_recipients INTEGER NOT NULL,
    messages_sent INTEGER NOT NULL,
    messages_failed INTEGER NOT NULL,
    messages_skipped INTEGER DEFAULT 0,
    success_rate FLOAT NOT NULL,
    total_time_seconds FLOAT NOT NULL,
    total_time_formatted VARCHAR(50),
    average_time_per_message FLOAT NOT NULL,
    browser_closed BOOLEAN DEFAULT TRUE,
    error_message TEXT,
    created_at DATETIME NOT NULL,
    started_at DATETIME,
    completed_at DATETIME,
    detailed_results JSON,
    excel_file_path VARCHAR(500),
    message_content TEXT,
    delay_between_messages_min INTEGER,
    delay_between_messages_max INTEGER,
    stop_on_consecutive_failures INTEGER,
    skip_sent_recipients BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (sender_profile_id) REFERENCES profiles (id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS ix_instagram_dm_statistics_id ON instagram_dm_statistics (id);
CREATE UNIQUE INDEX IF NOT EXISTS ix_instagram_dm_statistics_task_id ON instagram_dm_statistics (task_id);
CREATE INDEX IF NOT EXISTS ix_instagram_dm_statistics_profile_id ON instagram_dm_statistics (sender_profile_id);
CREATE INDEX IF NOT EXISTS ix_instagram_dm_statistics_created_at ON instagram_dm_statistics (created_at);
CREATE INDEX IF NOT EXISTS ix_instagram_dm_statistics_status ON instagram_dm_statistics (status);

-- Verify table was created
SELECT name FROM sqlite_master WHERE type='table' AND name = 'instagram_dm_statistics';

-- Show table schema
.schema instagram_dm_statistics
