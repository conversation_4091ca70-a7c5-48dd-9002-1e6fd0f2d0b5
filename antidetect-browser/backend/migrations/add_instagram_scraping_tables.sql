-- Migration: Add Instagram Scraping Tables
-- Created: 2024-01-01
-- Description: Add tables for Instagram scraping functionality

-- Create instagram_scraping_tasks table
CREATE TABLE IF NOT EXISTS instagram_scraping_tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id VARCHAR(255) UNIQUE NOT NULL,
    profile_id INTEGER NOT NULL,
    target_url VARCHAR(1000) NOT NULL,
    scraping_type VARCHAR(50) NOT NULL,
    max_results INTEGER DEFAULT 1000,
    status VARCHAR(50) DEFAULT 'pending',
    progress REAL DEFAULT 0.0,
    total_found INTEGER DEFAULT 0,
    total_scraped INTEGER DEFAULT 0,
    results_file_path VARCHAR(500),
    error_message TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    started_at DATETIME,
    completed_at DATETIME,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOR<PERSON><PERSON><PERSON> KEY (profile_id) REFERENCES profiles (id)
);

-- Create index on task_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_instagram_scraping_tasks_task_id ON instagram_scraping_tasks(task_id);

-- Create index on profile_id for faster profile-based queries
CREATE INDEX IF NOT EXISTS idx_instagram_scraping_tasks_profile_id ON instagram_scraping_tasks(profile_id);

-- Create index on status for faster status-based queries
CREATE INDEX IF NOT EXISTS idx_instagram_scraping_tasks_status ON instagram_scraping_tasks(status);

-- Create instagram_scraped_users table
CREATE TABLE IF NOT EXISTS instagram_scraped_users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id VARCHAR(255) NOT NULL,
    username VARCHAR(255) NOT NULL,
    profile_name VARCHAR(500),
    profile_url VARCHAR(1000),
    user_id VARCHAR(255),
    follower_count INTEGER,
    following_count INTEGER,
    post_count INTEGER,
    is_verified VARCHAR(10),
    is_private VARCHAR(10),
    bio TEXT,
    scraped_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES instagram_scraping_tasks (task_id)
);

-- Create index on task_id for faster task-based queries
CREATE INDEX IF NOT EXISTS idx_instagram_scraped_users_task_id ON instagram_scraped_users(task_id);

-- Create index on username for faster username-based queries
CREATE INDEX IF NOT EXISTS idx_instagram_scraped_users_username ON instagram_scraped_users(username);

-- Create composite index for deduplication
CREATE INDEX IF NOT EXISTS idx_instagram_scraped_users_task_username ON instagram_scraped_users(task_id, username);

-- Create instagram_scraping_exports table
CREATE TABLE IF NOT EXISTS instagram_scraping_exports (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id VARCHAR(255) NOT NULL,
    filename VARCHAR(500) NOT NULL,
    file_path VARCHAR(1000) NOT NULL,
    file_size INTEGER,
    format VARCHAR(50) DEFAULT 'excel',
    record_count INTEGER DEFAULT 0,
    status VARCHAR(50) DEFAULT 'completed',
    error_message TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES instagram_scraping_tasks (task_id)
);

-- Create index on task_id for faster task-based queries
CREATE INDEX IF NOT EXISTS idx_instagram_scraping_exports_task_id ON instagram_scraping_exports(task_id);

-- Create index on created_at for faster date-based queries
CREATE INDEX IF NOT EXISTS idx_instagram_scraping_exports_created_at ON instagram_scraping_exports(created_at);

-- Insert sample data for testing (optional)
-- INSERT INTO instagram_scraping_tasks (
--     task_id, profile_id, target_url, scraping_type, status, progress, total_found, total_scraped
-- ) VALUES (
--     'instagram_demo_001', 1, 'https://www.instagram.com/demo_user/', 'following', 'completed', 100.0, 50, 50
-- );

-- INSERT INTO instagram_scraped_users (
--     task_id, username, profile_name, profile_url
-- ) VALUES 
--     ('instagram_demo_001', 'user1', 'User One', 'https://www.instagram.com/user1/'),
--     ('instagram_demo_001', 'user2', 'User Two', 'https://www.instagram.com/user2/'),
--     ('instagram_demo_001', 'user3', 'User Three', 'https://www.instagram.com/user3/');

-- Verify tables were created
SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'instagram_%';

-- Show table schemas
.schema instagram_scraping_tasks
.schema instagram_scraped_users  
.schema instagram_scraping_exports
