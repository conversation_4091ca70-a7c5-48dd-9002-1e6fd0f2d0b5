-- Migration: Add Multi-Profile Support for Instagram Direct Message
-- Date: 2025-08-07
-- Purpose: Add support for multiple profiles in Instagram DM tasks

-- Add new columns to instagram_dm_statistics table for multi-profile support
ALTER TABLE instagram_dm_statistics ADD COLUMN sender_profile_ids JSON;
ALTER TABLE instagram_dm_statistics ADD COLUMN users_per_profile INTEGER DEFAULT 40;
ALTER TABLE instagram_dm_statistics ADD COLUMN allow_duplicate_users BOOLEAN DEFAULT FALSE;

-- Create new table for per-profile statistics
CREATE TABLE IF NOT EXISTS instagram_dm_profile_statistics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id VARCHAR(255) NOT NULL,
    profile_id INTEGER NOT NULL,
    profile_name VARCHAR(255),
    assigned_users INTEGER NOT NULL,
    messages_sent INTEGER NOT NULL,
    messages_failed INTEGER NOT NULL,
    success_rate FLOAT NOT NULL,
    start_time DATETIME,
    end_time DATETIME,
    duration_seconds FLOAT,
    error_message TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    <PERSON>OR<PERSON><PERSON>N KEY (task_id) REFERENCES instagram_dm_statistics (task_id),
    FOREIGN KEY (profile_id) REFERENCES profiles (id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_instagram_dm_profile_stats_task_id ON instagram_dm_profile_statistics(task_id);
CREATE INDEX IF NOT EXISTS idx_instagram_dm_profile_stats_profile_id ON instagram_dm_profile_statistics(profile_id);
CREATE INDEX IF NOT EXISTS idx_instagram_dm_profile_stats_created_at ON instagram_dm_profile_statistics(created_at);

-- Update existing records to have default values for new columns
UPDATE instagram_dm_statistics 
SET 
    sender_profile_ids = JSON_ARRAY(sender_profile_id),
    users_per_profile = 40,
    allow_duplicate_users = FALSE
WHERE sender_profile_ids IS NULL;
