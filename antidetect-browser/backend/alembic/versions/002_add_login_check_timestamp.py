"""Add last_login_check timestamp to profiles

Revision ID: 002
Revises: 001
Create Date: 2024-01-01 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '002'
down_revision = '001'
branch_labels = None
depends_on = None


def upgrade():
    # Add last_login_check column to profiles table
    op.add_column('profiles', sa.Column('last_login_check', sa.DateTime(), nullable=True))


def downgrade():
    # Remove last_login_check column from profiles table
    op.drop_column('profiles', 'last_login_check')
