"""Add bulk messaging task statistics table

Revision ID: 5848e4a0670c
Revises: 
Create Date: 2025-07-13 15:07:53.078436

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '5848e4a0670c'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('bulk_messaging_statistics',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('task_id', sa.String(), nullable=False),
    sa.Column('task_name', sa.String(), nullable=False),
    sa.Column('sender_profile_id', sa.Integer(), nullable=False),
    sa.Column('status', sa.String(), nullable=False),
    sa.Column('total_recipients', sa.Integer(), nullable=False),
    sa.Column('messages_sent', sa.Integer(), nullable=False),
    sa.Column('messages_failed', sa.Integer(), nullable=False),
    sa.Column('messages_skipped', sa.Integer(), nullable=False),
    sa.Column('success_rate', sa.Float(), nullable=False),
    sa.Column('total_time_seconds', sa.Float(), nullable=False),
    sa.Column('total_time_formatted', sa.String(), nullable=True),
    sa.Column('average_time_per_message', sa.Float(), nullable=False),
    sa.Column('browser_closed', sa.Boolean(), nullable=False),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('started_at', sa.DateTime(), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.Column('detailed_results', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['sender_profile_id'], ['profiles.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_bulk_messaging_statistics_id'), 'bulk_messaging_statistics', ['id'], unique=False)
    op.create_index(op.f('ix_bulk_messaging_statistics_task_id'), 'bulk_messaging_statistics', ['task_id'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_bulk_messaging_statistics_task_id'), table_name='bulk_messaging_statistics')
    op.drop_index(op.f('ix_bulk_messaging_statistics_id'), table_name='bulk_messaging_statistics')
    op.drop_table('bulk_messaging_statistics')
    # ### end Alembic commands ###
