#!/usr/bin/env python3
"""
Test Runner for Profile Sharing System
Runs comprehensive test suite with reporting and coverage
"""

import os
import sys
import subprocess
import argparse
import json
from datetime import datetime
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


class TestRunner:
    """Comprehensive test runner for profile sharing system"""
    
    def __init__(self):
        self.project_root = project_root
        self.test_dir = self.project_root / "tests"
        self.reports_dir = self.project_root / "test_reports"
        self.reports_dir.mkdir(exist_ok=True)
        
    def run_unit_tests(self, verbose=False):
        """Run unit tests"""
        print("🧪 Running Unit Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir),
            "-m", "unit or not (integration or e2e)",
            "--tb=short",
            f"--junitxml={self.reports_dir}/unit_tests.xml",
            f"--html={self.reports_dir}/unit_tests.html",
            "--self-contained-html"
        ]
        
        if verbose:
            cmd.append("-v")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        print(f"Unit Tests Result: {'✅ PASSED' if result.returncode == 0 else '❌ FAILED'}")
        if result.returncode != 0:
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
        
        return result.returncode == 0
    
    def run_integration_tests(self, verbose=False):
        """Run integration tests"""
        print("🔗 Running Integration Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir),
            "-m", "integration",
            "--tb=short",
            f"--junitxml={self.reports_dir}/integration_tests.xml",
            f"--html={self.reports_dir}/integration_tests.html",
            "--self-contained-html"
        ]
        
        if verbose:
            cmd.append("-v")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        print(f"Integration Tests Result: {'✅ PASSED' if result.returncode == 0 else '❌ FAILED'}")
        if result.returncode != 0:
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
        
        return result.returncode == 0
    
    def run_security_tests(self, verbose=False):
        """Run security-focused tests"""
        print("🔒 Running Security Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir),
            "-m", "security",
            "--tb=short",
            f"--junitxml={self.reports_dir}/security_tests.xml",
            f"--html={self.reports_dir}/security_tests.html",
            "--self-contained-html"
        ]
        
        if verbose:
            cmd.append("-v")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        print(f"Security Tests Result: {'✅ PASSED' if result.returncode == 0 else '❌ FAILED'}")
        if result.returncode != 0:
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
        
        return result.returncode == 0
    
    def run_performance_tests(self, verbose=False):
        """Run performance tests"""
        print("⚡ Running Performance Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir),
            "-m", "performance",
            "--tb=short",
            f"--junitxml={self.reports_dir}/performance_tests.xml",
            f"--html={self.reports_dir}/performance_tests.html",
            "--self-contained-html"
        ]
        
        if verbose:
            cmd.append("-v")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        print(f"Performance Tests Result: {'✅ PASSED' if result.returncode == 0 else '❌ FAILED'}")
        if result.returncode != 0:
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
        
        return result.returncode == 0
    
    def run_all_tests(self, verbose=False):
        """Run all tests"""
        print("🚀 Running All Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir),
            "--tb=short",
            f"--junitxml={self.reports_dir}/all_tests.xml",
            f"--html={self.reports_dir}/all_tests.html",
            "--self-contained-html"
        ]
        
        if verbose:
            cmd.append("-v")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        print(f"All Tests Result: {'✅ PASSED' if result.returncode == 0 else '❌ FAILED'}")
        if result.returncode != 0:
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
        
        return result.returncode == 0
    
    def run_with_coverage(self, verbose=False):
        """Run tests with coverage reporting"""
        print("📊 Running Tests with Coverage...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir),
            "--cov=app",
            "--cov-report=html:" + str(self.reports_dir / "coverage_html"),
            "--cov-report=xml:" + str(self.reports_dir / "coverage.xml"),
            "--cov-report=term-missing",
            f"--junitxml={self.reports_dir}/coverage_tests.xml",
            f"--html={self.reports_dir}/coverage_tests.html",
            "--self-contained-html"
        ]
        
        if verbose:
            cmd.append("-v")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        print(f"Coverage Tests Result: {'✅ PASSED' if result.returncode == 0 else '❌ FAILED'}")
        print("Coverage report generated in:", self.reports_dir / "coverage_html")
        
        if result.returncode != 0:
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
        
        return result.returncode == 0
    
    def run_specific_test(self, test_path, verbose=False):
        """Run a specific test file or test function"""
        print(f"🎯 Running Specific Test: {test_path}")
        
        cmd = [
            "python", "-m", "pytest",
            test_path,
            "--tb=short"
        ]
        
        if verbose:
            cmd.append("-v")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        print(f"Specific Test Result: {'✅ PASSED' if result.returncode == 0 else '❌ FAILED'}")
        if result.returncode != 0:
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
        
        return result.returncode == 0
    
    def generate_test_report(self):
        """Generate comprehensive test report"""
        print("📋 Generating Test Report...")
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "test_results": {},
            "summary": {
                "total_passed": 0,
                "total_failed": 0,
                "total_tests": 0
            }
        }
        
        # Check for test result files
        test_files = [
            ("unit_tests.xml", "Unit Tests"),
            ("integration_tests.xml", "Integration Tests"),
            ("security_tests.xml", "Security Tests"),
            ("performance_tests.xml", "Performance Tests"),
            ("all_tests.xml", "All Tests")
        ]
        
        for filename, test_type in test_files:
            xml_path = self.reports_dir / filename
            if xml_path.exists():
                # Parse XML results (simplified)
                report["test_results"][test_type] = {
                    "report_file": str(xml_path),
                    "status": "completed"
                }
        
        # Save report
        report_path = self.reports_dir / "test_report.json"
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"Test report saved to: {report_path}")
        return report
    
    def check_dependencies(self):
        """Check if required test dependencies are installed"""
        print("🔍 Checking Test Dependencies...")
        
        required_packages = [
            "pytest",
            "pytest-asyncio",
            "pytest-cov",
            "pytest-html",
            "pytest-timeout"
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            print("❌ Missing required packages:")
            for package in missing_packages:
                print(f"  - {package}")
            print("\nInstall missing packages with:")
            print(f"pip install {' '.join(missing_packages)}")
            return False
        
        print("✅ All test dependencies are installed")
        return True
    
    def setup_test_environment(self):
        """Setup test environment"""
        print("🛠️  Setting up Test Environment...")
        
        # Create necessary directories
        self.reports_dir.mkdir(exist_ok=True)
        
        # Set environment variables for testing
        os.environ["TESTING"] = "true"
        os.environ["DATABASE_URL"] = "sqlite+aiosqlite:///./test.db"
        
        print("✅ Test environment setup complete")


def main():
    """Main test runner function"""
    parser = argparse.ArgumentParser(description="Profile Sharing System Test Runner")
    parser.add_argument("--type", choices=["unit", "integration", "security", "performance", "all", "coverage"], 
                       default="all", help="Type of tests to run")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--specific", help="Run specific test file or function")
    parser.add_argument("--check-deps", action="store_true", help="Check test dependencies")
    parser.add_argument("--setup-only", action="store_true", help="Only setup test environment")
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    # Check dependencies
    if args.check_deps or not runner.check_dependencies():
        return 1
    
    # Setup test environment
    runner.setup_test_environment()
    
    if args.setup_only:
        return 0
    
    # Run specific test
    if args.specific:
        success = runner.run_specific_test(args.specific, args.verbose)
        return 0 if success else 1
    
    # Run tests based on type
    success = True
    
    if args.type == "unit":
        success = runner.run_unit_tests(args.verbose)
    elif args.type == "integration":
        success = runner.run_integration_tests(args.verbose)
    elif args.type == "security":
        success = runner.run_security_tests(args.verbose)
    elif args.type == "performance":
        success = runner.run_performance_tests(args.verbose)
    elif args.type == "coverage":
        success = runner.run_with_coverage(args.verbose)
    elif args.type == "all":
        success = runner.run_all_tests(args.verbose)
    
    # Generate report
    runner.generate_test_report()
    
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
