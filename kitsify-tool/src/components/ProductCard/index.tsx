import type { USDTConversion } from '@/services/currency';
import type { Product, ProductDuration } from '@/services/products';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import React, { useEffect, useState } from 'react';
import { currencyService } from '@/services/currency';
import CryptomusModal from '../CryptomusModal';
import ProductDetailModal from '../ProductDetailModal';

type ProductCardProps = {
  product: Product;
  onAddToCart: (product: Product, quantity: number, selectedDuration: ProductDuration) => void;
};

const ProductCard: React.FC<ProductCardProps> = ({ product, onAddToCart }) => {
  const t = useTranslations('Shop');
  const [quantity, setQuantity] = useState(1);
  const [selectedDuration, setSelectedDuration] = useState<ProductDuration | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showCryptomusModal, setShowCryptomusModal] = useState(false);
  const [cryptomusUsdtConversion, setCryptomusUsdtConversion] = useState<USDTConversion | null>(null);

  const {
    name,
    image_url,
    features: _features,
    durations = [],
    is_hot = false,
  } = product;

  // Chọn duration mặc định (thường là gói ngắn nhất)
  useEffect(() => {
    if (durations && durations.length > 0) {
      // Sắp xếp theo thời gian tăng dần và chọn gói đầu tiên
      const sortedDurations = [...durations].sort((a, b) => a.duration_days - b.duration_days);
      setSelectedDuration(sortedDurations[0] || null);
    }
  }, [durations]);

  // Get payment method discounts if available
  const getPaymentMethodDiscount = (methodName: string) => {
    const discount = product.productDiscounts?.find(
      discount => discount.paymentMethod.name.toLowerCase() === methodName.toLowerCase(),
    );
    return discount?.discount_percent || 0;
  };

  const paypalDiscountPercent = getPaymentMethodDiscount('paypal');
  const hasPaypalDiscount = paypalDiscountPercent > 0;

  const cryptomusDiscountPercent = getPaymentMethodDiscount('cryptomus');
  const hasCryptomusDiscount = cryptomusDiscountPercent > 0;

  // Calculate prices
  const isOutOfStock = selectedDuration?.quantity ? selectedDuration.quantity <= 0 : false;
  const hasDiscount = selectedDuration && selectedDuration.discount_price > 0
    && selectedDuration.discount_price < selectedDuration.original_price;
  const displayPrice = hasDiscount && selectedDuration
    ? Number(selectedDuration.discount_price)
    : Number(selectedDuration?.original_price || 0);

  const cryptomusPrice = hasCryptomusDiscount
    ? displayPrice * (1 - cryptomusDiscountPercent / 100)
    : displayPrice;

  // Use pre-calculated USDT conversion from API or calculate for Cryptomus discount
  useEffect(() => {
    if (hasCryptomusDiscount && cryptomusPrice > 0) {
      // Check if we have pre-calculated USDT conversion from the API
      const durationWithUSDT = selectedDuration?.usdt_conversion;
      if (durationWithUSDT && !hasCryptomusDiscount) {
        // Use the pre-calculated conversion if no additional discount
        setCryptomusUsdtConversion(durationWithUSDT);
      } else {
        // Calculate USDT for discounted price
        const fetchUsdtConversion = async () => {
          try {
            const conversion = await currencyService.convertUSDToUSDT(cryptomusPrice);
            setCryptomusUsdtConversion(conversion);
          } catch (error) {
            console.error('Failed to fetch USDT conversion:', error);
            setCryptomusUsdtConversion(null);
          }
        };

        fetchUsdtConversion();
      }
    } else {
      setCryptomusUsdtConversion(null);
    }
  }, [hasCryptomusDiscount, cryptomusPrice, selectedDuration]);

  if (!selectedDuration) {
    return null;
  }

  const discountPercentage = hasDiscount
    ? Math.round(((Number(selectedDuration.original_price) - Number(selectedDuration.discount_price))
      / Number(selectedDuration.original_price)) * 100)
    : selectedDuration.discount_percent || 0;

  const handleIncrement = () => {
    if (quantity < selectedDuration.quantity) {
      setQuantity(prev => prev + 1);
    }
  };

  const handleDecrement = () => {
    if (quantity > 1) {
      setQuantity(prev => prev - 1);
    }
  };

  const handleAddToCart = () => {
    if (!isOutOfStock && selectedDuration) {
      onAddToCart(product, quantity, selectedDuration);
      setQuantity(1);
    }
  };

  const handleDurationChange = (durationId: number) => {
    const duration = durations.find(d => d.id === durationId);
    if (duration) {
      setSelectedDuration(duration);
      setQuantity(1);
    }
  };

  const handleViewDetail = () => {
    setShowDetailModal(true);
  };

  const handleCloseModal = () => {
    setShowDetailModal(false);
  };

  // const handlePayPalPayment = () => {
  //   // PayPal payment with payment_method_id = 1 (hardcoded as requested)
  //   if (!isOutOfStock && selectedDuration) {
  //     onAddToCart(product, quantity, selectedDuration);
  //     setQuantity(1);
  //   }
  // };

  const handleCryptomusPayment = () => {
    // Show Cryptomus modal
    setShowCryptomusModal(true);
  };

  const handleCloseCryptomusModal = () => {
    setShowCryptomusModal(false);
  };

  return (
    <>
      <div className="flex size-full flex-col overflow-hidden rounded-lg border border-gray-200 bg-white shadow-md transition-all duration-300 hover:shadow-lg">
        <div className="relative h-48 w-full shrink-0">
          {image_url
            ? (
                <Image
                  src={image_url}
                  alt={name}
                  fill
                  className="object-cover"
                />
              )
            : (
                <div className="flex size-full items-center justify-center bg-gray-200">
                  <span className="text-gray-400">No image</span>
                </div>
              )}
          {isOutOfStock && (
            <div className="absolute right-0 top-0 m-2 rounded bg-red-500 px-2 py-1 text-xs font-medium text-white">
              {t('soldOut') || 'Sold Out'}
            </div>
          )}
          {hasDiscount && (
            <div className="absolute left-0 top-0 m-2 rounded bg-green-500 px-2 py-1 text-xs font-medium text-white">
              -
              {discountPercentage}
              %
            </div>
          )}
          {is_hot && (
            <div className="absolute right-0 top-0 m-2 flex items-center rounded-full bg-gradient-to-r from-red-500 to-orange-500 px-3 py-1.5 shadow-lg">
              <svg
                className="mr-1 size-3 text-white"
                fill="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
              </svg>
              <span className="text-xs font-bold text-white">HOT</span>
            </div>
          )}
        </div>

        <div className="flex flex-1 flex-col p-4">
          {/* Header Section - Fixed height */}
          <div className="mb-3 shrink-0">
            <h3 className="mb-2 text-lg font-semibold text-gray-900">{name}</h3>
          </div>

          {/* Content Section - Flexible height */}
          <div className="flex-1">
            {/* Duration Selection - Danh sách thay vì dropdown */}
            {durations.length > 1 && (
              <div className="mb-4">
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  {t('duration') || 'Thời hạn'}
                  :
                </label>
                <div className="space-y-2">
                  {durations.map((duration) => {
                    const isSelected = selectedDuration?.id === duration.id;
                    const durationPrice = Number(duration.discount_price > 0 ? duration.discount_price : duration.original_price);
                    const durationHasDiscount = duration.discount_price > 0 && duration.discount_price < duration.original_price;

                    return (
                      <button
                        key={duration.id}
                        type="button"
                        onClick={() => handleDurationChange(duration.id)}
                        className={`w-full rounded-xl border-2 p-3 text-left text-sm transition-all duration-300 ${
                          isSelected
                            ? 'border-blue-500 bg-gradient-to-r from-blue-50 to-indigo-50 shadow-md ring-2 ring-blue-200'
                            : 'border-gray-200 bg-gradient-to-r from-white to-gray-50 hover:border-blue-300 hover:from-blue-50 hover:to-indigo-50 hover:shadow-md'
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <div className={`size-5 rounded-full border-2 transition-all duration-200 ${
                              isSelected
                                ? 'border-blue-500 bg-gradient-to-r from-blue-500 to-indigo-600 shadow-md'
                                : 'border-gray-300 bg-white hover:border-blue-300'
                            }`}
                            >
                              {isSelected && (
                                <div className="flex size-full items-center justify-center">
                                  <div className="size-2 rounded-full bg-white shadow-sm" />
                                </div>
                              )}
                            </div>
                            <span className="font-medium text-gray-900">
                              {duration.duration_days}
                              {' '}
                              {t('days') || 'ngày'}
                            </span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className={`font-bold ${durationHasDiscount ? 'text-red-600' : 'text-gray-900'}`}>
                              $
                              {durationPrice.toFixed(2)}
                            </span>
                            {durationHasDiscount && (
                              <span className="text-xs text-gray-500 line-through">
                                $
                                {Number(duration.original_price).toFixed(2)}
                              </span>
                            )}
                          </div>
                        </div>
                        {duration.quantity <= 5 && duration.quantity > 0 && (
                          <div className="mt-1 text-xs text-orange-600">
                            {t('onlyLeft') || 'Chỉ còn'}
                            {' '}
                            {duration.quantity}
                            {' '}
                            {t('remaining') || 'sản phẩm'}
                          </div>
                        )}
                      </button>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Price Display for single duration */}
            {durations.length === 1 && (
              <div className="mb-3">
                <div className="flex items-center gap-2">
                  <span className={`text-lg font-bold ${hasDiscount ? 'text-red-600' : 'text-gray-900'}`}>
                    $
                    {displayPrice.toFixed(2)}
                  </span>
                  {hasDiscount && (
                    <span className="text-sm text-gray-500 line-through">
                      $
                      {Number(selectedDuration.original_price).toFixed(2)}
                    </span>
                  )}
                </div>
                <div className="text-sm text-gray-600">
                  {selectedDuration.duration_days}
                  {' '}
                  {t('days') || 'ngày'}
                </div>
              </div>
            )}
          </div>

          {/* Footer Section - Fixed at bottom */}
          <div className="mt-auto shrink-0 space-y-2">
            {/* Quantity selector */}
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">
                {t('quantity') || 'Số lượng'}
                :
              </span>
              <div className="flex items-center rounded-lg border border-gray-300 bg-gradient-to-r from-gray-50 to-white shadow-sm">
                <button
                  type="button"
                  onClick={handleDecrement}
                  disabled={quantity <= 1 || isOutOfStock}
                  className={`flex size-8 items-center justify-center rounded-l-lg text-sm font-bold transition-all duration-200 ${
                    quantity <= 1 || isOutOfStock
                      ? 'cursor-not-allowed bg-gray-200 text-gray-400'
                      : 'bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700 hover:shadow-md active:scale-95'
                  }`}
                  aria-label="Decrease quantity"
                >
                  −
                </button>
                <div className="flex h-8 w-12 items-center justify-center bg-gradient-to-r from-blue-50 to-indigo-50 text-center text-sm font-semibold text-gray-800">
                  {quantity}
                </div>
                <button
                  type="button"
                  onClick={handleIncrement}
                  disabled={quantity >= selectedDuration.quantity || isOutOfStock}
                  className={`flex size-8 items-center justify-center rounded-r-lg text-sm font-bold transition-all duration-200 ${
                    quantity >= selectedDuration.quantity || isOutOfStock
                      ? 'cursor-not-allowed bg-gray-200 text-gray-400'
                      : 'bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700 hover:shadow-md active:scale-95'
                  }`}
                  aria-label="Increase quantity"
                >
                  +
                </button>
              </div>
            </div>

            {/* Fallback button when no payment methods are available */}
            {hasPaypalDiscount && (
              <button
                type="button"
                onClick={handleAddToCart}
                disabled={isOutOfStock}
                className={`w-full rounded-lg px-4 py-3 font-medium text-white transition-all duration-200 ${
                  isOutOfStock
                    ? 'cursor-not-allowed bg-gray-400'
                    : 'bg-gradient-to-r from-blue-600 to-blue-700 hover:-translate-y-0.5 hover:from-blue-700 hover:to-blue-800 hover:shadow-lg active:scale-95'
                }`}
              >
                <div className="flex items-center justify-center space-x-2">
                  <span>{isOutOfStock ? (t('outOfStock') || 'Hết hàng') : (`PayPal - ${t('addToCart') || 'Thêm vào giỏ'}`)}</span>
                </div>
              </button>
            )}
            {/* Add to Cart Button */}
            {/* PayPal Button (only show if PayPal discount available) */}
            {/* {hasPaypalDiscount && (
                <button
                  type="button"
                  onClick={handlePayPalPayment}
                  disabled={isOutOfStock}
                  className={`w-full rounded-lg px-4 py-3 font-medium text-white transition-all duration-200 ${
                    isOutOfStock
                      ? 'cursor-not-allowed bg-gray-400'
                      : 'bg-gradient-to-r from-blue-600 to-blue-700 hover:-translate-y-0.5 hover:from-blue-700 hover:to-blue-800 hover:shadow-lg active:scale-95'
                  }`}
                >
                  <div className="flex items-center justify-center space-x-2">
                    <span>💳</span>
                    <span>{isOutOfStock ? (t('outOfStock') || 'Hết hàng') : `PayPal - ${t('addToCart') || 'Thêm vào giỏ'}`}</span>
                  </div>
                </button>
              )} */}
            {/* Cryptomus Button (only show if discount available) */}
            {hasCryptomusDiscount && (
              <button
                type="button"
                onClick={handleCryptomusPayment}
                disabled={isOutOfStock}
                className={`relative w-full overflow-hidden rounded-lg px-4 py-3 font-medium transition-all duration-200 ${
                  isOutOfStock
                    ? 'cursor-not-allowed bg-gray-400 text-white'
                    : 'bg-gradient-to-r from-orange-500 to-yellow-500 text-white hover:-translate-y-0.5 hover:from-orange-600 hover:to-yellow-600 hover:shadow-lg active:scale-95'
                }`}
              >
                {/* Discount Badge */}
                <div className="absolute -right-1 -top-1 animate-pulse rounded-full bg-red-500 px-2 pt-1 text-xs font-bold text-white">
                  -
                  {cryptomusDiscountPercent}
                  %
                </div>

                {isOutOfStock
                  ? (t('outOfStock') || 'Hết hàng')
                  : (
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <span>Crypto Payment</span>
                        </div>
                        <div className="text-right">
                          <div className="flex text-xs font-bold">
                            <span>
                              {' '}
                              $
                              {cryptomusPrice.toFixed(2)}
                            </span>
                            {cryptomusUsdtConversion && (
                              <div className="text-xs text-yellow-300">
                                {' '}
                                (
                                {' '}
                                =
                                {' '}
                                {cryptomusUsdtConversion.formattedAmount}
                                )
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    )}
              </button>
            )}

            {/* View Detail Button */}
            <button
              type="button"
              onClick={handleViewDetail}
              className="w-full rounded-lg border-2 border-blue-500 bg-gradient-to-r from-blue-50 to-blue-100 px-4 py-3 font-medium text-blue-700 transition-all duration-200 hover:-translate-y-0.5 hover:from-blue-100 hover:to-blue-200 hover:shadow-lg active:scale-95"
            >
              <div className="flex items-center justify-center space-x-2">
                <span>{t('viewMore')}</span>
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* Product Detail Modal */}
      <ProductDetailModal
        product={product}
        isOpen={showDetailModal}
        onClose={handleCloseModal}
        selectedDuration={selectedDuration}
        quantity={quantity}
        onDurationChange={handleDurationChange}
        onQuantityChange={setQuantity}
        onAddToCart={handleAddToCart}
      />

      {/* Cryptomus Modal */}
      <CryptomusModal
        isOpen={showCryptomusModal}
        onClose={handleCloseCryptomusModal}
        productName={name}
        discountPercent={cryptomusDiscountPercent}
        originalPrice={displayPrice}
        discountedPrice={cryptomusPrice}
      />
    </>
  );
};

export default ProductCard;
